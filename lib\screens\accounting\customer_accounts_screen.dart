import 'package:flutter/material.dart';
import '../../services/unified_invoice_service.dart';
import 'customer_account_details_screen.dart';
import '../../services/currency_service.dart';
import '../../l10n/app_localizations.dart';

class CustomerAccountsScreen extends StatefulWidget {
  const CustomerAccountsScreen({super.key});

  @override
  State<CustomerAccountsScreen> createState() => _CustomerAccountsScreenState();
}

class _CustomerAccountsScreenState extends State<CustomerAccountsScreen> {
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  List<Map<String, dynamic>> _customerAccounts = [];
  List<Map<String, dynamic>> _filteredAccounts = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  String _sortBy = 'name'; // 'name', 'balance', 'sales'

  @override
  void initState() {
    super.initState();
    _loadCustomerAccounts();
    _searchController.addListener(_filterAccounts);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload data when returning to this screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCustomerAccounts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomerAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await _database.getAllCustomerAccounts();
      setState(() {
        _customerAccounts = accounts;
        _filteredAccounts = accounts;
        _isLoading = false;
      });
      _sortAccounts();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل حسابات العملاء: $e');
      }
    }
  }

  void _filterAccounts() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredAccounts = _customerAccounts;
      } else {
        _filteredAccounts = _customerAccounts.where((account) {
          final customerName = (account['customerName']?.toString() ?? account['name']?.toString() ?? '').toLowerCase();
          return customerName.contains(query);
        }).toList();
      }
    });
    _sortAccounts();
  }

  void _sortAccounts() {
    setState(() {
      _filteredAccounts.sort((a, b) {
        switch (_sortBy) {
          case 'balance':
            final balanceA = (a['balance'] as num?)?.toDouble() ?? 0.0;
            final balanceB = (b['balance'] as num?)?.toDouble() ?? 0.0;
            return balanceB.compareTo(balanceA);
          case 'sales':
            final salesA = (a['totalSales'] as num?)?.toDouble() ?? 0.0;
            final salesB = (b['totalSales'] as num?)?.toDouble() ?? 0.0;
            return salesB.compareTo(salesA);
          case 'name':
          default:
            final nameA = a['customerName']?.toString() ?? a['name']?.toString() ?? '';
            final nameB = b['customerName']?.toString() ?? b['name']?.toString() ?? '';
            return nameA.compareTo(nameB);
        }
      });
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showCustomerDetails(Map<String, dynamic> account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerAccountDetailsScreen(
          customerName: account['customerName'] as String,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('حسابات العملاء'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
              _sortAccounts();
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'name',
                child: Row(
                  children: [
                    Icon(Icons.sort_by_alpha),
                    SizedBox(width: 8),
                    Text('ترتيب بالاسم'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'balance',
                child: Row(
                  children: [
                    Icon(Icons.account_balance_wallet),
                    SizedBox(width: 8),
                    Text('ترتيب بالرصيد'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sales',
                child: Row(
                  children: [
                    Icon(Icons.trending_up),
                    SizedBox(width: 8),
                    Text('ترتيب بالمبيعات'),
                  ],
                ),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCustomerAccounts,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE8F5E8),
              Color(0xFFE3F2FD),
            ],
          ),
        ),
        child: Column(
          children: [
            // Search Bar
            Padding(
              padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'البحث عن عميل...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
            ),

            // Summary Cards
            if (!_isLoading) _buildSummaryCards(isTablet),

            // Accounts List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredAccounts.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.people_outline, size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text('لا توجد حسابات عملاء'),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
                          itemCount: _filteredAccounts.length,
                          itemBuilder: (context, index) {
                            final account = _filteredAccounts[index];
                            return _buildAccountCard(account, isTablet);
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(bool isTablet) {
    final totalCustomers = _customerAccounts.length;
    final totalSales = _customerAccounts.fold<double>(
      0.0,
      (sum, account) => sum + (account['totalSales'] as double),
    );

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: isTablet ? 16.0 : 12.0),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي العملاء',
              totalCustomers.toString(),
              Icons.people,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: FutureBuilder<String>(
              future: CurrencyService.instance.formatAmount(totalSales),
              builder: (context, snapshot) {
                return _buildSummaryCard(
                  'إجمالي المبيعات',
                  snapshot.data ?? CurrencyService.instance.formatForPdf(totalSales),
                  Icons.trending_up,
                  Colors.green,
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: FutureBuilder<Map<String, double>>(
              future: _database.getCustomerPaymentStatistics(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final stats = snapshot.data!;
                  final outstandingAmount = stats['outstandingAmount'] ?? 0.0;

                  return FutureBuilder<String>(
                    future: CurrencyService.instance.formatAmount(outstandingAmount),
                    builder: (context, amountSnapshot) {
                      final localizations = AppLocalizations.of(context);
                      return _buildSummaryCard(
                        localizations?.outstandingFromCustomers ?? 'المبلغ المستحق من العملاء',
                        amountSnapshot.data ?? CurrencyService.instance.formatForPdf(outstandingAmount),
                        Icons.account_balance,
                        outstandingAmount >= 0 ? Colors.purple : Colors.red,
                      );
                    },
                  );
                }
                final localizations = AppLocalizations.of(context);
                return _buildSummaryCard(
                  localizations?.outstandingFromCustomers ?? 'المبلغ المستحق من العملاء',
                  '...',
                  Icons.account_balance,
                  Colors.purple,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountCard(Map<String, dynamic> account, bool isTablet) {
    final customerName = account['customerName']?.toString() ?? account['name']?.toString() ?? '';
    final balance = (account['balance'] as num?)?.toDouble() ?? 0.0;
    final totalSales = (account['totalSales'] as num?)?.toDouble() ?? 0.0;
    final totalReturns = (account['totalReturns'] as num?)?.toDouble() ?? 0.0;
    final totalTransactions = (account['totalTransactions'] as num?)?.toInt() ?? 0;

    final balanceColor = balance > 0 ? Colors.green : balance < 0 ? Colors.red : Colors.grey;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showCustomerDetails(account),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    child: Text(
                      customerName.isNotEmpty ? customerName[0].toUpperCase() : 'ع',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          customerName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '$totalTransactions معاملة',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      FutureBuilder<String>(
                        future: CurrencyService.instance.formatAmount(balance),
                        builder: (context, snapshot) {
                          return Text(
                            snapshot.data ?? CurrencyService.instance.formatForPdf(balance),
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: balanceColor,
                            ),
                          );
                        },
                      ),
                      const Text(
                        'الرصيد',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: FutureBuilder<String>(
                      future: CurrencyService.instance.formatAmount(totalSales),
                      builder: (context, snapshot) {
                        return _buildAccountDetail(
                          'المبيعات',
                          snapshot.data ?? CurrencyService.instance.formatForPdf(totalSales),
                          Colors.blue,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: FutureBuilder<String>(
                      future: CurrencyService.instance.formatAmount(totalReturns),
                      builder: (context, snapshot) {
                        return _buildAccountDetail(
                          'المرتجعات',
                          snapshot.data ?? CurrencyService.instance.formatForPdf(totalReturns),
                          Colors.orange,
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: FutureBuilder<String>(
                      future: CurrencyService.instance.formatAmount(account['totalPayments'] ?? 0.0),
                      builder: (context, snapshot) {
                        return _buildAccountDetail(
                          'المدفوعات',
                          snapshot.data ?? CurrencyService.instance.formatForPdf(account['totalPayments'] ?? 0.0),
                          Colors.green,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: FutureBuilder<String>(
                      future: CurrencyService.instance.formatAmount(account['outstandingAmount'] ?? 0.0),
                      builder: (context, snapshot) {
                        final outstandingAmount = account['outstandingAmount'] ?? 0.0;
                        return _buildAccountDetail(
                          'المتبقي',
                          snapshot.data ?? CurrencyService.instance.formatForPdf(outstandingAmount),
                          outstandingAmount >= 0 ? Colors.purple : Colors.red,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountDetail(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}


