import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../models/invoice.dart';
import 'unified_database_service.dart';

/// خدمة الفواتير الموحدة - تستخدم قاعدة البيانات الموحدة بدلاً من InvoiceDatabase
class UnifiedInvoiceService {
  static final UnifiedInvoiceService _instance = UnifiedInvoiceService._internal();
  factory UnifiedInvoiceService() => _instance;
  UnifiedInvoiceService._internal();

  // Constructor عادي للاستخدام المباشر
  static UnifiedInvoiceService get instance => _instance;

  final UnifiedDatabaseService _unifiedDb = UnifiedDatabaseService();

  // Table names
  static const String invoicesTable = 'invoices';
  static const String invoiceItemsTable = 'invoice_items';
  static const String customersTable = 'customers';
  static const String suppliersTable = 'suppliers';

  /// الحصول على قاعدة البيانات
  Future<Database> get database async => await _unifiedDb.database;

  /// إدراج فاتورة جديدة
  Future<int> insertInvoice(Map<String, dynamic> invoice) async {
    return await _unifiedDb.insertInvoice(invoice);
  }

  /// إدراج عنصر فاتورة
  Future<int> insertInvoiceItem(Map<String, dynamic> item) async {
    return await _unifiedDb.insertInvoiceItem(item);
  }

  /// إدراج عميل
  Future<int> insertCustomer(Map<String, dynamic> customer) async {
    return await _unifiedDb.insertCustomer(customer);
  }

  /// إدراج مورد
  Future<int> insertSupplier(Map<String, dynamic> supplier) async {
    return await _unifiedDb.insertSupplier(supplier);
  }

  /// الحصول على جميع الفواتير
  Future<List<Map<String, dynamic>>> getAllInvoices() async {
    return await _unifiedDb.getAllInvoices();
  }

  /// الحصول على جميع العملاء
  Future<List<Map<String, dynamic>>> getAllCustomers() async {
    return await _unifiedDb.getAllCustomers();
  }

  /// الحصول على جميع الموردين
  Future<List<Map<String, dynamic>>> getAllSuppliers() async {
    return await _unifiedDb.getAllSuppliers();
  }

  /// الحصول على فاتورة بالمعرف
  Future<Map<String, dynamic>?> getInvoiceById(int id) async {
    final db = await database;
    final result = await db.query(
      invoicesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// الحصول على عناصر فاتورة
  Future<List<Map<String, dynamic>>> getInvoiceItems(int invoiceId) async {
    final db = await database;
    return await db.query(
      invoiceItemsTable,
      where: 'invoiceId = ?',
      whereArgs: [invoiceId],
    );
  }

  /// تحديث فاتورة
  Future<int> updateInvoice(int id, Map<String, dynamic> invoice) async {
    final db = await database;
    invoice['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      invoicesTable,
      invoice,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف فاتورة
  Future<int> deleteInvoice(int id) async {
    final db = await database;
    return await db.delete(
      invoicesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// البحث في الفواتير
  Future<List<Map<String, dynamic>>> searchInvoices(String query) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'invoiceNumber LIKE ? OR supplierOrCustomerName LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على فواتير حسب النوع
  Future<List<Map<String, dynamic>>> getInvoicesByType(String type) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على فواتير حسب العميل/المورد
  Future<List<Map<String, dynamic>>> getInvoicesByEntity(String entityName) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'supplierOrCustomerName = ?',
      whereArgs: [entityName],
      orderBy: 'date DESC',
    );
  }

  /// حساب إجمالي المبيعات
  Future<double> getTotalSales() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['sale']
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// حساب إجمالي المشتريات
  Future<double> getTotalPurchases() async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['purchase']
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  /// الحصول على أسماء العملاء من الفواتير
  Future<List<String>> getCustomerNamesFromInvoices() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT DISTINCT supplierOrCustomerName
      FROM $invoicesTable
      WHERE type IN (?, ?)
      ORDER BY supplierOrCustomerName
    ''', ['sale', 'sale_return']);

    return result.map((row) => row['supplierOrCustomerName'] as String).toList();
  }

  /// الحصول على أسماء الموردين من الفواتير
  Future<List<String>> getSupplierNamesFromInvoices() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT DISTINCT supplierOrCustomerName
      FROM $invoicesTable
      WHERE type IN (?, ?)
      ORDER BY supplierOrCustomerName
    ''', ['purchase', 'purchase_return']);

    return result.map((row) => row['supplierOrCustomerName'] as String).toList();
  }

  /// الحصول على جميع أسماء الكيانات (عملاء وموردين)
  Future<List<String>> getAllEntityNames() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT DISTINCT supplierOrCustomerName
      FROM $invoicesTable
      ORDER BY supplierOrCustomerName
    ''');

    return result.map((row) => row['supplierOrCustomerName'] as String).toList();
  }

  /// الحصول على إحصائيات العميل
  Future<Map<String, dynamic>> getCustomerAccountSummary(String customerName) async {
    final db = await database;
    
    // إجمالي المبيعات
    final salesResult = await db.rawQuery('''
      SELECT SUM(totalAmount) as total, COUNT(*) as count
      FROM $invoicesTable
      WHERE supplierOrCustomerName = ? AND type = ?
    ''', [customerName, 'sale']);
    
    // إجمالي المرتجعات
    final returnsResult = await db.rawQuery('''
      SELECT SUM(totalAmount) as total, COUNT(*) as count
      FROM $invoicesTable
      WHERE supplierOrCustomerName = ? AND type = ?
    ''', [customerName, 'sale_return']);
    
    final totalSales = (salesResult.first['total'] as double?) ?? 0.0;
    final totalReturns = (returnsResult.first['total'] as double?) ?? 0.0;
    final salesCount = (salesResult.first['count'] as int?) ?? 0;
    final returnsCount = (returnsResult.first['count'] as int?) ?? 0;
    
    return {
      'customerName': customerName,
      'totalSales': totalSales,
      'totalReturns': totalReturns,
      'balance': totalSales - totalReturns,
      'salesCount': salesCount,
      'returnsCount': returnsCount,
      'totalTransactions': salesCount + returnsCount,
    };
  }

  /// الحصول على إحصائيات المورد
  Future<Map<String, dynamic>> getSupplierAccountSummary(String supplierName) async {
    final db = await database;
    
    // إجمالي المشتريات
    final purchasesResult = await db.rawQuery('''
      SELECT SUM(totalAmount) as total, COUNT(*) as count
      FROM $invoicesTable
      WHERE supplierOrCustomerName = ? AND type = ?
    ''', [supplierName, 'purchase']);
    
    // إجمالي المرتجعات
    final returnsResult = await db.rawQuery('''
      SELECT SUM(totalAmount) as total, COUNT(*) as count
      FROM $invoicesTable
      WHERE supplierOrCustomerName = ? AND type = ?
    ''', [supplierName, 'purchase_return']);
    
    final totalPurchases = (purchasesResult.first['total'] as double?) ?? 0.0;
    final totalReturns = (returnsResult.first['total'] as double?) ?? 0.0;
    final purchasesCount = (purchasesResult.first['count'] as int?) ?? 0;
    final returnsCount = (returnsResult.first['count'] as int?) ?? 0;
    
    return {
      'supplierName': supplierName,
      'totalPurchases': totalPurchases,
      'totalReturns': totalReturns,
      'balance': totalPurchases - totalReturns,
      'purchasesCount': purchasesCount,
      'returnsCount': returnsCount,
      'totalTransactions': purchasesCount + returnsCount,
    };
  }

  /// الحصول على إحصائيات الفواتير
  Future<Map<String, double>> getInvoiceStatistics() async {
    final db = await database;

    // إجمالي المبيعات
    final salesResult = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['sale']
    );
    final sales = (salesResult.first['total'] as double?) ?? 0.0;

    // إجمالي المشتريات
    final purchasesResult = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['purchase']
    );
    final purchases = (purchasesResult.first['total'] as double?) ?? 0.0;

    // إجمالي مرتجع المبيعات
    final saleReturnsResult = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['sale_return']
    );
    final saleReturns = (saleReturnsResult.first['total'] as double?) ?? 0.0;

    // إجمالي مرتجع المشتريات
    final purchaseReturnsResult = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM $invoicesTable WHERE type = ?',
      ['purchase_return']
    );
    final purchaseReturns = (purchaseReturnsResult.first['total'] as double?) ?? 0.0;

    // حساب صافي الربح/الخسارة
    final netProfitLoss = (sales - saleReturns) - (purchases - purchaseReturns);

    return {
      'sales': sales,
      'purchases': purchases,
      'sale_returns': saleReturns,
      'purchase_returns': purchaseReturns,
      'net_profit_loss': netProfitLoss,
    };
  }

  /// إضافة بيانات تجريبية
  Future<void> addSampleData() async {
    // سيتم استخدام البيانات الموجودة في قاعدة البيانات الموحدة
    // التي تم نقلها من قاعدة البيانات القديمة
  }

  /// إنشاء بيانات تجريبية
  Future<void> createSampleData() async {
    // سيتم استخدام البيانات الموجودة في قاعدة البيانات الموحدة
    // التي تم نقلها من قاعدة البيانات القديمة
  }

  // ===== طرق إضافية مطلوبة للتوافق مع الملفات الموجودة =====

  /// توليد رقم فاتورة
  Future<String> generateInvoiceNumber(String type) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $invoicesTable WHERE type = ?',
      [type]
    );
    final count = (result.first['count'] as int?) ?? 0;
    final prefix = type == 'sale' ? 'S' : 'P';
    return '$prefix${(count + 1).toString().padLeft(6, '0')}';
  }

  /// إضافة بيانات تجريبية بالقوة
  Future<void> forceAddSampleData() async {
    // لا حاجة لإضافة بيانات تجريبية - البيانات موجودة بالفعل
  }

  /// وضع علامة على الفاتورة كمدفوعة
  Future<void> markInvoiceAsPaid(int invoiceId, [int? transactionId]) async {
    final db = await database;
    final updateData = {
      'isPaid': 1,
      'updated_at': DateTime.now().millisecondsSinceEpoch
    };
    if (transactionId != null) {
      updateData['treasuryTransactionId'] = transactionId;
    }
    await db.update(
      invoicesTable,
      updateData,
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// الحصول على الفواتير حسب النوع
  Future<List<Invoice>> getInvoices({String? type}) async {
    final db = await database;
    List<Map<String, dynamic>> result;

    if (type != null) {
      result = await db.query(
        invoicesTable,
        where: 'type = ?',
        whereArgs: [type],
        orderBy: 'date DESC',
      );
    } else {
      result = await db.query(invoicesTable, orderBy: 'date DESC');
    }

    return result.map((map) => Invoice.fromMap(map)).toList();
  }

  /// الحصول على إحصائيات المبيعات
  Future<Map<String, double>> getSalesStatistics() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT
        SUM(CASE WHEN type = 'sale' THEN totalAmount ELSE 0 END) as sales,
        SUM(CASE WHEN type = 'sale_return' THEN totalAmount ELSE 0 END) as returns,
        COUNT(CASE WHEN type = 'sale' THEN 1 END) as salesCount,
        COUNT(CASE WHEN type = 'sale_return' THEN 1 END) as returnsCount
      FROM $invoicesTable
    ''');

    final row = result.first;
    return {
      'sales': (row['sales'] as double?) ?? 0.0,
      'returns': (row['returns'] as double?) ?? 0.0,
      'salesCount': ((row['salesCount'] as int?) ?? 0).toDouble(),
      'returnsCount': ((row['returnsCount'] as int?) ?? 0).toDouble(),
    };
  }

  /// الحصول على إحصائيات المشتريات
  Future<Map<String, double>> getPurchaseStatistics() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT
        SUM(CASE WHEN type = 'purchase' THEN totalAmount ELSE 0 END) as purchases,
        SUM(CASE WHEN type = 'purchase_return' THEN totalAmount ELSE 0 END) as returns,
        COUNT(CASE WHEN type = 'purchase' THEN 1 END) as purchasesCount,
        COUNT(CASE WHEN type = 'purchase_return' THEN 1 END) as returnsCount
      FROM $invoicesTable
    ''');

    final row = result.first;
    return {
      'purchases': (row['purchases'] as double?) ?? 0.0,
      'returns': (row['returns'] as double?) ?? 0.0,
      'purchasesCount': ((row['purchasesCount'] as int?) ?? 0).toDouble(),
      'returnsCount': ((row['returnsCount'] as int?) ?? 0).toDouble(),
    };
  }

  /// الحصول على الفواتير حسب نطاق التاريخ
  Future<List<Map<String, dynamic>>> getInvoicesByDateRange(
    DateTime startDate, DateTime endDate, String type) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'type = ? AND date >= ? AND date <= ?',
      whereArgs: [type, startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch],
      orderBy: 'date DESC',
    );
  }

  /// تحديث عميل
  Future<void> updateCustomer(Map<String, dynamic> customer) async {
    final db = await database;
    await db.update(
      customersTable,
      customer,
      where: 'id = ?',
      whereArgs: [customer['id']],
    );
  }

  /// حذف عميل
  Future<void> deleteCustomer(int id) async {
    final db = await database;
    await db.delete(
      customersTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// تحديث مورد
  Future<void> updateSupplier(Map<String, dynamic> supplier) async {
    final db = await database;
    await db.update(
      suppliersTable,
      supplier,
      where: 'id = ?',
      whereArgs: [supplier['id']],
    );
  }

  /// حذف مورد
  Future<void> deleteSupplier(int id) async {
    final db = await database;
    await db.delete(
      suppliersTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على جميع حسابات العملاء
  Future<List<Map<String, dynamic>>> getAllCustomerAccounts() async {
    final customerNames = await getCustomerNamesFromInvoices();
    final accounts = <Map<String, dynamic>>[];

    for (final name in customerNames) {
      final summary = await getCustomerAccountSummary(name);
      accounts.add(summary);
    }

    return accounts;
  }

  /// الحصول على جميع حسابات الموردين
  Future<List<Map<String, dynamic>>> getAllSupplierAccounts() async {
    final supplierNames = await getSupplierNamesFromInvoices();
    final accounts = <Map<String, dynamic>>[];

    for (final name in supplierNames) {
      final summary = await getSupplierAccountSummary(name);
      accounts.add(summary);
    }

    return accounts;
  }

  /// ربط فاتورة بالخزينة
  Future<void> linkInvoiceToTreasury(int invoiceId, int treasuryId) async {
    final db = await database;
    await db.update(
      invoicesTable,
      {
        'treasuryId': treasuryId,
        'updated_at': DateTime.now().millisecondsSinceEpoch
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// وضع علامة على الفاتورة كغير مدفوعة
  Future<void> markInvoiceAsUnpaid(int invoiceId) async {
    final db = await database;
    await db.update(
      invoicesTable,
      {
        'isPaid': 0,
        'treasuryTransactionId': null,
        'updated_at': DateTime.now().millisecondsSinceEpoch
      },
      where: 'id = ?',
      whereArgs: [invoiceId],
    );
  }

  /// الحصول على الفواتير غير المدفوعة
  Future<List<Invoice>> getUnpaidInvoices({String? type}) async {
    final db = await database;
    List<Map<String, dynamic>> result;

    if (type != null) {
      result = await db.query(
        invoicesTable,
        where: 'isPaid = 0 AND type = ?',
        whereArgs: [type],
        orderBy: 'date DESC',
      );
    } else {
      result = await db.query(
        invoicesTable,
        where: 'isPaid = 0',
        orderBy: 'date DESC',
      );
    }

    return result.map((map) => Invoice.fromMap(map)).toList();
  }

  /// الحصول على الفواتير المرتبطة بخزينة معينة
  Future<List<Invoice>> getInvoicesByTreasury(int treasuryId) async {
    final db = await database;
    final result = await db.query(
      invoicesTable,
      where: 'treasuryId = ?',
      whereArgs: [treasuryId],
      orderBy: 'date DESC',
    );

    return result.map((map) => Invoice.fromMap(map)).toList();
  }

  /// الحصول على إحصائيات دفعات العملاء
  Future<Map<String, double>> getCustomerPaymentStatistics() async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
    return {
      'totalPayments': 0.0,
      'totalReceivables': 0.0,
      'totalOverdue': 0.0,
    };
  }

  /// الحصول على إحصائيات دفعات الموردين
  Future<Map<String, double>> getSupplierPaymentStatistics() async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
    return {
      'totalPayments': 0.0,
      'totalPayables': 0.0,
      'totalOverdue': 0.0,
    };
  }

  /// الحصول على تفاصيل حساب العميل
  Future<Map<String, dynamic>?> getCustomerAccountDetails(String customerName) async {
    return await getCustomerAccountSummary(customerName);
  }

  /// الحصول على تفاصيل حساب المورد
  Future<Map<String, dynamic>?> getSupplierAccountDetails(String supplierName) async {
    return await getSupplierAccountSummary(supplierName);
  }

  /// الحصول على فواتير العميل
  Future<List<Map<String, dynamic>>> getInvoicesByCustomerName(String customerName) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'supplierOrCustomerName = ? AND type IN (?, ?)',
      whereArgs: [customerName, 'sale', 'sale_return'],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على فواتير المورد
  Future<List<Map<String, dynamic>>> getInvoicesBySupplierName(String supplierName) async {
    final db = await database;
    return await db.query(
      invoicesTable,
      where: 'supplierOrCustomerName = ? AND type IN (?, ?)',
      whereArgs: [supplierName, 'purchase', 'purchase_return'],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على دفعات العميل
  Future<List<Map<String, dynamic>>> getCustomerPayments(String customerName) async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
    return [];
  }

  /// الحصول على دفعات المورد
  Future<List<Map<String, dynamic>>> getSupplierPayments(String supplierName) async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
    return [];
  }

  /// إضافة دفعة عميل
  Future<void> addCustomerPayment(String customerName, double amount, String notes, [DateTime? date]) async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
  }

  /// إضافة دفعة مورد
  Future<void> addSupplierPayment(String supplierName, double amount, String notes, [DateTime? date]) async {
    // مؤقتاً - سيتم تنفيذها لاحقاً
  }
}
