import 'aluminum_profile.dart';

class ProfileSeries {
  final int? id;
  final String name; // اسم المجموعة مثل "سوناتا 45"
  final String code; // كود المجموعة مثل "SON45"
  final ProfileType type; // مفصلي أو سحاب
  final String description;
  final String? imagePath;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProfileSeries({
    this.id,
    required this.name,
    required this.code,
    required this.type,
    this.description = '',
    this.imagePath,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'type': type.key,
      'description': description,
      'image_path': imagePath,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory ProfileSeries.fromMap(Map<String, dynamic> map) {
    // التعامل مع البيانات من جدول القطاعات حيث series_id يحتوي على معرف المجموعة
    final seriesId = map['series_id'] ?? map['id'];
    final seriesName = _convertToString(map['name']) ??
                      _convertToString(seriesId) ?? '';

    return ProfileSeries(
      id: seriesId?.toInt() ?? seriesName.hashCode,
      name: seriesName,
      code: _convertToString(map['code']) ?? seriesName,
      type: ProfileType.values.firstWhere(
        (t) => t.key == _convertToString(map['type']),
        orElse: () => ProfileType.hinge,
      ),
      description: _convertToString(map['description']) ?? '',
      imagePath: _convertToString(map['image_path']),
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? DateTime.now().millisecondsSinceEpoch),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] ?? DateTime.now().millisecondsSinceEpoch),
    );
  }

  /// تحويل القيمة إلى String مع التعامل مع Blob
  static String? _convertToString(dynamic value) {
    if (value == null) return null;
    if (value is String) return value;
    if (value is List<int>) {
      // تحويل Blob إلى String
      try {
        return String.fromCharCodes(value);
      } catch (e) {
        return null;
      }
    }
    return value.toString();
  }

  ProfileSeries copyWith({
    int? id,
    String? name,
    String? code,
    ProfileType? type,
    String? description,
    String? imagePath,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProfileSeries(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      type: type ?? this.type,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'ProfileSeries{id: $id, name: $name, code: $code, type: $type}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileSeries &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        code.hashCode ^
        type.hashCode;
  }
}
