import 'package:flutter/foundation.dart';
import '../models/invoice.dart';
import '../models/treasury_transaction.dart';
import '../services/unified_invoice_service.dart';
import '../services/unified_treasury_service.dart';

/// خدمة ربط الفواتير بالخزينة
class InvoiceTreasuryService {
  final UnifiedInvoiceService _invoiceDatabase = UnifiedInvoiceService();
  final UnifiedTreasuryService _treasuryDatabase = UnifiedTreasuryService();

  /// ربط فاتورة بالخزينة وإنشاء معاملة مالية
  Future<int?> linkInvoiceToTreasury({
    required Invoice invoice,
    required int treasuryId,
    bool autoCreateTransaction = true,
  }) async {
    try {
      // ربط الفاتورة بالخزينة
      await _invoiceDatabase.linkInvoiceToTreasury(invoice.id!, treasuryId);

      if (autoCreateTransaction) {
        // إنشاء معاملة مالية في الخزينة
        final transactionId = await _createTreasuryTransactionFromInvoice(
          invoice: invoice,
          treasuryId: treasuryId,
        );

        // تحديث الفاتورة بمعرف المعاملة
        if (transactionId != null) {
          await _invoiceDatabase.markInvoiceAsPaid(invoice.id!, transactionId);
        }

        return transactionId;
      }

      return null;
    } catch (e) {
      debugPrint('Error linking invoice to treasury: $e');
      return null;
    }
  }

  /// إنشاء معاملة مالية في الخزينة من الفاتورة
  Future<int?> _createTreasuryTransactionFromInvoice({
    required Invoice invoice,
    required int treasuryId,
  }) async {
    try {
      double income = 0.0;
      double expenses = 0.0;
      String description = '';

      // تحديد نوع المعاملة حسب نوع الفاتورة
      switch (invoice.type) {
        case 'sale':
          income = invoice.netAmount;
          description = 'دخل من فاتورة مبيعات رقم ${invoice.invoiceNumber}';
          break;
        case 'purchase':
          expenses = invoice.netAmount;
          description = 'مصروف فاتورة مشتريات رقم ${invoice.invoiceNumber}';
          break;
        case 'sale_return':
          expenses = invoice.netAmount;
          description = 'مصروف مرتجع مبيعات رقم ${invoice.invoiceNumber}';
          break;
        case 'purchase_return':
          income = invoice.netAmount;
          description = 'دخل من مرتجع مشتريات رقم ${invoice.invoiceNumber}';
          break;
        default:
          throw Exception('نوع فاتورة غير مدعوم: ${invoice.type}');
      }

      final transaction = TreasuryTransaction(
        treasuryId: treasuryId,
        description: description,
        income: income,
        expenses: expenses,
        notes: 'تم إنشاؤها تلقائياً من الفاتورة - ${invoice.supplierOrCustomerName}',
        date: invoice.date,
        invoiceId: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
      );

      return await _treasuryDatabase.insertTreasuryTransaction(transaction.toMap());
    } catch (e) {
      debugPrint('Error creating treasury transaction from invoice: $e');
      return null;
    }
  }

  /// دفع فاتورة (إنشاء معاملة مالية)
  Future<bool> payInvoice({
    required Invoice invoice,
    required int treasuryId,
  }) async {
    try {
      final transactionId = await linkInvoiceToTreasury(
        invoice: invoice,
        treasuryId: treasuryId,
        autoCreateTransaction: true,
      );

      return transactionId != null;
    } catch (e) {
      debugPrint('Error paying invoice: $e');
      return false;
    }
  }

  /// إلغاء دفع فاتورة
  Future<bool> unpayInvoice(Invoice invoice) async {
    try {
      // حذف المعاملة المالية إذا كانت موجودة
      if (invoice.treasuryTransactionId != null) {
        await _treasuryDatabase.deleteTreasuryTransaction(
          invoice.treasuryTransactionId!,
        );
      }

      // تحديث حالة الفاتورة
      await _invoiceDatabase.markInvoiceAsUnpaid(invoice.id!);

      return true;
    } catch (e) {
      debugPrint('Error unpaying invoice: $e');
      return false;
    }
  }

  /// الحصول على الفواتير غير المدفوعة
  Future<List<Invoice>> getUnpaidInvoices({String? type}) async {
    return await _invoiceDatabase.getUnpaidInvoices(type: type);
  }

  /// الحصول على الفواتير المرتبطة بخزينة معينة
  Future<List<Invoice>> getInvoicesByTreasury(int treasuryId) async {
    return await _invoiceDatabase.getInvoicesByTreasury(treasuryId);
  }

  /// الحصول على إجمالي الفواتير غير المدفوعة
  Future<Map<String, double>> getUnpaidInvoicesTotals() async {
    try {
      final unpaidSales = await getUnpaidInvoices(type: 'sale');
      final unpaidPurchases = await getUnpaidInvoices(type: 'purchase');
      final unpaidSaleReturns = await getUnpaidInvoices(type: 'sale_return');
      final unpaidPurchaseReturns = await getUnpaidInvoices(type: 'purchase_return');

      double totalUnpaidSales = unpaidSales.fold(0.0, (sum, invoice) => sum + invoice.netAmount);
      double totalUnpaidPurchases = unpaidPurchases.fold(0.0, (sum, invoice) => sum + invoice.netAmount);
      double totalUnpaidSaleReturns = unpaidSaleReturns.fold(0.0, (sum, invoice) => sum + invoice.netAmount);
      double totalUnpaidPurchaseReturns = unpaidPurchaseReturns.fold(0.0, (sum, invoice) => sum + invoice.netAmount);

      return {
        'unpaidSales': totalUnpaidSales,
        'unpaidPurchases': totalUnpaidPurchases,
        'unpaidSaleReturns': totalUnpaidSaleReturns,
        'unpaidPurchaseReturns': totalUnpaidPurchaseReturns,
        'totalReceivables': totalUnpaidSales - totalUnpaidSaleReturns, // المستحقات
        'totalPayables': totalUnpaidPurchases - totalUnpaidPurchaseReturns, // المدفوعات المستحقة
      };
    } catch (e) {
      debugPrint('Error getting unpaid invoices totals: $e');
      return {
        'unpaidSales': 0.0,
        'unpaidPurchases': 0.0,
        'unpaidSaleReturns': 0.0,
        'unpaidPurchaseReturns': 0.0,
        'totalReceivables': 0.0,
        'totalPayables': 0.0,
      };
    }
  }

  /// دفع عدة فواتير دفعة واحدة
  Future<List<int>> payMultipleInvoices({
    required List<Invoice> invoices,
    required int treasuryId,
  }) async {
    List<int> successfulTransactions = [];

    for (final invoice in invoices) {
      final transactionId = await linkInvoiceToTreasury(
        invoice: invoice,
        treasuryId: treasuryId,
        autoCreateTransaction: true,
      );

      if (transactionId != null) {
        successfulTransactions.add(transactionId);
      }
    }

    return successfulTransactions;
  }

  /// التحقق من إمكانية ربط فاتورة بخزينة
  Future<bool> canLinkInvoiceToTreasury(Invoice invoice, int treasuryId) async {
    try {
      // التحقق من وجود الخزينة
      final treasury = await _treasuryDatabase.getTreasuryById(treasuryId);
      if (treasury == null) {
        return false;
      }

      // التحقق من أن الفاتورة غير مدفوعة
      if (invoice.isPaid) {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking if invoice can be linked to treasury: $e');
      return false;
    }
  }
}
