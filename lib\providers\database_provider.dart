import 'package:flutter/material.dart';
import '../services/unified_treasury_service.dart';

class DatabaseProvider extends InheritedWidget {
  final UnifiedTreasuryService treasuryDatabase;

  const DatabaseProvider({
    super.key,
    required this.treasuryDatabase,
    required super.child,
  });

  static UnifiedTreasuryService of(BuildContext context) {
    final provider = context.dependOnInheritedWidgetOfExactType<DatabaseProvider>();
    if (provider == null) {
      throw Exception('No DatabaseProvider found in context');
    }
    return provider.treasuryDatabase;
  }

  @override
  bool updateShouldNotify(DatabaseProvider oldWidget) {
    return treasuryDatabase != oldWidget.treasuryDatabase;
  }
}
