import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'screens/splash_screen.dart';
import 'screens/license_activation_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/cutting/cutting_projects_screen.dart';
import 'screens/treasury/treasury_screen.dart';
import 'screens/treasury/treasury_summary_screen.dart';
import 'screens/accounting/accounting_invoices_screen.dart';
import 'screens/ai_tools_screen.dart';
import 'screens/design_services_screen.dart';
// import 'screens/tasks/tasks_screen.dart'; // تم حذفه
import 'screens/aluminum/aluminum_settings_screen.dart' as aluminum;
// import 'screens/upvc/upvc_settings_screen.dart' as upvc;
import 'l10n/app_localizations.dart';
import 'providers/database_provider.dart';
import 'services/unified_treasury_service.dart';
import 'services/currency_service.dart';
import 'services/license_service.dart';
import 'services/device_info_service.dart';
// import 'services/task_service.dart'; // تم تعطيل خدمة المهام مؤقتاً

// متغير عام لحفظ نتيجة فحص الترخيص
Map<String, dynamic>? _licenseCheckResult;

// دالة للحصول على نتيجة فحص الترخيص
Map<String, dynamic>? getLicenseCheckResult() {
  return _licenseCheckResult;
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // جلب وطباعة معلومات الجهاز عند بدء التطبيق
  await _printDeviceSerial();

  // Initialize currency service
  await CurrencyService.instance.initCache();

  // فحص رخصة البرنامج وجلب قيمة is_dev
  await _checkLicense();

  final treasuryDatabase = UnifiedTreasuryService();
  runApp(
    DatabaseProvider(
      treasuryDatabase: treasuryDatabase,
      child: const SmartAssistantApp(),
    ),
  );
}

/// جلب وطباعة سيريال الجهاز عند بدء التطبيق
Future<void> _printDeviceSerial() async {
  try {
    debugPrint('🚀 بدء جلب معلومات الجهاز وطباعة السيريال...');

    final result = await DeviceInfoService.getDeviceInfo();

    if (result['success'] == true) {
      debugPrint('✅ تم جلب معلومات الجهاز بنجاح');

      // طباعة معلومات الجهاز بشكل مفصل
      DeviceInfoService.printDeviceInfo();

    } else {
      debugPrint('❌ فشل جلب معلومات الجهاز: ${result['error']}');
      debugPrint('🌐 المنصة الحالية: ${DeviceInfoService.devicePlatform}');
    }
  } catch (e) {
    debugPrint('💥 خطأ غير متوقع أثناء جلب معلومات الجهاز: $e');
    debugPrint('🌐 المنصة الحالية: ${DeviceInfoService.devicePlatform}');
  }
}

/// فحص رخصة البرنامج عند بدء التطبيق
Future<void> _checkLicense() async {
  try {
    debugPrint('🚀 بدء فحص رخصة البرنامج وجلب بيانات الاشتراك...');

    final result = await LicenseService.checkLicense();

    // حفظ نتيجة فحص الترخيص
    _licenseCheckResult = result;

    if (result['success'] == true) {
      debugPrint('✅ تم فحص الرخصة وجلب بيانات الاشتراك بنجاح');
      debugPrint('📊 قيمة is_dev: ${result['is_dev']}');
      debugPrint('🔢 رقم التسلسل من قاعدة البيانات: ${result['serial']}');
      debugPrint('🔍 سيريال الجهاز الحالي: ${result['current_device_serial']}');
      debugPrint(result['is_current_device'] == true ? '✅ الجهاز مسجل ومطابق' : '⚠️ الجهاز غير مسجل أو غير مطابق');
      debugPrint('👤 نوع المستخدم: ${LicenseService.userTypeText}');
      debugPrint('💬 الرسالة: ${result['message']}');
    } else {
      debugPrint('❌ فشل فحص الرخصة: ${result['error']}');
      debugPrint('🔍 سيريال الجهاز الحالي: ${result['current_device_serial']}');
      debugPrint('🔄 إعادة تعيين جميع القيم إلى الافتراضية');

      // إعادة تعيين جميع القيم إلى الافتراضية في حالة الفشل
      LicenseService.resetAllValues();
    }
  } catch (e) {
    debugPrint('💥 خطأ غير متوقع أثناء فحص الرخصة: $e');
    debugPrint('🔄 إعادة تعيين جميع القيم إلى الافتراضية');

    // إعادة تعيين جميع القيم إلى الافتراضية في حالة الخطأ
    LicenseService.resetAllValues();

    // في حالة الخطأ، إنشاء نتيجة افتراضية
    _licenseCheckResult = {
      'success': false,
      'error': e.toString(),
      'is_current_device': false,
      'current_device_serial': DeviceInfoService.deviceSerial ?? 'غير متوفر',
      'message': 'حدث خطأ أثناء فحص الترخيص',
    };
  }
}

class SmartAssistantApp extends StatefulWidget {
  const SmartAssistantApp({super.key});

  @override
  State<SmartAssistantApp> createState() => _SmartAssistantAppState();
}

class _SmartAssistantAppState extends State<SmartAssistantApp> {
  Locale _locale = const Locale('ar', ''); // Arabic as default

  void _changeLanguage(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'المساعد الذكي - Uptime_Smart_Assist',
      debugShowCheckedModeBanner: false,
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2E7D32), // لون أخضر للمطابخ
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        fontFamily: 'Cairo', // خط عربي
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
          titleTextStyle: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      builder: (context, child) {
        // Force RTL for Arabic, LTR for English
        return Directionality(
          textDirection: _locale.languageCode == 'ar'
              ? TextDirection.rtl
              : TextDirection.ltr,
          child: child!,
        );
      },
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/home': (context) => HomePage(onLanguageChanged: _changeLanguage),
        '/license': (context) => const LicenseActivationScreen(
              currentDeviceSerial: '',
              message: 'يرجى تفعيل ترخيص التطبيق',
            ),
        '/settings': (context) => const SettingsScreen(),
        '/aluminum_settings': (context) => const aluminum.AluminumSettingsScreen(),
        // '/upvc_settings': (context) => const upvc.UpvcSettingsScreen(),
      },
    );
  }
}

class HomePage extends StatelessWidget {
  final Function(Locale) onLanguageChanged;

  const HomePage({super.key, required this.onLanguageChanged});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.appTitle),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // Language toggle button
          PopupMenuButton<Locale>(
            icon: const Icon(Icons.language),
            onSelected: onLanguageChanged,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: Locale('ar', ''),
                child: Row(
                  children: [
                    Text('🇸🇦'),
                    SizedBox(width: 8),
                    Text('العربية'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: Locale('en', ''),
                child: Row(
                  children: [
                    Text('🇺🇸'),
                    SizedBox(width: 8),
                    Text('English'),
                  ],
                ),
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
      drawer: _buildDrawer(context),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA), // أبيض مائل للرمادي الفاتح
              Color(0xFFE8F5E8), // أخضر فاتح جداً
              Color(0xFFE3F2FD), // أزرق فاتح جداً
              Color(0xFFFFF8E1), // أصفر فاتح جداً
              Color(0xFFF3E5F5), // بنفسجي فاتح جداً
              Color(0xFFE0F2F1), // تركوازي فاتح جداً
            ],
            stops: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.topRight,
              radius: 1.5,
              colors: [
                Colors.white.withValues(alpha: 0.3),
                Colors.transparent,
              ],
              stops: const [0.0, 1.0],
            ),
          ),
          child: ServicesGrid(onLanguageChanged: onLanguageChanged),
        ),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Drawer(
      child: Column(
        children: [          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Icon(
                        Icons.handyman,
                        size: 40,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      localizations.appTitle,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      localizations.appSubtitle,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Tooltip(
                    message: localizations.treasurySummary,
                    child: FloatingActionButton.small(
                      heroTag: 'treasurySummary',
                      backgroundColor: Colors.white,
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      elevation: 2,
                      onPressed: () {
                        Navigator.pop(context); // Close drawer
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TreasurySummaryScreen(),
                          ),
                        );
                      },
                      child: const Icon(Icons.bar_chart),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  title: localizations.home,
                  icon: Icons.home,
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.cuttingService,
                  icon: Icons.handyman,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CuttingProjectsScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.costCalculation,
                  icon: Icons.calculate,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(
                        context,
                        localizations.costCalculation,
                        localizations.costCalculationDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.kitchenDesign,
                  icon: Icons.kitchen,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.kitchenDesign,
                        localizations.kitchenDesignDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.windowsDoors,
                  icon: Icons.door_front_door,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.windowsDoors,
                        localizations.windowsDoorsDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.projectManagement,
                  icon: Icons.assignment,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(
                        context,
                        localizations.projectManagement,
                        localizations.projectManagementDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.inventory,
                  icon: Icons.inventory,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.inventory,
                        localizations.inventoryDesc);
                  },                ),
                // خدمات إضافية
                _buildDrawerItem(
                  context,
                  title: localizations.treasury,
                  icon: Icons.account_balance_wallet,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TreasuryScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.treasurySummary,
                  icon: Icons.bar_chart,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TreasurySummaryScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.tasks,
                  icon: Icons.task_alt,
                  onTap: () {
                    Navigator.pop(context);
                    // Navigator.push(
                    //   context,
                    //   MaterialPageRoute(
                    //     builder: (context) => const TasksScreen(),
                    //   ),
                    // );
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('خدمة المهام غير متوفرة حالياً')),
                    );
                  },
                ),
                const Divider(),
                // خدمات الذكاء الاصطناعي
                _buildDrawerItem(
                  context,
                  title: localizations.aiVision,
                  icon: Icons.camera_enhance,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.aiVision,
                        localizations.aiVisionDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.aiAssistant,
                  icon: Icons.psychology,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.aiAssistant,
                        localizations.aiAssistantDesc);
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.aiDesign,
                  icon: Icons.auto_awesome,
                  onTap: () {
                    Navigator.pop(context);
                    _showComingSoonDialog(context, localizations.aiDesign,
                        localizations.aiDesignDesc);
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  title: localizations.settings,
                  icon: Icons.settings,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
                _buildDrawerItem(
                  context,
                  title: localizations.about,
                  icon: Icons.info,
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog(context);
                  },
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              '${localizations.version} © 2025',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).colorScheme.primary,
      ),
      title: Text(title),
      onTap: onTap,
    );
  }

  void _showAboutDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AboutDialog(
        applicationName: localizations.appTitle,
        applicationVersion: localizations.version,
        applicationIcon: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.handyman,
            size: 30,
            color: Colors.white,
          ),
        ),
        children: [
          const SizedBox(height: 16),
          Text(
            localizations.aboutDescription,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            localizations.aboutCopyright,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  void _showComingSoonDialog(
      BuildContext context, String title, String description) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Color(0xFF9C27B0),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      localizations.comingSoonMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              localizations.ok,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}

class ServicesGrid extends StatefulWidget {
  final Function(Locale) onLanguageChanged;

  const ServicesGrid({super.key, required this.onLanguageChanged});

  @override
  State<ServicesGrid> createState() => _ServicesGridState();
}

class _ServicesGridState extends State<ServicesGrid> {
  int _incompleteTasks = 0;

  @override
  void initState() {
    super.initState();
    _loadIncompleteTasks();
  }

  Future<void> _loadIncompleteTasks() async {
    try {
      // final taskService = TaskService(); // تم تعطيل خدمة المهام مؤقتاً
      // final statistics = await taskService.getTaskStatistics();
      // final incomplete = (statistics['pending'] ?? 0) + (statistics['inProgress'] ?? 0);

      if (mounted) {
        setState(() {
          _incompleteTasks = 0; // تم تعطيل خدمة المهام مؤقتاً
        });
      }
    } catch (e) {
      // في حالة الخطأ، نتجاهل ولا نعرض العداد
      if (mounted) {
        setState(() {
          _incompleteTasks = 0;
        });
      }
    }
  }

  List<Map<String, dynamic>> _getAllServices(AppLocalizations localizations) {
    return [
      {
        'title': localizations.cuttingService,
        'icon': Icons.handyman,
        'color': const Color(0xFF1976D2),
        'description': localizations.cuttingServiceDesc,
        'serviceType': 'cutting',
      },
      {
        'title': 'حسابات وفواتير',
        'icon': Icons.receipt_long,
        'color': const Color(0xFF388E3C),
        'description': 'إدارة الفواتير والحسابات وتقارير المبيعات والمشتريات',
        'serviceType': 'accounting',
      },
      {
        'title': localizations.treasury,
        'icon': Icons.account_balance_wallet,
        'color': const Color(0xFF4CAF50),
        'description': localizations.treasuryDesc,
        'serviceType': 'treasury',
      },
      {
        'title': localizations.treasurySummary,
        'icon': Icons.bar_chart,
        'color': const Color(0xFF2196F3),
        'description': localizations.treasurySummaryDesc,
        'serviceType': 'treasury_summary',
      },
      {
        'title': 'تخصيمات',
        'icon': Icons.design_services,
        'color': const Color(0xFFE65100),
        'description': 'خدمات التصميم المتخصصة للمطابخ والأبواب والشبابيك',
        'serviceType': 'design_services',
      },
      {
        'title': localizations.projectManagement,
        'icon': Icons.assignment,
        'color': const Color(0xFFD32F2F),
        'description': localizations.projectManagementDesc,
        'serviceType': 'projects',
      },
      {
        'title': localizations.inventory,
        'icon': Icons.inventory,
        'color': const Color(0xFF00796B),
        'description': localizations.inventoryDesc,
        'serviceType': 'inventory',
      },
      {
        'title': localizations.taskManagement,
        'icon': Icons.task_alt,
        'color': const Color(0xFF607D8B),
        'description': localizations.taskManagementDesc,
        'serviceType': 'tasks',
      },
      {
        'title': 'أدوات الذكاء الاصطناعي',
        'icon': Icons.auto_awesome,
        'color': const Color(0xFF9C27B0),
        'description': 'مجموعة من الأدوات المدعومة بالذكاء الاصطناعي',
        'serviceType': 'ai_tools',
      },
    ];
  }



  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;

    // تحديد الأحجام بناءً على عرض الشاشة
    double padding = screenWidth < 600 ? 8.0 : 12.0;

    // Get localized services
    final allServices = _getAllServices(localizations);

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // تحديد عدد الأعمدة بناءً على عرض الشاشة
                int crossAxisCount;
                double childAspectRatio;

                if (constraints.maxWidth < 600) {
                  // موبايل - عمودين
                  crossAxisCount = 2;
                  childAspectRatio = 1.2;
                } else if (constraints.maxWidth < 900) {
                  // تابلت - 3 أعمدة
                  crossAxisCount = 3;
                  childAspectRatio = 1.3;
                } else if (constraints.maxWidth < 1200) {
                  // شاشة متوسطة - 4 أعمدة
                  crossAxisCount = 4;
                  childAspectRatio = 1.4;
                } else {
                  // شاشة كبيرة - 5 أعمدة
                  crossAxisCount = 5;
                  childAspectRatio = 1.5;
                }

                return GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio: childAspectRatio,
                  ),
                  itemCount: allServices.length,
                  itemBuilder: (context, index) {
                    final service = allServices[index];
                    final isAI = service['serviceType'] == 'ai_tools';
                    final isTask = service['serviceType'] == 'tasks';
                    return ServiceCard(
                      title: service['title'],
                      icon: service['icon'],
                      color: service['color'],
                      description: service['description'],
                      isAIService: isAI,
                      isTaskService: isTask,
                      taskCount: isTask ? _incompleteTasks : null,
                      onTap: () => _navigateToService(
                          context, service['serviceType']),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToService(BuildContext context, String serviceType) async {
    final localizations = AppLocalizations.of(context)!;

    switch (serviceType) {
      case 'cutting':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CuttingProjectsScreen(),
          ),
        );
        break;      case 'accounting':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AccountingInvoicesScreen(),
          ),
        );
        break;
      case 'ai_tools':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const AIToolsScreen(),
          ),
        );
        break;
      case 'design_services':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DesignServicesScreen(),
          ),
        );
        break;
      case 'projects':
        _showComingSoonDialog(context, localizations.projectManagement,
            localizations.projectManagementDesc);
        break;
      case 'inventory':
        _showComingSoonDialog(
            context, localizations.inventory, localizations.inventoryDesc);
        break;      case 'treasury':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TreasuryScreen(),
          ),
        );
        break;
      case 'treasury_summary':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const TreasurySummaryScreen(),
          ),
        );
        break;
      case 'tasks':
        // final result = await Navigator.push(
        //   context,
        //   MaterialPageRoute(
        //     builder: (context) => const TasksScreen(),
        //   ),
        // );
        // // تحديث عداد المهام عند العودة من شاشة المهام
        // if (result == true && context.mounted) {
        //   final servicesGridState = context.findAncestorStateOfType<_ServicesGridState>();
        //   servicesGridState?._loadIncompleteTasks();
        // }
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خدمة المهام غير متوفرة حالياً')),
        );
        break;
    }
  }

  void _showComingSoonDialog(
      BuildContext context, String title, String description) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Color(0xFF9C27B0),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      localizations.comingSoonMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              localizations.ok,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}

class ServiceCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final String description;
  final VoidCallback onTap;
  final bool isAIService;
  final int? taskCount;
  final bool isTaskService;

  const ServiceCard({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.description,
    required this.onTap,
    this.isAIService = false,
    this.taskCount,
    this.isTaskService = false,
  });

  @override
  Widget build(BuildContext context) {
    // تحديد الأحجام بناءً على عرض الشاشة
    final screenWidth = MediaQuery.of(context).size.width;

    double iconSize;
    double titleFontSize;
    double descriptionFontSize;
    double cardPadding;

    if (screenWidth < 600) {
      // موبايل
      iconSize = 28;
      titleFontSize = 12;
      descriptionFontSize = 10;
      cardPadding = 8;
    } else if (screenWidth < 900) {
      // تابلت
      iconSize = 32;
      titleFontSize = 14;
      descriptionFontSize = 11;
      cardPadding = 10;
    } else {
      // شاشة كبيرة
      iconSize = 36;
      titleFontSize = 16;
      descriptionFontSize = 12;
      cardPadding = 12;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(cardPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isAIService
                  ? [
                      color.withValues(alpha: 0.15),
                      color.withValues(alpha: 0.08),
                      Colors.purple.withValues(alpha: 0.05),
                    ]
                  : [
                      color.withValues(alpha: 0.1),
                      color.withValues(alpha: 0.05),
                    ],
            ),
            border: isAIService
                ? Border.all(
                    color: Colors.purple.withValues(alpha: 0.3),
                    width: 1,
                  )
                : null,
            boxShadow: isAIService
                ? [
                    BoxShadow(
                      color: Colors.purple.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Stack(
            children: [
              // أيقونة الذكاء الاصطناعي في الزاوية العلوية اليمنى
              if (isAIService)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.purple.withValues(alpha: 0.6),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.purple.withValues(alpha: 0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.auto_awesome,
                      size: iconSize * 0.8,
                      color: Colors.white,
                    ),
                  ),
                ),
              // عداد المهام غير المكتملة في الزاوية العلوية اليسرى
              if (isTaskService && taskCount != null && taskCount! > 0)
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.6),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                        BoxShadow(
                          color: Colors.red.withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Text(
                      '$taskCount',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: iconSize * 0.4,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              // محتوى الكارت الرئيسي
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(cardPadding * 0.5),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                      boxShadow: isAIService
                          ? [
                              BoxShadow(
                                color: color.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                              BoxShadow(
                                color: Colors.purple.withValues(alpha: 0.2),
                                blurRadius: 12,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Icon(
                      icon,
                      size: iconSize,
                      color: color,
                    ),
                  ),
                  SizedBox(height: cardPadding * 0.5),
                  Flexible(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: cardPadding * 0.25),
                  Flexible(
                    child: Text(
                      description,
                      style: TextStyle(
                        fontSize: descriptionFontSize,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
