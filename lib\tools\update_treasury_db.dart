import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';

/// This script is used to update the database schema with indexes for better performance.
/// Run this script only once to update an existing database.
Future<void> main() async {
  debugPrint('Treasury Database Schema Update Tool');
  debugPrint('-----------------------------------');
  
  // Initialize FFI for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }
  
  try {
    // Get the database path
    String path = join(await getDatabasesPath(), 'uptime_treasury.db');
    
    // Check if the database exists
    if (!File(path).existsSync()) {
      debugPrint('Database not found at: $path');
      debugPrint('No update needed. The database will be created with indexes when the app runs.');
      return;
    }
    
    debugPrint('Found database at: $path');
    
    // Open the database
    final db = await openDatabase(path);
    
    // Check if indexes already exist
    final indexCheck = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_treasury_id'");
    
    if (indexCheck.isNotEmpty) {
      debugPrint('Indexes already exist. No update needed.');
      await db.close();
      return;
    }
    
    // Create indexes
    debugPrint('Creating indexes for better performance...');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_treasury_id ON treasury_transactions(treasury_id);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_transaction_date ON treasury_transactions(date);');
    
    // Enable foreign keys
    await db.execute('PRAGMA foreign_keys = ON');
    
    // Close the database
    await db.close();
    
    debugPrint('Database updated successfully!');
  } catch (e) {
    debugPrint('Error updating database: $e');
  }
}
