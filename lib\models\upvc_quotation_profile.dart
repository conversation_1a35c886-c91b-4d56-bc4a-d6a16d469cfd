enum UpvcProfileType {
  hinge('hinge', 'مفصلي', 'Hinge'),
  sliding('sliding', 'سحاب', 'Sliding');

  const UpvcProfileType(this.key, this.arabicName, this.englishName);
  final String key;
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

enum UpvcProfileCategory {
  halaf('halaf', 'حلق', 'Frame'),
  bar('bar', 'بار', 'Bar'),
  dalfa('dalfa', 'ضلفة', 'Sash'),
  marad('marad', 'مرد', 'Mullion'),
  baketa('baketa', 'باكتة', 'Bead'),
  soas('soas', 'سؤاس', 'Sill'),
  dalfaSilk('dalfaSilk', 'ضلفة سلك', 'Screen Sash'),
  olba('olba', 'علبة', 'Box'),
  filta('filta', 'فلتة', 'Gasket'),
  skineh('skineh', 'سكينة', 'Track'), // Only for sliding
  anf('anf', 'الانف', 'Nose'); // Only for sliding

  const UpvcProfileCategory(this.key, this.arabicName, this.englishName);
  final String key;
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }

  static List<UpvcProfileCategory> getHingeCategories() {
    return [
      halaf, bar, dalfa, marad, baketa, soas, dalfaSilk, olba, filta
    ];
  }

  static List<UpvcProfileCategory> getSlidingCategories() {
    return [
      halaf, bar, dalfa, skineh, baketa, soas, dalfaSilk, olba, filta, anf
    ];
  }
}

class UpvcProfile {
  final int? id;
  final String name;
  final String code;
  final UpvcProfileType type;
  final UpvcProfileCategory category;
  final int? seriesId; // ربط بمجموعة القطاعات
  final double? width;
  final double? height;
  final double? thickness;
  final double? weight; // kg per meter
  final String color;
  final String description;
  final String? lipType; // نوع الشفة
  final double? lipThickness; // سمك الشفة
  final bool? withBaketa; // بالباكتة (للضلفة المفصلي فقط)
  final bool? withDalfa; // بالضلفة (للسكينة السحاب فقط)
  final String? imagePath;
  final double? pricePerMeter;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  UpvcProfile({
    this.id,
    required this.name,
    required this.code,
    required this.type,
    required this.category,
    this.seriesId,
    this.width,
    this.height,
    this.thickness,
    this.weight,
    this.color = '',
    this.description = '',
    this.lipType,
    this.lipThickness,
    this.withBaketa,
    this.withDalfa,
    this.imagePath,
    this.pricePerMeter,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'type': type.key,
      'category': category.key,
      'series_id': seriesId,
      'width': width,
      'height': height,
      'thickness': thickness,
      'weight': weight,
      'color': color,
      'description': description,
      'lip_type': lipType,
      'lip_thickness': lipThickness,
      'with_baketa': withBaketa != null ? (withBaketa! ? 1 : 0) : null,
      'with_dalfa': withDalfa != null ? (withDalfa! ? 1 : 0) : null,
      'image_path': imagePath,
      'price_per_meter': pricePerMeter,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcProfile.fromMap(Map<String, dynamic> map) {
    return UpvcProfile(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      code: map['code'] ?? '',
      type: UpvcProfileType.values.firstWhere(
        (t) => t.key == map['type'],
        orElse: () => UpvcProfileType.hinge,
      ),
      category: UpvcProfileCategory.values.firstWhere(
        (c) => c.key == map['category'],
        orElse: () => UpvcProfileCategory.halaf,
      ),
      seriesId: map['series_id']?.toInt(),
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      thickness: map['thickness']?.toDouble(),
      weight: map['weight']?.toDouble(),
      color: map['color'] ?? '',
      description: map['description'] ?? '',
      lipType: map['lip_type'],
      lipThickness: map['lip_thickness']?.toDouble(),
      withBaketa: map['with_baketa'] != null ? (map['with_baketa'] == 1) : null,
      withDalfa: map['with_dalfa'] != null ? (map['with_dalfa'] == 1) : null,
      imagePath: map['image_path'],
      pricePerMeter: map['price_per_meter']?.toDouble(),
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  UpvcProfile copyWith({
    int? id,
    String? name,
    String? code,
    UpvcProfileType? type,
    UpvcProfileCategory? category,
    int? seriesId,
    double? width,
    double? height,
    double? thickness,
    double? weight,
    String? color,
    String? description,
    String? lipType,
    double? lipThickness,
    bool? withBaketa,
    bool? withDalfa,
    String? imagePath,
    double? pricePerMeter,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UpvcProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      type: type ?? this.type,
      category: category ?? this.category,
      seriesId: seriesId ?? this.seriesId,
      width: width ?? this.width,
      height: height ?? this.height,
      thickness: thickness ?? this.thickness,
      weight: weight ?? this.weight,
      color: color ?? this.color,
      description: description ?? this.description,
      lipType: lipType ?? this.lipType,
      lipThickness: lipThickness ?? this.lipThickness,
      withBaketa: withBaketa ?? this.withBaketa,
      withDalfa: withDalfa ?? this.withDalfa,
      imagePath: imagePath ?? this.imagePath,
      pricePerMeter: pricePerMeter ?? this.pricePerMeter,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UpvcProfile{id: $id, name: $name, code: $code, type: $type, category: $category}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpvcProfile &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.type == type &&
        other.category == category;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        code.hashCode ^
        type.hashCode ^
        category.hashCode;
  }
}
