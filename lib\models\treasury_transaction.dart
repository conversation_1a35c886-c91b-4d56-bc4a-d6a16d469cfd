import 'package:intl/intl.dart';

class TreasuryTransaction {
  final int? id;
  final int treasuryId;
  final String description;
  final double income;
  final double expenses;
  final String notes;
  final DateTime date;
  final DateTime createdAt;
  final int? invoiceId; // ربط المعاملة بالفاتورة
  final String? invoiceNumber; // رقم الفاتورة للعرض

  TreasuryTransaction({
    this.id,
    required this.treasuryId,
    required this.description,
    required this.income,
    required this.expenses,
    required this.notes,
    required this.date,
    DateTime? createdAt,
    this.invoiceId,
    this.invoiceNumber,
  }) : createdAt = createdAt ?? DateTime.now();

  TreasuryTransaction copyWith({
    int? id,
    int? treasuryId,
    String? description,
    double? income,
    double? expenses,
    String? notes,
    DateTime? date,
    DateTime? createdAt,
    int? invoiceId,
    String? invoiceNumber,
  }) {
    return TreasuryTransaction(
      id: id ?? this.id,
      treasuryId: treasuryId ?? this.treasuryId,
      description: description ?? this.description,
      income: income ?? this.income,
      expenses: expenses ?? this.expenses,
      notes: notes ?? this.notes,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'treasury_id': treasuryId,
      'description': description,
      'income': income,
      'expenses': expenses,
      'notes': notes,
      'date': DateFormat('yyyy-MM-dd').format(date),
      'created_at': createdAt.millisecondsSinceEpoch,
      'invoice_id': invoiceId,
      'invoice_number': invoiceNumber,
    };
  }

  factory TreasuryTransaction.fromMap(Map<String, dynamic> map) {
    return TreasuryTransaction(
      id: map['id'] as int?,
      treasuryId: map['treasury_id'] as int,
      description: map['description'] as String,
      income: map['income'] as double,
      expenses: map['expenses'] as double,
      notes: map['notes'] as String,
      date: DateTime.parse(map['date'] as String),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      invoiceId: map['invoice_id'] as int?,
      invoiceNumber: map['invoice_number'] as String?,
    );
  }

  double get balance => income - expenses;
}
