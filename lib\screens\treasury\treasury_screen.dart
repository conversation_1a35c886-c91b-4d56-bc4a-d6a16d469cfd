import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';
import '../../services/unified_treasury_service.dart' as unified;
import '../../providers/database_provider.dart';
import 'treasury_transaction_form.dart';
import 'treasury_summary_screen.dart';
import 'widgets/daily_summary_card.dart';

class TreasuryScreen extends StatefulWidget {
  const TreasuryScreen({super.key});

  @override
  State<TreasuryScreen> createState() => _TreasuryScreenState();
}

class _TreasuryScreenState extends State<TreasuryScreen> {
  late unified.UnifiedTreasuryService _databaseHelper;
  List<Treasury> _treasuries = [];
  bool _isLoading = true;
  bool _isLoadingToday = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _balanceController = TextEditingController(text: '0.00');
  final TextEditingController _dateController = TextEditingController(
    text: DateFormat('yyyy-MM-dd').format(DateTime.now())
  );

  // Add variables for today's transactions
  Map<int, List<TreasuryTransaction>> _todayTransactions = {};
  Map<int, Map<String, double>> _todaySummary = {};


  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _databaseHelper = DatabaseProvider.of(context);
    _loadTreasuries();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _nameController.dispose();
    _balanceController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _loadTreasuries() async {
    setState(() {
      _isLoading = true;

    });

    try {
      List<Treasury> treasuries;
      if (_searchQuery.isEmpty) {
        final treasuriesData = await _databaseHelper.getAllTreasuries();
        treasuries = treasuriesData.map((data) => Treasury.fromMap(data)).toList();
      } else {
        final treasuriesData = await _databaseHelper.getAllTreasuries();
        treasuries = treasuriesData.map((data) => Treasury.fromMap(data)).toList()
            .where((t) => t.name.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
      }

      // Load today's transactions for each treasury
      setState(() {
        _isLoadingToday = true;
        _treasuries = treasuries;
        _isLoading = false;
      });

      final Map<int, List<TreasuryTransaction>> todayTransactions = {};
      final Map<int, Map<String, double>> todaySummary = {};

      for (final treasury in treasuries) {
        if (treasury.id != null) {
          final transactionsData = await _databaseHelper.getTodayTransactions(treasury.id!);
          todayTransactions[treasury.id!] = transactionsData.map((data) => TreasuryTransaction.fromMap(data)).toList();
          todaySummary[treasury.id!] = await _databaseHelper.getTodaySummary(treasury.id!);
        }
      }

      setState(() {
        _todayTransactions = todayTransactions;
        _todaySummary = todaySummary;
        _isLoadingToday = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isLoadingToday = false;

      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.errorLoadingData(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddTreasuryDialog(BuildContext context) {
    _nameController.clear();
    _balanceController.text = '0.00';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.addTreasury),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.treasuryName,
              ),
            ),
            TextField(
              controller: _balanceController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.initialBalance,
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
            ),
            TextField(
              controller: _dateController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.date,
              ),
              readOnly: true,
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () async {
              if (_nameController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(AppLocalizations.of(context)!.treasuryNameRequired),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }              final treasury = Treasury(
                name: _nameController.text.trim(),
                previousBalance: double.parse(_balanceController.text),
                currentBalance: 0.0,
                date: DateTime.parse(_dateController.text),
              );

              await _databaseHelper.insertTreasury(treasury.toMap());
              _loadTreasuries();
              // ignore: use_build_context_synchronously
              Navigator.pop(context);
            },
            child: Text(AppLocalizations.of(context)!.add),
          ),
        ],
      ),
    );
  }

  Widget _buildDailySummary(int treasuryId) {
    if (_isLoadingToday) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    final summary = _todaySummary[treasuryId];
    final transactions = _todayTransactions[treasuryId];

    if (summary == null || transactions == null) {
      return const SizedBox.shrink();
    }

    final todayIncome = summary['income'] ?? 0.0;
    final todayExpenses = summary['expenses'] ?? 0.0;
    final todayNet = todayIncome - todayExpenses;

    return DailySummaryCard(
      todayIncome: todayIncome,
      todayExpenses: todayExpenses,
      todayNet: todayNet,
      transactions: transactions,
      onAddTransaction: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TreasuryTransactionForm(treasuryId: treasuryId),
          ),
        ).then((_) => _loadTreasuries());
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.treasuries),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TreasurySummaryScreen(),
                ),
              ).then((_) => _loadTreasuries());
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: localizations.searchTreasury,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _loadTreasuries();
              },
            ),
          ),

          // Main Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _treasuries.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.account_balance_wallet_outlined,
                              size: 64,
                              color: theme.colorScheme.primary.withValues(alpha: 0.3),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? localizations.noTreasuriesFound
                                  : localizations.noTreasuriesMatchSearch,
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadTreasuries,
                        child: ListView.builder(
                          physics: const BouncingScrollPhysics(),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12.0,
                            vertical: 8.0,
                          ),
                          itemCount: _treasuries.length,
                          itemBuilder: (context, index) {
                            final treasury = _treasuries[index];
                            return Column(
                              children: [
                                Material(
                                  borderRadius: BorderRadius.circular(16),
                                  elevation: 2,
                                  color: theme.colorScheme.surface,
                                  child: InkWell(
                                    onTap: () {
                                      if (treasury.id != null) {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                TreasuryTransactionForm(
                                                  treasuryId: treasury.id!,
                                                ),
                                          ),
                                        ).then((_) => _loadTreasuries());
                                      }
                                    },
                                    borderRadius: BorderRadius.circular(16),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16.0),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  treasury.name,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  DateFormat('yyyy-MM-dd')
                                                      .format(treasury.date),
                                                  style: theme.textTheme.bodyMedium
                                                      ?.copyWith(
                                                    color: theme.colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.3),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                NumberFormat("#,##0.00")
                                                    .format(treasury.finalBalance),
                                                style: TextStyle(
                                                  color: treasury.finalBalance >= 0
                                                      ? Colors.green
                                                      : Colors.red,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              Text(
                                                'رصيد',
                                                style: theme.textTheme.bodySmall
                                                    ?.copyWith(
                                                  color: theme.colorScheme.onSurface
                                                      .withValues(alpha: 0.3),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                if (treasury.id != null)
                                  _buildDailySummary(treasury.id!),
                              ],
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddTreasuryDialog(context),
        backgroundColor: theme.colorScheme.primary,
        icon: const Icon(Icons.add),
        label: Text(localizations.addTreasury),
      ),
    );
  }
}
