import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/cutting_project.dart';
import '../../services/unified_cutting_service.dart';
import 'add_edit_project_screen.dart';
import 'project_view_screen.dart';

class CuttingProjectsScreen extends StatefulWidget {
  const CuttingProjectsScreen({super.key});

  @override
  State<CuttingProjectsScreen> createState() => _CuttingProjectsScreenState();
}

class _CuttingProjectsScreenState extends State<CuttingProjectsScreen> {
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();
  List<CuttingProject> _projects = [];
  List<CuttingProject> _filteredProjects = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final projectsData = await _databaseHelper.getAllCuttingProjects();
      final projects = projectsData.map((data) => CuttingProject.fromMap(data)).toList();
      setState(() {
        _projects = projects;
        _filteredProjects = projects;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        _showErrorSnackBar('Error loading projects: $e');
      }
    }
  }

  void _filterProjects(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProjects = _projects;
      } else {
        _filteredProjects = _projects.where((project) {
          return project.projectNumber.toLowerCase().contains(query.toLowerCase()) ||
              project.customerName.toLowerCase().contains(query.toLowerCase()) ||
              project.phone.toLowerCase().contains(query.toLowerCase()) ||
              project.address.toLowerCase().contains(query.toLowerCase()) ||
              project.notes.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _deleteProject(CuttingProject project) async {
    final localizations = AppLocalizations.of(context)!;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.confirmDelete),
        content: Text(localizations.deleteProjectMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );

    if (confirmed == true && project.id != null) {
      try {
        await _databaseHelper.deleteCuttingProject(project.id!);
        _showSuccessSnackBar(localizations.projectDeleted);
        _loadProjects();
      } catch (e) {
        _showErrorSnackBar('${localizations.error}: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.cuttingProjects),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: localizations.searchProjects,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: _filterProjects,
            ),
          ),

          // Add new project button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  final result = await Navigator.push<bool>(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddEditProjectScreen(),
                    ),
                  );
                  if (result == true) {
                    _loadProjects();
                  }
                },
                icon: const Icon(Icons.add),
                label: Text(localizations.addNewProject),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Projects list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredProjects.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.folder_open,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              localizations.noProjectsFound,
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : _buildProjectsDataTable(localizations, dateFormat),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectsDataTable(AppLocalizations localizations, DateFormat dateFormat) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد ما إذا كانت الشاشة صغيرة (موبايل) أم كبيرة (ويندوز/متصفح)
        bool isMobile = constraints.maxWidth < 800;

        if (isMobile) {
          // تصميم Cards للموبايل
          return _buildMobileProjectsList(localizations, dateFormat);
        } else {
          // تصميم DataTable للشاشات الكبيرة
          return _buildDesktopProjectsTable(localizations, dateFormat);
        }
      },
    );
  }

  Widget _buildMobileProjectsList(AppLocalizations localizations, DateFormat dateFormat) {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        scrollbars: true,
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: _filteredProjects.length,
        itemBuilder: (context, index) {
        final project = _filteredProjects[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12.0),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رقم المشروع واسم العميل
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${localizations.projectNumber}: ${project.projectNumber}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.blue,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            project.customerName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // أزرار الإجراءات
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.visibility, color: Colors.green, size: 24),
                          onPressed: () async {
                            final result = await Navigator.push<bool>(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ProjectViewScreen(project: project),
                              ),
                            );
                            if (result == true) {
                              _loadProjects();
                            }
                          },
                          tooltip: localizations.viewProject,
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue, size: 24),
                          onPressed: () async {
                            final result = await Navigator.push<bool>(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddEditProjectScreen(project: project),
                              ),
                            );
                            if (result == true) {
                              _loadProjects();
                            }
                          },
                          tooltip: localizations.editProject,
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red, size: 24),
                          onPressed: () => _deleteProject(project),
                          tooltip: localizations.deleteProject,
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // معلومات المشروع
                _buildInfoRow(Icons.phone, localizations.phone, project.phone),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.calendar_today, localizations.date, dateFormat.format(project.date)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.schedule, localizations.deliveryDate, dateFormat.format(project.deliveryDate)),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.location_on, localizations.address, project.address),
                if (project.notes.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.note, localizations.notes, project.notes),
                ],
              ],
            ),
          ),
        );
        },
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 18, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopProjectsTable(AppLocalizations localizations, DateFormat dateFormat) {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        scrollbars: true,
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(
            scrollbars: true,
          ),
          child: SingleChildScrollView(
            child: DataTable(
          columnSpacing: 20,
          headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
          columns: [
            DataColumn(
              label: Text(
                localizations.projectNumber,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.customerName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.date,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.deliveryDate,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.phone,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.address,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.notes,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            DataColumn(
              label: Text(
                localizations.actions,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
          rows: _filteredProjects.map((project) {
            return DataRow(
              cells: [
                DataCell(Text(project.projectNumber)),
                DataCell(Text(project.customerName)),
                DataCell(Text(dateFormat.format(project.date))),
                DataCell(Text(dateFormat.format(project.deliveryDate))),
                DataCell(Text(project.phone)),
                DataCell(
                  SizedBox(
                    width: 150,
                    child: Text(
                      project.address,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: 150,
                    child: Text(
                      project.notes,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.visibility, color: Colors.green),
                        onPressed: () async {
                          final result = await Navigator.push<bool>(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ProjectViewScreen(project: project),
                            ),
                          );
                          if (result == true) {
                            _loadProjects();
                          }
                        },
                        tooltip: localizations.viewProject,
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.blue),
                        onPressed: () async {
                          final result = await Navigator.push<bool>(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddEditProjectScreen(project: project),
                            ),
                          );
                          if (result == true) {
                            _loadProjects();
                          }
                        },
                        tooltip: localizations.editProject,
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _deleteProject(project),
                        tooltip: localizations.deleteProject,
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
          ),
        ),
      ),
    );
  }
}