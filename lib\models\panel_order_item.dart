enum PanelMaterial {
  wood('خشب', 'wood'),
  fiber('فيبر', 'fiber'),
  glass('زجاج', 'glass'),
  mdf('MDF', 'mdf'),
  plywood('أبلكاش', 'plywood');

  const PanelMaterial(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;
}

class PanelOrderItem {
  final String id;
  final String label;
  final double width;
  final double height;
  final int quantity;
  final PanelMaterial material;

  PanelOrderItem({
    required this.id,
    required this.label,
    required this.width,
    required this.height,
    required this.quantity,
    required this.material,
  });

  factory PanelOrderItem.fromMap(Map<String, dynamic> map) {
    return PanelOrderItem(
      id: map['id'] ?? '',
      label: map['label'] ?? '',
      width: (map['width'] ?? 0).toDouble(),
      height: (map['height'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 0,
      material: PanelMaterial.values.firstWhere(
        (m) => m.englishName == map['material'],
        orElse: () => PanelMaterial.wood,
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'label': label,
      'width': width,
      'height': height,
      'quantity': quantity,
      'material': material.englishName,
    };
  }

  PanelOrderItem copyWith({
    String? id,
    String? label,
    double? width,
    double? height,
    int? quantity,
    PanelMaterial? material,
  }) {
    return PanelOrderItem(
      id: id ?? this.id,
      label: label ?? this.label,
      width: width ?? this.width,
      height: height ?? this.height,
      quantity: quantity ?? this.quantity,
      material: material ?? this.material,
    );
  }

  // مساحة القطعة الواحدة
  double get area => width * height;

  // المساحة الإجمالية لجميع القطع
  double get totalArea => area * quantity;

  @override
  String toString() {
    return 'PanelOrderItem{id: $id, label: $label, width: $width, height: $height, quantity: $quantity, material: ${material.arabicName}}';
  }
}
