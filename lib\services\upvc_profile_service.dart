import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/upvc_profile.dart';

class UnifiedUpvcService {
  static const String _databaseName = 'upvc_profiles.db';
  static const int _databaseVersion = 1;
  static const String _profilesTable = 'upvc_profiles';
  static const String _seriesTable = 'upvc_profile_series';

  Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // جدول مجموعات القطاعات
    await db.execute('''
      CREATE TABLE $_seriesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول القطاعات
    await db.execute('''
      CREATE TABLE $_profilesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        series_id INTEGER,
        width REAL,
        height REAL,
        thickness REAL,
        weight REAL,
        color TEXT,
        description TEXT,
        lip_type TEXT,
        lip_thickness REAL,
        with_baketa INTEGER,
        with_dalfa INTEGER,
        image_path TEXT,
        price_per_meter REAL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES $_seriesTable (id) ON DELETE CASCADE
      )
    ''');
  }

  // إدراج مجموعة قطاعات
  Future<int> insertSeries(Map<String, dynamic> series) async {
    final db = await database;
    return await db.insert(_seriesTable, series);
  }

  // إدراج قطاع
  Future<int> insertProfile(UpvcProfile profile) async {
    final db = await database;
    return await db.insert(_profilesTable, profile.toMap());
  }

  // جلب جميع المجموعات
  Future<List<Map<String, dynamic>>> getAllSeries() async {
    final db = await database;
    return await db.query(
      _seriesTable,
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name ASC',
    );
  }

  // جلب القطاعات حسب المجموعة والنوع والفئة
  Future<List<UpvcProfile>> getProfilesBySeriesAndTypeAndCategory(
    int seriesId,
    UpvcProfileType type,
    UpvcProfileCategory category,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _profilesTable,
      where: 'series_id = ? AND type = ? AND category = ? AND is_active = ?',
      whereArgs: [seriesId, type.englishName, category.englishName, 1],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return UpvcProfile.fromMap(maps[i]);
    });
  }

  // جلب جميع القطاعات حسب المجموعة والنوع
  Future<List<UpvcProfile>> getProfilesBySeriesAndType(
    int seriesId,
    UpvcProfileType type,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _profilesTable,
      where: 'series_id = ? AND type = ? AND is_active = ?',
      whereArgs: [seriesId, type.englishName, 1],
      orderBy: 'category ASC, name ASC',
    );

    return List.generate(maps.length, (i) {
      return UpvcProfile.fromMap(maps[i]);
    });
  }

  // تحديث قطاع
  Future<int> updateProfile(UpvcProfile profile) async {
    final db = await database;
    return await db.update(
      _profilesTable,
      profile.toMap(),
      where: 'id = ?',
      whereArgs: [profile.id],
    );
  }

  // حذف قطاع
  Future<int> deleteProfile(int id) async {
    final db = await database;
    return await db.update(
      _profilesTable,
      {'is_active': 0, 'updated_at': DateTime.now().millisecondsSinceEpoch},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // إدراج بيانات تجريبية
  Future<void> insertSampleData() async {
    final db = await database;

    // التحقق من وجود بيانات
    final count = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_seriesTable'),
    );

    if (count != null && count > 0) {
      debugPrint('Sample uPVC data already exists');
      return;
    }

    // إدراج مجموعات تجريبية
    final now = DateTime.now().millisecondsSinceEpoch;
    
    final series1Id = await db.insert(_seriesTable, {
      'name': 'Veka uPVC',
      'description': 'مجموعة قطاعات Veka للـ uPVC',
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
    });

    // ignore: unused_local_variable
    final series2Id = await db.insert(_seriesTable, {
      'name': 'Rehau uPVC',
      'description': 'مجموعة قطاعات Rehau للـ uPVC',
      'is_active': 1,
      'created_at': now,
      'updated_at': now,
    });

    // إدراج قطاعات تجريبية للمفصلي
    final hingeProfiles = [
      // حلق مفصلي - يحتاج نوع الشفة وسمك الشفة
      UpvcProfile(
        name: 'حلق مفصلي Veka',
        code: 'VK-H-001',
        type: UpvcProfileType.hinge,
        category: UpvcProfileCategory.halaf,
        seriesId: series1Id,
        thickness: 2.5,
        lipType: 'بدون شفة',
        lipThickness: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      // ضلفة مفصلي - يحتاج نوع الشفة وسمك الشفة وبالباكتة
      UpvcProfile(
        name: 'ضلفة مفصلي Veka',
        code: 'VK-D-001',
        type: UpvcProfileType.hinge,
        category: UpvcProfileCategory.dalfa,
        seriesId: series1Id,
        thickness: 2.0,
        lipType: 'شفه منه فيه',
        lipThickness: 1.5,
        withBaketa: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      // بار مفصلي - لا يحتاج شفة
      UpvcProfile(
        name: 'بار مفصلي Veka',
        code: 'VK-B-001',
        type: UpvcProfileType.hinge,
        category: UpvcProfileCategory.bar,
        seriesId: series1Id,
        thickness: 1.8,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // إدراج قطاعات تجريبية للسحاب
    final slidingProfiles = [
      // حلق سحاب
      UpvcProfile(
        name: 'حلق سحاب Veka',
        code: 'VK-HS-001',
        type: UpvcProfileType.sliding,
        category: UpvcProfileCategory.halaf,
        seriesId: series1Id,
        thickness: 2.8,
        lipType: 'بار خارجى',
        lipThickness: 2.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      // سكينة سحاب - يحتاج بالضلفة
      UpvcProfile(
        name: 'سكينة سحاب Veka',
        code: 'VK-SK-001',
        type: UpvcProfileType.sliding,
        category: UpvcProfileCategory.skineh,
        seriesId: series1Id,
        thickness: 1.5,
        withDalfa: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      // الانف سحاب
      UpvcProfile(
        name: 'الانف سحاب Veka',
        code: 'VK-AN-001',
        type: UpvcProfileType.sliding,
        category: UpvcProfileCategory.anf,
        seriesId: series1Id,
        thickness: 1.2,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // إدراج القطاعات
    for (final profile in [...hingeProfiles, ...slidingProfiles]) {
      await db.insert(_profilesTable, profile.toMap());
    }

    debugPrint('Sample uPVC data inserted successfully');
  }

  // إعادة تعيين قاعدة البيانات
  Future<void> resetDatabase() async {
    final db = await database;
    await db.delete(_profilesTable);
    await db.delete(_seriesTable);
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
