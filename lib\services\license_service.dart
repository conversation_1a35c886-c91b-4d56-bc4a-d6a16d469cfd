import 'package:flutter/foundation.dart';
import 'mysql_service.dart';
import 'device_info_service.dart';

/// خدمة فحص رخصة البرنامج والحصول على معلومات الاشتراك
class LicenseService {
  static LicenseService? _instance;
  static LicenseService get instance => _instance ??= LicenseService._();
  
  LicenseService._();
  
  static final MySQLService _mysql = MySQLService.instance;
  
  // متغير لتخزين قيمة is_dev
  static int? _isDevValue;
  
  /// قيمة serial المخزنة محلياً
  static String? _serialValue;
  

  
  /// الحصول على قيمة is_dev المحفوظة
  static int? get isDevValue => _isDevValue;
  
  /// الحصول على قيمة serial
  static String? get serialValue => _serialValue;
  

  
  /// فحص رخصة البرنامج وجلب جميع بيانات الاشتراك من جدول subscription
  static Future<Map<String, dynamic>> checkLicense() async {
    try {
      debugPrint('🔗 محاولة الاتصال بقاعدة البيانات الأونلاين...');

      // أولاً، جلب سيريال الجهاز الحالي
      String? currentDeviceSerial;
      try {
        final deviceInfo = await DeviceInfoService.getDeviceInfo();
        if (deviceInfo['success'] == true) {
          currentDeviceSerial = DeviceInfoService.deviceSerial;
          debugPrint('🔍 سيريال الجهاز الحالي: $currentDeviceSerial');
        } else {
          debugPrint('⚠️ لم يتم الحصول على سيريال الجهاز');
        }
      } catch (e) {
        debugPrint('❌ خطأ في جلب سيريال الجهاز: $e');
      }

      // محاولة الاتصال بقاعدة البيانات الأونلاين
      if (!await _mysql.connect()) {
        debugPrint('❌ فشل الاتصال بقاعدة البيانات الأونلاين لفحص الرخصة');
        debugPrint('🌐 تحقق من اتصال الإنترنت وإعدادات قاعدة البيانات');
        return {
          'success': false,
          'error': 'فشل الاتصال بقاعدة البيانات الأونلاين - تحقق من اتصال الإنترنت',
          'is_dev': null,
          'serial': null,
        };
      }

      debugPrint('✅ تم الاتصال بقاعدة البيانات بنجاح');
      debugPrint('📊 جلب بيانات الاشتراك من جدول subscription...');

      // البحث عن سيريال الجهاز الحالي في قاعدة البيانات
      List<Map<String, dynamic>> subscriptionData = [];
      bool deviceFound = false;

      if (currentDeviceSerial != null && currentDeviceSerial.isNotEmpty && currentDeviceSerial != 'غير متوفر') {
        debugPrint('🔍 البحث عن سيريال الجهاز الحالي في قاعدة البيانات: $currentDeviceSerial');
        subscriptionData = await _mysql.select(
          'SELECT is_dev, serial FROM subscription WHERE is_active = 1 AND serial = ?',
          [currentDeviceSerial]
        );
        debugPrint('📋 عدد السجلات المطابقة لسيريال الجهاز: ${subscriptionData.length}');

        if (subscriptionData.isNotEmpty) {
          deviceFound = true;
          debugPrint('✅ تم العثور على سيريال الجهاز في قاعدة البيانات');
        } else {
          debugPrint('❌ لم يتم العثور على سيريال الجهاز في قاعدة البيانات');
          debugPrint('🚫 الجهاز غير مرخص لاستخدام التطبيق');
        }
      } else {
        debugPrint('⚠️ لم يتم الحصول على سيريال صحيح للجهاز');
      }

      if (deviceFound && subscriptionData.isNotEmpty) {
        // الجهاز مسجل ومرخص
        final data = subscriptionData.first;

        final isDevValue = data['is_dev'];
        _isDevValue = isDevValue is int ? isDevValue : int.tryParse(isDevValue.toString());

        final serialValue = data['serial'];
        _serialValue = serialValue?.toString();

        debugPrint('✅ تم العثور على ترخيص صحيح للجهاز:');
        debugPrint('   📊 is_dev: $_isDevValue');
        debugPrint('   🔢 سيريال الجهاز المرخص: $_serialValue');
        debugPrint('   🔍 سيريال الجهاز الحالي: $currentDeviceSerial');
        debugPrint('   ✅ الجهاز مرخص ومسجل في النظام');
        debugPrint('📝 البيانات المسترجعة: $data');

        return {
          'success': true,
          'is_dev': _isDevValue,
          'serial': _serialValue,
          'current_device_serial': currentDeviceSerial,
          'is_current_device': true,
          'message': 'تم فحص الرخصة بنجاح - الجهاز مسجل ومرخص',
        };
      } else {
        // الجهاز غير مسجل أو غير مرخص
        debugPrint('❌ الجهاز غير مرخص لاستخدام التطبيق');
        debugPrint('🔍 سيريال الجهاز الحالي: $currentDeviceSerial');
        debugPrint('💡 يجب تسجيل سيريال الجهاز في نظام إدارة التراخيص');

        // إعادة تعيين القيم
        _isDevValue = null;
        _serialValue = null;

        return {
          'success': false,
          'error': 'الجهاز غير مرخص - سيريال الجهاز غير مسجل في النظام',
          'is_dev': null,
          'serial': null,
          'current_device_serial': currentDeviceSerial,
          'is_current_device': false,
          'message': 'الجهاز غير مرخص - يرجى تسجيل سيريال الجهاز لدى الدعم الفني',
        };
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في فحص رخصة البرنامج: $e');
      debugPrint('🔍 تفاصيل الخطأ: ${e.runtimeType}');
      return {
        'success': false,
        'error': 'خطأ في فحص الرخصة: $e',
        'is_dev': null,
        'serial': null,
      };
    }
  }
  

  
  /// إعادة تعيين قيمة is_dev (للاختبار)
  static void resetIsDevValue() {
    _isDevValue = null;
  }
  
  /// إعادة تعيين قيمة serial (للاختبار)
  static void resetSerialValue() {
    _serialValue = null;
  }
  
  /// إعادة تعيين جميع القيم (للاختبار)
  static void resetAllValues() {
    _isDevValue = null;
    _serialValue = null;
  }
  
  /// التحقق من أن المستخدم مطور (is_dev = 1)
  static bool get isDeveloper => _isDevValue == 1;
  
  /// التحقق من أن المستخدم مستخدم عادي (is_dev = 0)
  static bool get isRegularUser => _isDevValue == 0;
  
  /// الحصول على نوع المستخدم كنص
  static String get userTypeText {
    if (_isDevValue == null) return 'غير محدد';
    return _isDevValue == 1 ? 'مطور' : 'مستخدم عادي';
  }
}