import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import 'aluminum/aluminum_quotations_screen.dart' as aluminum;
// import 'upvc/upvc_quotations_screen.dart' as upvc;

class DesignServicesScreen extends StatelessWidget {
  const DesignServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('خدمات التصميم المتخصصة'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE8F5E8),
              Color(0xFFE3F2FD),
              Color(0xFFFFF8E1),
              Color(0xFFF3E5F5),
            ],
            stops: [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(isTablet ? 20 : 16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE65100),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFE65100).withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.design_services,
                        size: isTablet ? 32 : 28,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: isTablet ? 16 : 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'خدمات التصميم المتخصصة',
                            style: TextStyle(
                              fontSize: isTablet ? 24 : 20,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2E7D32),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'تخصيمات المطابخ والألومنيوم وuPVC',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: isTablet ? 32 : 24),

              // Design Services Grid
              Expanded(
                child: _buildDesignServicesGrid(isTablet, localizations),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesignServicesGrid(bool isTablet, AppLocalizations localizations) {
    final designServices = [
      {
        'title': localizations.kitchenDesign,
        'icon': Icons.kitchen,
        'color': const Color(0xFFE65100),
        'description': localizations.kitchenDesignDesc,
        'features': [
          'تخصيم مطابخ حديثة',
          'اختيار الخامات والألوان',
          'تحسين استغلال المساحة',
          'تخصيم ثلاثي الأبعاد',
        ],
      },
      {
        'title': localizations.aluminumDesign,
        'icon': Icons.view_quilt,
        'color': const Color(0xFF607D8B),
        'description': localizations.aluminumDesignDesc,
        'features': [
          'تخصيم أعمال الألومنيوم',
          'حساب الكميات والتكاليف',
          'اختيار الألوان والتشطيبات',
          'تخصيم الواجهات والنوافذ',
        ],
      },
      {
        'title': localizations.upvcDesign,
        'icon': Icons.door_front_door,
        'color': const Color(0xFF795548),
        'description': localizations.upvcDesignDesc,
        'features': [
          'تخصيم أعمال uPVC',
          'حساب المواد والتكاليف',
          'اختيار الألوان والأشكال',
          'حلول العزل والأمان',
        ],
      },
    ];

    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount;
        double childAspectRatio;

        if (constraints.maxWidth > 1200) {
          crossAxisCount = 3;
          childAspectRatio = 0.9;
        } else if (constraints.maxWidth > 800) {
          crossAxisCount = 2;
          childAspectRatio = 1.0;
        } else {
          crossAxisCount = 1;
          childAspectRatio = 1.2;
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: designServices.length,
          itemBuilder: (context, index) {
            final service = designServices[index];
            return _buildDesignServiceCard(
              title: service['title'] as String,
              icon: service['icon'] as IconData,
              color: service['color'] as Color,
              description: service['description'] as String,
              features: service['features'] as List<String>,
              onTap: () => _handleServiceTap(
                context,
                service['title'] as String,
                service['description'] as String,
                service['features'] as List<String>,
              ),
              isTablet: isTablet,
            );
          },
        );
      },
    );
  }

  Widget _buildDesignServiceCard({
    required String title,
    required IconData icon,
    required Color color,
    required String description,
    required List<String> features,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.all(isTablet ? 24 : 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
                Colors.white,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isTablet ? 16 : 12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      size: isTablet ? 32 : 28,
                      color: color,
                    ),
                  ),
                  SizedBox(width: isTablet ? 16 : 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: isTablet ? 20 : 18,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2E7D32),
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: isTablet ? 16 : 12),

              // Description
              Text(
                description,
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  color: Colors.grey[700],
                  height: 1.4,
                ),
              ),

              SizedBox(height: isTablet ? 20 : 16),

              // Features
              Text(
                'الميزات المتاحة:',
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E7D32),
                ),
              ),

              SizedBox(height: isTablet ? 12 : 8),

              Expanded(
                child: ListView.builder(
                  itemCount: features.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              features[index],
                              style: TextStyle(
                                fontSize: isTablet ? 14 : 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // Action button
              SizedBox(height: isTablet ? 16 : 12),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  vertical: isTablet ? 12 : 10,
                  horizontal: isTablet ? 16 : 12,
                ),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.touch_app,
                      size: isTablet ? 20 : 18,
                      color: color,
                    ),
                    SizedBox(width: isTablet ? 8 : 6),
                    Text(
                      'اضغط للمزيد',
                      style: TextStyle(
                        fontSize: isTablet ? 14 : 12,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleServiceTap(BuildContext context, String title, String description, List<String> features) {
    final localizations = AppLocalizations.of(context)!;

    // Check if this is aluminum design service
    if (title == localizations.aluminumDesign) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const aluminum.AluminumQuotationsScreen(),
        ),
      );
    }
    // Check if this is uPVC design service
    else if (title == localizations.upvcDesign) {
      _showComingSoonDialog(context, title, description, features);
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => const upvc.UpvcQuotationsScreen(),
      //   ),
      // );
    }
    else {
      _showComingSoonDialog(context, title, description, features);
    }
  }

  void _showComingSoonDialog(BuildContext context, String title, String description, List<String> features) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.design_services,
                color: const Color(0xFFE65100),
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                description,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[700],
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'الميزات المخطط لها:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E7D32),
                ),
              ),
              const SizedBox(height: 8),
              ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 16,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.construction,
                      color: Colors.orange[700],
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'هذه الخدمة قيد التطوير وستكون متاحة قريباً',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFF2E7D32),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text(
                'حسناً',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        );
      },
    );
  }
}
