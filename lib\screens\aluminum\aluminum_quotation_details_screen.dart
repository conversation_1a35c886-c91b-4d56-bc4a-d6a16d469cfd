// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../l10n/app_localizations.dart';
import '../../models/aluminum_quotation.dart';
import '../../models/aluminum_quotation_item.dart';
import '../../services/unified_aluminum_service.dart';
import '../../widgets/drawing_options_dialog.dart';
import 'add_edit_aluminum_quotation_screen.dart';
import 'add_edit_aluminum_quotation_item_screen.dart';

class AluminumQuotationDetailsScreen extends StatefulWidget {
  final AluminumQuotation quotation;

  const AluminumQuotationDetailsScreen({super.key, required this.quotation});

  @override
  State<AluminumQuotationDetailsScreen> createState() => _AluminumQuotationDetailsScreenState();
}

class _AluminumQuotationDetailsScreenState extends State<AluminumQuotationDetailsScreen> {
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();
  List<AluminumQuotationItem> _items = [];
  bool _isLoading = true;
  AluminumQuotationItem? _selectedItem; // البند المحدد لعرض الرسمة

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  Future<void> _loadItems() async {
    setState(() => _isLoading = true);
    try {
      final itemsData = await _aluminumService.getQuotationItems(widget.quotation.id!);
      final items = itemsData.map((data) => AluminumQuotationItem.fromMap(data)).toList();
      setState(() {
        _items = items;
        _isLoading = false;
        // تحديد البند الأول تلقائياً إذا كان متاحاً
        if (items.isNotEmpty && _selectedItem == null) {
          _selectedItem = items.first;
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البنود: $e')),
        );
      }
    }
  }

  Future<void> _addItem() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditAluminumQuotationItemScreen(quotation: widget.quotation),
      ),
    );
    if (result == true) {
      _loadItems();
    }
  }

  Future<void> _editItem(AluminumQuotationItem item) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditAluminumQuotationItemScreen(
          quotation: widget.quotation,
          item: item,
        ),
      ),
    );
    if (result == true) {
      _loadItems();
    }
  }

  Future<void> _deleteItem(AluminumQuotationItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا البند؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _aluminumService.deleteQuotationItem(item.id!);
        _loadItems();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف البند بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف البند: $e')),
          );
        }
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text('مقايسة رقم ${widget.quotation.quotationNumber}'),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final result = await navigator.push(
                MaterialPageRoute(
                  builder: (context) => AddEditAluminumQuotationScreen(quotation: widget.quotation),
                ),
              );
              if (result == true && mounted) {
                navigator.pop(true);
              }
            },
            icon: const Icon(Icons.edit),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Column(
          children: [
            // Quotation Info Card - مصغر
            Container(
              margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              child: Card(
                elevation: 1,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.quotation.clientName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                          const SizedBox(width: 6),
                          Text(
                            '${widget.quotation.quotationDate.day}/${widget.quotation.quotationDate.month}/${widget.quotation.quotationDate.year}',
                            style: TextStyle(color: Colors.grey[600], fontSize: 12),
                          ),
                          if (widget.quotation.clientPhone.isNotEmpty) ...[
                            const SizedBox(width: 16),
                            Icon(Icons.phone, size: 14, color: Colors.grey[600]),
                            const SizedBox(width: 6),
                            Text(
                              widget.quotation.clientPhone,
                              style: TextStyle(color: Colors.grey[600], fontSize: 12),
                            ),
                          ],
                        ],
                      ),
                      if (widget.quotation.notes.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          widget.quotation.notes,
                          style: TextStyle(
                            color: Colors.grey[700],
                            fontStyle: FontStyle.italic,
                            fontSize: 11,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Main Content - Split into two sections
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final isTablet = constraints.maxWidth > 800;
                    final isLargeTablet = constraints.maxWidth > 1200;

                    if (isTablet) {
                      // Tablet layout - side by side
                      return Row(
                        children: [
                          // Right side - Items list
                          Expanded(
                            flex: isLargeTablet ? 2 : 1,
                            child: _buildItemsSection(localizations),
                          ),
                          const SizedBox(width: 16),
                          // Left side - 3D Drawing
                          Expanded(
                            flex: isLargeTablet ? 3 : 1,
                            child: _buildDrawingSection(),
                          ),
                        ],
                      );
                    } else {
                      // Mobile layout - stacked
                      return Column(
                        children: [
                          // Items list (حجم مناسب لعرض بند واحد)
                          Expanded(
                            flex: 2,
                            child: _buildItemsSection(localizations),
                          ),
                          const SizedBox(height: 6),
                          // 3D Drawing (أكبر على الموبايل)
                          Expanded(
                            flex: 5,
                            child: _buildDrawingSection(),
                          ),
                        ],
                      );
                    }
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // بناء قسم البنود
  Widget _buildItemsSection(AppLocalizations localizations) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              color: Color(0xFF607D8B),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.list, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  localizations.quotationItems,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add, color: Colors.white, size: 18),
                  padding: const EdgeInsets.all(2),
                  constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _items.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.inbox_outlined,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد بنود في المقايسة',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(8),
                        itemCount: _items.length,
                        itemBuilder: (context, index) {
                          final item = _items[index];
                          return _buildItemCard(item);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  // بناء قسم الرسم ثلاثي الأبعاد
  Widget _buildDrawingSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Color(0xFF607D8B),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.view_in_ar, color: Colors.white, size: 18),
                SizedBox(width: 6),
                Text(
                  'رسمة البند',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _selectedItem == null
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.touch_app,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'اختر بند لعرض الرسمة',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(
                    padding: const EdgeInsets.all(16),
                    child: _build3DWindow(_selectedItem!),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemCard(AluminumQuotationItem item) {
    final isSelected = _selectedItem?.id == item.id;

    return Card(
      margin: const EdgeInsets.only(bottom: 4),
      elevation: isSelected ? 4 : 1,
      color: isSelected ? const Color(0xFF607D8B).withValues(alpha: 0.1) : null,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedItem = item;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTypeColor(item.type),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item.type.arabicName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (isSelected) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'محدد',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        _editItem(item);
                      } else if (value == 'delete') {
                        _deleteItem(item);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                      const PopupMenuItem(value: 'delete', child: Text('حذف')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  Text(
                    'المقاس: ${_formatNumber(item.width)} × ${_formatNumber(item.height)} سم',
                    style: const TextStyle(fontSize: 11),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'الكمية: ${item.quantity}',
                    style: const TextStyle(fontSize: 11),
                  ),
                ],
              ),
              if (item.sashCount != SashCount.one) ...[
                const SizedBox(height: 2),
                Text(
                  'عدد الضلف: ${item.sashCount.arabicName}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 10,
                  ),
                ),
              ],
              if (item.trackCount != null) ...[
                const SizedBox(height: 4),
                Text(
                  'عدد السكك: ${item.trackCount!.arabicName}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
              if (item.notes.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'ملاحظات: ${item.notes}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // رسم الشباك ثلاثي الأبعاد
  Widget _build3DWindow(AluminumQuotationItem item) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // معلومات البند
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      item.type == WindowDoorType.hinge ? Icons.door_front_door : Icons.view_column,
                      color: _getTypeColor(item.type),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${item.type.arabicName} - ${item.sashCount.arabicName}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    if (item.trackCount != null) ...[
                      Text(
                        '(${item.trackCount!.arabicName})',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    // زر خيارات الرسمة
                    IconButton(
                      onPressed: () => _showDrawingOptionsDialog(item),
                      icon: const Icon(Icons.settings, size: 20),
                      tooltip: 'خيارات الرسمة',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
              ),
              // الرسمة
              Expanded(
                child: CustomPaint(
                  size: Size(constraints.maxWidth, constraints.maxHeight - 50),
                  painter: Window3DPainter(item),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // دالة تنسيق الأرقام لعرض الأرقام العشرية بشكل صحيح
  String _formatNumber(double number) {
    if (number == number.roundToDouble()) {
      return number.toInt().toString();
    } else {
      return number.toString();
    }
  }

  // عرض نافذة خيارات الرسمة
  void _showDrawingOptionsDialog(AluminumQuotationItem item) {
    showDialog(
      context: context,
      builder: (context) => DrawingOptionsDialog(
        item: item,
        onSave: (updatedItem) async {
          try {
            await _aluminumService.updateQuotationItem(updatedItem);

            // تحديث القائمة والرسمة فوراً
            setState(() {
              final index = _items.indexWhere((i) => i.id == item.id);
              if (index != -1) {
                _items[index] = updatedItem;
                if (_selectedItem?.id == item.id) {
                  _selectedItem = updatedItem;
                }
              }
            });

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حفظ خيارات الرسمة بنجاح')),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في حفظ الخيارات: $e')),
              );
            }
          }
        },
      ),
    );
  }

  Color _getTypeColor(WindowDoorType type) {
    switch (type) {
      case WindowDoorType.hinge:
        return Colors.blue;
      case WindowDoorType.sliding:
        return Colors.teal;
    }
  }
}

// رسام الشباك ثلاثي الأبعاد
class Window3DPainter extends CustomPainter {
  final AluminumQuotationItem item;

  Window3DPainter(this.item);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill;

    // قص الرسمة داخل حدود الإطار
    canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // حساب أبعاد الثوابت فقط إذا كانت موجودة
    final fixedPanels = item.drawingOptions.fixedPanels;

    // حساب المساحة المطلوبة للثوابت الجانبية فقط إذا كانت موجودة
    final leftFixedWidth = fixedPanels.hasLeft ? fixedPanels.leftSize * 2 : 0.0;
    final rightFixedWidth = fixedPanels.hasRight ? fixedPanels.rightSize * 2 : 0.0;
    final totalFixedWidth = leftFixedWidth + rightFixedWidth;

    // حساب المساحة المطلوبة للثوابت العمودية فقط إذا كانت موجودة
    final topFixedHeight = fixedPanels.hasTop ? fixedPanels.topSize * 2 : 0.0;
    final bottomFixedHeight = fixedPanels.hasBottom ? fixedPanels.bottomSize * 2 : 0.0;
    final totalFixedHeight = topFixedHeight + bottomFixedHeight;

    // حساب المساحة المتاحة للشباك (بدون مسافات إضافية بين الشباك والثوابت)
    final margin = 20.0;

    final availableWidth = size.width - (margin * 2) - totalFixedWidth;
    final availableHeight = size.height - (margin * 2) - totalFixedHeight - 40; // مساحة للمقاسات فقط

    // التأكد من أن المساحة المتاحة موجبة مع حد أدنى للرسمة
    if (availableWidth <= 80 || availableHeight <= 80) return;

    // أبعاد ثابتة تماماً - لا تتغير أبداً مهما كان حجم الشاشة
    const drawWidth = 200.0; // عرض ثابت
    const drawHeight = 150.0; // ارتفاع ثابت

    // موضع الرسم في المنتصف
    final startX = (size.width - drawWidth) / 2;
    final startY = (size.height - drawHeight) / 2;

    // لا نرسم أي خلفية أو ظلال

    // رسم الضلف حسب النوع
    if (item.type == WindowDoorType.hinge) {
      _drawHingeWindow(canvas, startX, startY, drawWidth, drawHeight, paint, fillPaint);
    } else {
      _drawSlidingWindow(canvas, startX, startY, drawWidth, drawHeight, paint, fillPaint);
    }

    // رسم الثوابت المتعددة
    _drawFixedPanels(canvas, startX, startY, drawWidth, drawHeight, paint, fillPaint, size);

    // رسم مقاسات البند (العرض والارتفاع)
    _drawItemDimensions(canvas, startX, startY, drawWidth, drawHeight, paint, fillPaint);
  }

  void _drawHingeWindow(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint) {
    paint.strokeWidth = 2.0;
    paint.color = Colors.brown;

    switch (item.sashCount) {
      case SashCount.one:
        // ضلفة واحدة
        _drawSingleSash(canvas, startX, startY, width, height, paint, fillPaint);
        break;
      case SashCount.two:
        // ضلفتين
        _drawDoubleSash(canvas, startX, startY, width, height, paint, fillPaint);
        break;
      case SashCount.three:
      case SashCount.four:
      case SashCount.six:
        // ضلف متعددة
        _drawMultipleSash(canvas, startX, startY, width, height, paint, fillPaint, item.sashCount.count);
        break;
    }
  }

  void _drawSlidingWindow(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint) {
    // رسم الحلق كأربعة أجزاء منفصلة للسحاب
    _drawSlidingFrameParts(canvas, startX, startY, width, height, paint, fillPaint);

    // رسم الضلف السحاب
    _drawSlidingSashParts(canvas, startX, startY, width, height, paint, fillPaint);
  }

  // رسم أجزاء الحلق الأربعة للشبابيك السحاب
  void _drawSlidingFrameParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    final frameThickness = 12.0; // سمك الحلق

    paint.color = Colors.teal.shade300;
    fillPaint.color = Colors.teal.shade300;

    // الجزء العلوي
    canvas.drawRect(
      Rect.fromLTWH(x, y, width, frameThickness),
      fillPaint,
    );

    // الجزء السفلي
    canvas.drawRect(
      Rect.fromLTWH(x, y + height - frameThickness, width, frameThickness),
      fillPaint,
    );

    // الجزء الأيسر
    canvas.drawRect(
      Rect.fromLTWH(x, y + frameThickness, frameThickness, height - (frameThickness * 2)),
      fillPaint,
    );

    // الجزء الأيمن
    canvas.drawRect(
      Rect.fromLTWH(x + width - frameThickness, y + frameThickness, frameThickness, height - (frameThickness * 2)),
      fillPaint,
    );

    // رسم الحدود الخارجية
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0;
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح قريب من البيج

    // حدود الجزء العلوي
    canvas.drawRect(Rect.fromLTWH(x, y, width, frameThickness), paint);

    // حدود الجزء السفلي
    canvas.drawRect(Rect.fromLTWH(x, y + height - frameThickness, width, frameThickness), paint);

    // حدود الجزء الأيسر
    canvas.drawRect(Rect.fromLTWH(x, y + frameThickness, frameThickness, height - (frameThickness * 2)), paint);

    // حدود الجزء الأيمن
    canvas.drawRect(Rect.fromLTWH(x + width - frameThickness, y + frameThickness, frameThickness, height - (frameThickness * 2)), paint);

    paint.style = PaintingStyle.fill;

    // رسم السكك
    if (item.trackCount != null) {
      paint.color = Colors.grey.shade400;
      paint.strokeWidth = 3.0;

      if (item.trackCount == TrackCount.two) {
        // سكتين
        canvas.drawLine(
          Offset(x, y - 8),
          Offset(x + width, y - 8),
          paint,
        );
        canvas.drawLine(
          Offset(x, y + height + 8),
          Offset(x + width, y + height + 8),
          paint,
        );
      } else {
        // سكة واحدة
        canvas.drawLine(
          Offset(x, y - 5),
          Offset(x + width, y - 5),
          paint,
        );
        canvas.drawLine(
          Offset(x, y + height + 5),
          Offset(x + width, y + height + 5),
          paint,
        );
      }
    }
  }

  // رسم ضلف السحاب
  void _drawSlidingSashParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    final frameThickness = 12.0; // سمك الحلق
    final sashThickness = 8.0; // سمك الضلفة
    final gap = 3.0; // المسافة بين الحلق والضلفة
    final overlapWidth = 15.0; // عرض التداخل بين الضلف

    // حساب موضع الضلف داخل الحلق
    final sashX = x + frameThickness + gap;
    final sashY = y + frameThickness + gap;
    final sashWidth = width - (frameThickness * 2) - (gap * 2);
    final sashHeight = height - (frameThickness * 2) - (gap * 2);

    // عرض كل ضلفة (مع التداخل)
    final singleSashWidth = (sashWidth + overlapWidth) / 2;

    // رسم الضلفة الخلفية (اليسرى)
    _drawSlidingSingleSashFrame(canvas, sashX, sashY, singleSashWidth, sashHeight, paint, fillPaint, sashThickness, Colors.teal.shade100);

    // رسم الضلفة الأمامية (اليمنى) مع التداخل
    _drawSlidingSingleSashFrame(canvas, sashX + singleSashWidth - overlapWidth, sashY, singleSashWidth, sashHeight, paint, fillPaint, sashThickness, Colors.teal.shade200);
  }

  // رسم ضلفة سحاب واحدة كأربعة أجزاء
  void _drawSlidingSingleSashFrame(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint, double thickness, Color sashColor) {
    // لون الضلفة
    paint.color = sashColor;
    fillPaint.color = sashColor;

    // الجزء العلوي للضلفة
    canvas.drawRect(
      Rect.fromLTWH(x, y, width, thickness),
      fillPaint,
    );

    // الجزء السفلي للضلفة
    canvas.drawRect(
      Rect.fromLTWH(x, y + height - thickness, width, thickness),
      fillPaint,
    );

    // الجزء الأيسر للضلفة
    canvas.drawRect(
      Rect.fromLTWH(x, y + thickness, thickness, height - (thickness * 2)),
      fillPaint,
    );

    // الجزء الأيمن للضلفة
    canvas.drawRect(
      Rect.fromLTWH(x + width - thickness, y + thickness, thickness, height - (thickness * 2)),
      fillPaint,
    );

    // رسم الحدود للضلفة
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5;
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح قريب من البيج

    // حدود الجزء العلوي
    canvas.drawRect(Rect.fromLTWH(x, y, width, thickness), paint);

    // حدود الجزء السفلي
    canvas.drawRect(Rect.fromLTWH(x, y + height - thickness, width, thickness), paint);

    // حدود الجزء الأيسر
    canvas.drawRect(Rect.fromLTWH(x, y + thickness, thickness, height - (thickness * 2)), paint);

    // حدود الجزء الأيمن
    canvas.drawRect(Rect.fromLTWH(x + width - thickness, y + thickness, thickness, height - (thickness * 2)), paint);

    paint.style = PaintingStyle.fill;
  }

  void _drawSingleSash(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint) {
    // رسم الحلق كأربعة أجزاء منفصلة
    _drawFrameParts(canvas, startX, startY, width, height, paint, fillPaint);

    // رسم الضلفة داخل الحلق
    _drawSashParts(canvas, startX, startY, width, height, paint, fillPaint);
  }

  // رسم أجزاء الحلق الأربعة بقص 45 درجة (ميتر)
  void _drawFrameParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    // سمك الحلق ثابت
    final frameThickness = 12.0; // سمك ثابت

    paint.color = const Color(0xFFF5F5DC); // لون بيج
    fillPaint.color = const Color(0xFFF5F5DC); // لون بيج

    // رسم الحلق بقص 45 درجة
    _drawMiterFramePart(canvas, x, y, width, height, frameThickness, paint, fillPaint);

    paint.style = PaintingStyle.fill;
  }

  // رسم جزء من الحلق بقص 45 درجة (ميتر) - محسن
  void _drawMiterFramePart(Canvas canvas, double x, double y, double width, double height, double thickness, Paint paint, Paint fillPaint) {
    // الجزء العلوي - بقص 45 درجة صحيح في الزوايا
    final topPath = Path();
    topPath.moveTo(x, y); // الزاوية العلوية اليسرى الخارجية
    topPath.lineTo(x + width, y); // الحافة العلوية الخارجية
    topPath.lineTo(x + width - thickness, y + thickness); // قص 45 درجة للزاوية اليمنى
    topPath.lineTo(x + thickness, y + thickness); // الحافة السفلية الداخلية
    topPath.close();
    canvas.drawPath(topPath, fillPaint);

    // الجزء السفلي - بقص 45 درجة صحيح في الزوايا
    final bottomPath = Path();
    bottomPath.moveTo(x, y + height); // الزاوية السفلية اليسرى الخارجية
    bottomPath.lineTo(x + thickness, y + height - thickness); // قص 45 درجة للزاوية اليسرى
    bottomPath.lineTo(x + width - thickness, y + height - thickness); // الحافة العلوية الداخلية
    bottomPath.lineTo(x + width, y + height); // الزاوية السفلية اليمنى الخارجية
    bottomPath.close();
    canvas.drawPath(bottomPath, fillPaint);

    // الجزء الأيسر - بقص 45 درجة صحيح في الزوايا
    final leftPath = Path();
    leftPath.moveTo(x, y); // الزاوية العلوية اليسرى الخارجية
    leftPath.lineTo(x + thickness, y + thickness); // قص 45 درجة للزاوية العلوية
    leftPath.lineTo(x + thickness, y + height - thickness); // الحافة الداخلية
    leftPath.lineTo(x, y + height); // الزاوية السفلية اليسرى الخارجية
    leftPath.close();
    canvas.drawPath(leftPath, fillPaint);

    // الجزء الأيمن - بقص 45 درجة صحيح في الزوايا
    final rightPath = Path();
    rightPath.moveTo(x + width, y); // الزاوية العلوية اليمنى الخارجية
    rightPath.lineTo(x + width, y + height); // الحافة الخارجية
    rightPath.lineTo(x + width - thickness, y + height - thickness); // قص 45 درجة للزاوية السفلى
    rightPath.lineTo(x + width - thickness, y + thickness); // قص 45 درجة للزاوية العلوية
    rightPath.close();
    canvas.drawPath(rightPath, fillPaint);

    // رسم الحدود بقص 45 درجة
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 2.0; // سمك ثابت
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح قريب من البيج

    canvas.drawPath(topPath, paint);
    canvas.drawPath(bottomPath, paint);
    canvas.drawPath(leftPath, paint);
    canvas.drawPath(rightPath, paint);
  }

  // رسم أجزاء الضلفة الأربعة بقص 45 درجة (ميتر) مع الباكتة والزجاج
  void _drawSashParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    // أحجام ثابتة
    final frameThickness = 12.0; // سمك ثابت
    final sashThickness = 10.0; // سمك ثابت
    final overlap = 2.0; // تداخل ثابت

    // حساب موضع الضلفة (تركب على الحلق قليلاً)
    final sashX = x + frameThickness - overlap;
    final sashY = y + frameThickness - overlap;
    final sashWidth = width - (frameThickness * 2) + (overlap * 2);
    final sashHeight = height - (frameThickness * 2) + (overlap * 2);

    // لون الضلفة (بيج فاتح)
    paint.color = const Color(0xFFFAF0E6); // لون بيج فاتح
    fillPaint.color = const Color(0xFFFAF0E6); // لون بيج فاتح

    // رسم الضلفة بقص 45 درجة
    _drawMiterSashPart(canvas, sashX, sashY, sashWidth, sashHeight, sashThickness, paint, fillPaint);

    paint.style = PaintingStyle.fill;

    // رسم الباكتة والزجاج داخل الضلفة
    _drawSashGlazingBead(canvas, sashX, sashY, sashWidth, sashHeight, sashThickness, paint, fillPaint);

    // رسم المفصلات والمقبض للضلفة الواحدة
    _drawHingesAndHandle(canvas, sashX, sashY, sashWidth, sashHeight, paint, fillPaint, 1);
  }

  // رسم الباكتة (بقص 90 درجة) والزجاج داخل الضلفة
  void _drawSashGlazingBead(Canvas canvas, double sashX, double sashY, double sashWidth, double sashHeight, double sashThickness, Paint paint, Paint fillPaint) {
    // أحجام ثابتة للباكتة
    final beadThickness = 4.0; // سمك ثابت
    final gap = 3.0; // مسافة ثابتة

    // حساب منطقة الباكتة (داخل الضلفة مع مسافة)
    final beadX = sashX + sashThickness + gap;
    final beadY = sashY + sashThickness + gap;
    final beadWidth = sashWidth - (sashThickness * 2) - (gap * 2);
    final beadHeight = sashHeight - (sashThickness * 2) - (gap * 2);

    // التأكد من أن منطقة الباكتة موجبة
    if (beadWidth <= beadThickness * 2 || beadHeight <= beadThickness * 2) return;

    // رسم الباكتة باستخدام نفس الدالة المستخدمة في الثابت
    _drawBeadFrame(canvas, beadX, beadY, beadWidth, beadHeight, paint, fillPaint, beadThickness);

    // رسم الزجاج داخل الباكتة باستخدام نفس الدالة المستخدمة في الثابت
    final glassX = beadX + beadThickness;
    final glassY = beadY + beadThickness;
    final glassWidth = beadWidth - (beadThickness * 2);
    final glassHeight = beadHeight - (beadThickness * 2);

    if (glassWidth > 0 && glassHeight > 0) {
      _drawSingleGlassPane(canvas, glassX, glassY, glassWidth, glassHeight, paint, fillPaint);
    }
  }

  // رسم جزء من الضلفة بقص 45 درجة (ميتر) - محسن
  void _drawMiterSashPart(Canvas canvas, double x, double y, double width, double height, double thickness, Paint paint, Paint fillPaint) {
    // الجزء العلوي - بقص 45 درجة صحيح في الزوايا
    final topPath = Path();
    topPath.moveTo(x, y); // الزاوية العلوية اليسرى الخارجية
    topPath.lineTo(x + width, y); // الحافة العلوية الخارجية
    topPath.lineTo(x + width - thickness, y + thickness); // قص 45 درجة للزاوية اليمنى
    topPath.lineTo(x + thickness, y + thickness); // الحافة السفلية الداخلية
    topPath.close();
    canvas.drawPath(topPath, fillPaint);

    // الجزء السفلي - بقص 45 درجة صحيح في الزوايا
    final bottomPath = Path();
    bottomPath.moveTo(x, y + height); // الزاوية السفلية اليسرى الخارجية
    bottomPath.lineTo(x + thickness, y + height - thickness); // قص 45 درجة للزاوية اليسرى
    bottomPath.lineTo(x + width - thickness, y + height - thickness); // الحافة العلوية الداخلية
    bottomPath.lineTo(x + width, y + height); // الزاوية السفلية اليمنى الخارجية
    bottomPath.close();
    canvas.drawPath(bottomPath, fillPaint);

    // الجزء الأيسر - بقص 45 درجة صحيح في الزوايا
    final leftPath = Path();
    leftPath.moveTo(x, y); // الزاوية العلوية اليسرى الخارجية
    leftPath.lineTo(x + thickness, y + thickness); // قص 45 درجة للزاوية العلوية
    leftPath.lineTo(x + thickness, y + height - thickness); // الحافة الداخلية
    leftPath.lineTo(x, y + height); // الزاوية السفلية اليسرى الخارجية
    leftPath.close();
    canvas.drawPath(leftPath, fillPaint);

    // الجزء الأيمن - بقص 45 درجة صحيح في الزوايا
    final rightPath = Path();
    rightPath.moveTo(x + width, y); // الزاوية العلوية اليمنى الخارجية
    rightPath.lineTo(x + width, y + height); // الحافة الخارجية
    rightPath.lineTo(x + width - thickness, y + height - thickness); // قص 45 درجة للزاوية السفلى
    rightPath.lineTo(x + width - thickness, y + thickness); // قص 45 درجة للزاوية العلوية
    rightPath.close();
    canvas.drawPath(rightPath, fillPaint);

    // رسم الحدود بقص 45 درجة
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.5; // سمك ثابت
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح قريب من البيج

    canvas.drawPath(topPath, paint);
    canvas.drawPath(bottomPath, paint);
    canvas.drawPath(leftPath, paint);
    canvas.drawPath(rightPath, paint);
  }

  void _drawDoubleSash(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint) {
    // رسم الحلق كأربعة أجزاء منفصلة
    _drawFrameParts(canvas, startX, startY, width, height, paint, fillPaint);

    // رسم ضلفتين جنباً إلى جنب
    _drawDoubleSashParts(canvas, startX, startY, width, height, paint, fillPaint);
  }

  void _drawMultipleSash(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint, int sashCount) {
    // رسم الحلق كأربعة أجزاء منفصلة
    _drawFrameParts(canvas, startX, startY, width, height, paint, fillPaint);

    // رسم ضلف متعددة
    _drawMultipleSashParts(canvas, startX, startY, width, height, paint, fillPaint, sashCount);
  }

  // رسم ضلفتين جنباً إلى جنب
  void _drawDoubleSashParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    // أحجام ثابتة
    final frameThickness = 12.0; // سمك ثابت
    final sashThickness = 10.0; // سمك ثابت
    final overlap = 2.0; // تداخل ثابت
    final dividerWidth = 6.0; // عرض فاصل ثابت

    // حساب موضع الضلف (تركب على الحلق قليلاً)
    final sashX = x + frameThickness - overlap;
    final sashY = y + frameThickness - overlap;
    final sashWidth = width - (frameThickness * 2) + (overlap * 2);
    final sashHeight = height - (frameThickness * 2) + (overlap * 2);

    // عرض كل ضلفة
    final singleSashWidth = (sashWidth - dividerWidth) / 2;

    // رسم الضلفة اليسرى
    _drawSingleSashFrame(canvas, sashX, sashY, singleSashWidth, sashHeight, paint, fillPaint, sashThickness);

    // رسم الضلفة اليمنى
    _drawSingleSashFrame(canvas, sashX + singleSashWidth + dividerWidth, sashY, singleSashWidth, sashHeight, paint, fillPaint, sashThickness);

    // رسم الفاصل الأوسط
    paint.color = const Color(0xFFF5F5DC); // لون بيج
    fillPaint.color = const Color(0xFFF5F5DC); // لون بيج
    canvas.drawRect(
      Rect.fromLTWH(sashX + singleSashWidth, sashY, dividerWidth, sashHeight),
      fillPaint,
    );

    // رسم الزجاج للضلفتين
    _drawDoubleGlass(canvas, sashX, sashY, sashWidth, sashHeight, paint, fillPaint);

    // رسم المفصلات والمقبض للضلفتين
    _drawHingesAndHandle(canvas, sashX, sashY, sashWidth, sashHeight, paint, fillPaint, 2);
  }

  // رسم ضلف متعددة
  void _drawMultipleSashParts(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint, int sashCount) {
    // أحجام ثابتة
    final frameThickness = 12.0; // سمك ثابت
    final sashThickness = 10.0; // سمك ثابت
    final overlap = 2.0; // تداخل ثابت
    final dividerWidth = 6.0; // عرض فاصل ثابت

    // حساب موضع الضلف (تركب على الحلق قليلاً)
    final sashX = x + frameThickness - overlap;
    final sashY = y + frameThickness - overlap;
    final sashWidth = width - (frameThickness * 2) + (overlap * 2);
    final sashHeight = height - (frameThickness * 2) + (overlap * 2);

    // عرض كل ضلفة
    final singleSashWidth = (sashWidth - (dividerWidth * (sashCount - 1))) / sashCount;

    // رسم كل ضلفة
    for (int i = 0; i < sashCount; i++) {
      final currentX = sashX + (i * (singleSashWidth + dividerWidth));
      _drawSingleSashFrame(canvas, currentX, sashY, singleSashWidth, sashHeight, paint, fillPaint, sashThickness);

      // رسم الفاصل (إلا للضلفة الأخيرة)
      if (i < sashCount - 1) {
        paint.color = const Color(0xFFF5F5DC); // لون بيج
        fillPaint.color = const Color(0xFFF5F5DC); // لون بيج
        canvas.drawRect(
          Rect.fromLTWH(currentX + singleSashWidth, sashY, dividerWidth, sashHeight),
          fillPaint,
        );
      }
    }

    // رسم الزجاج للضلف المتعددة
    _drawMultipleGlass(canvas, sashX, sashY, sashWidth, sashHeight, paint, fillPaint, sashCount);

    // رسم المفصلات والمقبض للضلف المتعددة
    _drawHingesAndHandle(canvas, sashX, sashY, sashWidth, sashHeight, paint, fillPaint, sashCount);
  }

  // رسم ضلفة واحدة كأربعة أجزاء بقص 45 درجة (ميتر) مع الباكتة والزجاج
  void _drawSingleSashFrame(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint, double thickness) {
    // لون الضلفة (بيج فاتح)
    paint.color = const Color(0xFFFAF0E6); // لون بيج فاتح
    fillPaint.color = const Color(0xFFFAF0E6); // لون بيج فاتح

    // رسم الضلفة بقص 45 درجة
    _drawMiterSashPart(canvas, x, y, width, height, thickness, paint, fillPaint);

    paint.style = PaintingStyle.fill;

    // رسم الباكتة والزجاج داخل الضلفة
    _drawSashGlazingBead(canvas, x, y, width, height, thickness, paint, fillPaint);
  }

  // رسم المفصلات والمقبض
  void _drawHingesAndHandle(Canvas canvas, double sashX, double sashY, double sashWidth, double sashHeight, Paint paint, Paint fillPaint, int sashCount) {
    if (sashCount == 1) {
      // للضلفة الواحدة: مقبض على القائم الأيمن، مفصلات على القائم الأيسر
      _drawHinges(canvas, sashX, sashY, sashHeight, paint, fillPaint);
      _drawHandle(canvas, sashX + sashWidth, sashY + sashHeight / 2, paint, fillPaint);
    } else if (sashCount == 2) {
      // للضلفتين: مفصلات على الجوانب، مقبض على القائم الأوسط

      // مفصلات الضلفة اليسرى (على الجانب الأيسر)
      _drawHinges(canvas, sashX, sashY, sashHeight, paint, fillPaint);

      // مفصلات الضلفة اليمنى (على الجانب الأيمن)
      _drawHinges(canvas, sashX + sashWidth, sashY, sashHeight, paint, fillPaint);

      // مقبض على القائم الأوسط (الفاصل بين الضلفتين)
      _drawHandle(canvas, sashX + sashWidth / 2, sashY + sashHeight / 2, paint, fillPaint);
    } else {
      // للضلف المتعددة: مفصلات على الجوانب، مقبض على القائم الأوسط
      _drawHinges(canvas, sashX, sashY, sashHeight, paint, fillPaint);
      _drawHinges(canvas, sashX + sashWidth, sashY, sashHeight, paint, fillPaint);
      _drawHandle(canvas, sashX + sashWidth / 2, sashY + sashHeight / 2, paint, fillPaint);
    }
  }

  // رسم المفصلات
  void _drawHinges(Canvas canvas, double x, double y, double height, Paint paint, Paint fillPaint) {
    // أحجام ثابتة للمفصلات
    final hingeWidth = 10.0; // عرض ثابت
    final hingeHeight = 16.0; // ارتفاع ثابت
    final hingeColor = Colors.grey.shade600;

    paint.color = hingeColor;
    fillPaint.color = hingeColor;

    // المفصلة العلوية - موضع ثابت
    final topHingeOffset = 20.0; // مسافة ثابتة
    final topHingeY = y + topHingeOffset;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - hingeWidth/2, topHingeY, hingeWidth, hingeHeight),
        const Radius.circular(2),
      ),
      fillPaint,
    );

    // المفصلة السفلية - موضع ثابت
    final bottomHingeOffset = 20.0; // مسافة ثابتة
    final bottomHingeY = y + height - bottomHingeOffset - hingeHeight;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - hingeWidth/2, bottomHingeY, hingeWidth, hingeHeight),
        const Radius.circular(2),
      ),
      fillPaint,
    );

    // لا نرسم مفصلة وسطى - فقط مفصلتان علوية وسفلية

    // رسم حدود المفصلات
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    paint.color = Colors.grey.shade800;

    // حدود المفصلة العلوية
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - hingeWidth/2, topHingeY, hingeWidth, hingeHeight),
        const Radius.circular(2),
      ),
      paint,
    );

    // حدود المفصلة السفلية
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - hingeWidth/2, bottomHingeY, hingeWidth, hingeHeight),
        const Radius.circular(2),
      ),
      paint,
    );

    // لا نرسم حدود مفصلة وسطى - فقط حدود المفصلتين العلوية والسفلية

    paint.style = PaintingStyle.fill;
  }

  // رسم المقبض
  void _drawHandle(Canvas canvas, double x, double y, Paint paint, Paint fillPaint) {
    // أحجام ثابتة للمقبض
    final handleWidth = 12.0; // عرض ثابت
    final handleHeight = 24.0; // ارتفاع ثابت
    final handleColor = Colors.grey.shade700;

    paint.color = handleColor;
    fillPaint.color = handleColor;

    // رسم المقبض
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - handleWidth/2, y - handleHeight/2, handleWidth, handleHeight),
        const Radius.circular(3),
      ),
      fillPaint,
    );

    // رسم حدود المقبض
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    paint.color = Colors.grey.shade900;

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x - handleWidth/2, y - handleHeight/2, handleWidth, handleHeight),
        const Radius.circular(3),
      ),
      paint,
    );

    // رسم نقطة صغيرة في المنتصف
    paint.color = Colors.grey.shade400;
    canvas.drawCircle(Offset(x, y), 2.0, paint);

    paint.style = PaintingStyle.fill;
  }



  // رسم الزجاج للضلفتين
  void _drawDoubleGlass(Canvas canvas, double sashX, double sashY, double sashWidth, double sashHeight, Paint paint, Paint fillPaint) {
    // أحجام ثابتة
    final sashThickness = 10.0; // سمك ثابت
    final dividerWidth = 6.0; // عرض فاصل ثابت

    // عرض كل ضلفة
    final singleSashWidth = (sashWidth - dividerWidth) / 2;

    // رسم الباكتة والزجاج للضلفة اليسرى
    _drawSashGlazingBead(canvas, sashX, sashY, singleSashWidth, sashHeight, sashThickness, paint, fillPaint);

    // رسم الباكتة والزجاج للضلفة اليمنى
    final rightSashX = sashX + singleSashWidth + dividerWidth;
    _drawSashGlazingBead(canvas, rightSashX, sashY, singleSashWidth, sashHeight, sashThickness, paint, fillPaint);
  }

  // رسم الباكتة والزجاج للضلف المتعددة
  void _drawMultipleGlass(Canvas canvas, double sashX, double sashY, double sashWidth, double sashHeight, Paint paint, Paint fillPaint, int sashCount) {
    // أحجام ثابتة
    final sashThickness = 10.0; // سمك ثابت
    final dividerWidth = 6.0; // عرض فاصل ثابت

    // عرض كل ضلفة
    final singleSashWidth = (sashWidth - (dividerWidth * (sashCount - 1))) / sashCount;

    // رسم الباكتة والزجاج لكل ضلفة
    for (int i = 0; i < sashCount; i++) {
      final currentX = sashX + (i * (singleSashWidth + dividerWidth));

      // رسم الباكتة والزجاج لهذه الضلفة
      _drawSashGlazingBead(canvas, currentX, sashY, singleSashWidth, sashHeight, sashThickness, paint, fillPaint);
    }
  }

  // رسم لوح زجاج واحد
  void _drawSingleGlassPane(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    // لون الزجاج (أزرق فاتح شفاف)
    paint.color = const Color(0xFF87CEEB).withValues(alpha: 0.3); // Sky blue شفاف
    fillPaint.color = const Color(0xFF87CEEB).withValues(alpha: 0.3);

    // رسم الزجاج
    canvas.drawRect(
      Rect.fromLTWH(x, y, width, height),
      fillPaint,
    );

    // رسم حدود الزجاج (خفيفة)
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 0.5;
    paint.color = const Color(0xFF4682B4).withValues(alpha: 0.5); // Steel blue شفاف

    canvas.drawRect(
      Rect.fromLTWH(x, y, width, height),
      paint,
    );

    // إضافة تأثير انعكاس بسيط
    final reflectionPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    // انعكاس في الزاوية العلوية اليسرى
    canvas.drawRect(
      Rect.fromLTWH(x, y, width * 0.3, height * 0.3),
      reflectionPaint,
    );

    paint.style = PaintingStyle.fill;
  }







  // رسم الثوابت المتعددة
  void _drawFixedPanels(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint, Size size) {
    final fixedPanels = item.drawingOptions.fixedPanels;

    if (!fixedPanels.hasAnyFixed) return;

    // المتغيرات المطلوبة
    final drawWidth = width;
    final drawHeight = height;
    final margin = 10.0; // تقليل الهامش للشاشات الصغيرة

    // حساب المساحة المتاحة للثوابت
    final availableWidth = size.width - (margin * 2);
    final availableHeight = size.height - (margin * 2);

    // حساب أحجام الثوابت بناءً على المساحة المتاحة
    final maxFixedSize = math.min(availableWidth * 0.3, availableHeight * 0.3); // 30% من المساحة المتاحة

    // إعداد النص للمقاسات
    final textPainter = TextPainter(
      textDirection: TextDirection.rtl,
      textAlign: TextAlign.center,
    );

    // ثابت يمين - ملتصق مباشرة بالحلق
    if (fixedPanels.hasRight) {
      final requestedWidth = fixedPanels.rightSize * 2; // الحجم المطلوب
      final fixedWidth = math.min(requestedWidth, maxFixedSize); // تحديد الحجم بناءً على المساحة المتاحة
      final fixedX = startX + drawWidth; // ملتصق مباشرة بالحلق بدون مسافة

      // التأكد من أن الثابت داخل حدود الإطار
      if (fixedX + fixedWidth <= size.width - margin) {
        _drawFixedPanel(canvas, fixedX, startY, fixedWidth, drawHeight, fixedPanels.rightDivisions, true, paint, fillPaint);

        // رسم المقاس في الأعلى مع الأسهم
        textPainter.text = TextSpan(
          text: fixedPanels.rightSize.toStringAsFixed(0),
          style: const TextStyle(
            color: Colors.red,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        final textX = fixedX + fixedWidth/2 - textPainter.width/2;
        final textY = math.max(5.0, startY - 20);
        textPainter.paint(canvas, Offset(textX, textY));

        // رسم الأسهم والخط للثابت الأيمن (أفقية)
        paint.color = Colors.red;
        paint.style = PaintingStyle.fill;
        _drawHorizontalArrow(canvas, Offset(fixedX, startY - 10), true, paint); // سهم يسار
        _drawHorizontalArrow(canvas, Offset(fixedX + fixedWidth, startY - 10), false, paint); // سهم يمين

        // رسم الخط الأفقي بين الأسهم
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 1.0;
        canvas.drawLine(
          Offset(fixedX, startY - 10),
          Offset(fixedX + fixedWidth, startY - 10),
          paint,
        );
      }
    }

    // ثابت شمال - ملتصق مباشرة بالحلق
    if (fixedPanels.hasLeft) {
      final requestedWidth = fixedPanels.leftSize * 2; // الحجم المطلوب
      final fixedWidth = math.min(requestedWidth, maxFixedSize); // تحديد الحجم بناءً على المساحة المتاحة
      final fixedX = startX - fixedWidth; // ملتصق مباشرة بالحلق بدون مسافة

      // التأكد من أن الثابت داخل حدود الإطار
      if (fixedX >= margin) {
        _drawFixedPanel(canvas, fixedX, startY, fixedWidth, drawHeight, fixedPanels.leftDivisions, true, paint, fillPaint);

        // رسم المقاس في الأعلى مع الأسهم
        textPainter.text = TextSpan(
          text: fixedPanels.leftSize.toStringAsFixed(0),
          style: const TextStyle(
            color: Colors.red,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        final textX = fixedX + fixedWidth/2 - textPainter.width/2;
        final textY = math.max(5.0, startY - 20);
        textPainter.paint(canvas, Offset(textX, textY));

        // رسم الأسهم والخط للثابت الأيسر (أفقية)
        paint.color = Colors.red;
        paint.style = PaintingStyle.fill;
        _drawHorizontalArrow(canvas, Offset(fixedX, startY - 10), true, paint); // سهم يسار
        _drawHorizontalArrow(canvas, Offset(fixedX + fixedWidth, startY - 10), false, paint); // سهم يمين

        // رسم الخط الأفقي بين الأسهم
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 1.0;
        canvas.drawLine(
          Offset(fixedX, startY - 10),
          Offset(fixedX + fixedWidth, startY - 10),
          paint,
        );
      }
    }

    // ثابت أعلى - ملتصق مباشرة بالحلق
    if (fixedPanels.hasTop) {
      final requestedHeight = fixedPanels.topSize * 2; // الحجم المطلوب
      final fixedHeight = math.min(requestedHeight, maxFixedSize); // تحديد الحجم بناءً على المساحة المتاحة
      final fixedY = startY - fixedHeight; // ملتصق مباشرة بالحلق بدون مسافة

      // التأكد من أن الثابت مرئي جزئياً على الأقل
      if (fixedY + fixedHeight > margin) {
        _drawFixedPanel(canvas, startX, fixedY, drawWidth, fixedHeight, fixedPanels.topDivisions, false, paint, fillPaint);

        // رسم المقاس عمودياً على الجانب الأيسر مع الأسهم
        canvas.save();
        canvas.translate(startX - 25, fixedY + fixedHeight/2);
        canvas.rotate(-math.pi / 2); // دوران 90 درجة عكس عقارب الساعة

        textPainter.text = TextSpan(
          text: fixedPanels.topSize.toStringAsFixed(0),
          style: const TextStyle(
            color: Colors.red,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(canvas, Offset(-textPainter.width/2, -textPainter.height/2));
        canvas.restore();

        // رسم الأسهم والخط للثابت العلوي (عمودية)
        paint.color = Colors.red;
        paint.style = PaintingStyle.fill;
        _drawVerticalArrow(canvas, Offset(startX - 15, fixedY), true, paint); // سهم أعلى
        _drawVerticalArrow(canvas, Offset(startX - 15, fixedY + fixedHeight), false, paint); // سهم أسفل

        // رسم الخط العمودي بين الأسهم
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 1.0;
        canvas.drawLine(
          Offset(startX - 15, fixedY),
          Offset(startX - 15, fixedY + fixedHeight),
          paint,
        );
      }
    }

    // ثابت أسفل - ملتصق مباشرة بالحلق
    if (fixedPanels.hasBottom) {
      final requestedHeight = fixedPanels.bottomSize * 2; // الحجم المطلوب
      final fixedHeight = math.min(requestedHeight, maxFixedSize); // تحديد الحجم بناءً على المساحة المتاحة
      final fixedY = startY + drawHeight; // ملتصق مباشرة بالحلق بدون مسافة

      // التأكد من أن الثابت مرئي جزئياً على الأقل
      if (fixedY < size.height - margin) {
        _drawFixedPanel(canvas, startX, fixedY, drawWidth, fixedHeight, fixedPanels.bottomDivisions, false, paint, fillPaint);

        // رسم المقاس عمودياً على الجانب الأيسر مع الأسهم
        canvas.save();
        canvas.translate(startX - 25, fixedY + fixedHeight/2);
        canvas.rotate(-math.pi / 2); // دوران 90 درجة عكس عقارب الساعة

        textPainter.text = TextSpan(
          text: fixedPanels.bottomSize.toStringAsFixed(0),
          style: const TextStyle(
            color: Colors.red,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        );
        textPainter.layout();
        textPainter.paint(canvas, Offset(-textPainter.width/2, -textPainter.height/2));
        canvas.restore();

        // رسم الأسهم والخط للثابت السفلي (عمودية)
        paint.color = Colors.red;
        paint.style = PaintingStyle.fill;
        _drawVerticalArrow(canvas, Offset(startX - 15, fixedY), true, paint); // سهم أعلى
        _drawVerticalArrow(canvas, Offset(startX - 15, fixedY + fixedHeight), false, paint); // سهم أسفل

        // رسم الخط العمودي بين الأسهم
        paint.style = PaintingStyle.stroke;
        paint.strokeWidth = 1.0;
        canvas.drawLine(
          Offset(startX - 15, fixedY),
          Offset(startX - 15, fixedY + fixedHeight),
          paint,
        );
      }
    }
  }

  // رسم ثابت - الحلق والباكتة والزجاج
  void _drawFixedPanel(Canvas canvas, double x, double y, double width, double height, int divisions, bool isVertical, Paint paint, Paint fillPaint) {
    // رسم الحلق كأربعة أجزاء منفصلة
    _drawFrameParts(canvas, x, y, width, height, paint, fillPaint);

    // رسم الباكتة والزجاج مع السؤاسات
    _drawFixedPanelInterior(canvas, x, y, width, height, paint, fillPaint, divisions, isVertical);
  }

  // رسم الباكتة والزجاج للثابت مع السؤاسات
  void _drawFixedPanelInterior(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint, int divisions, bool isVertical) {
    // أحجام ثابتة
    final frameThickness = 12.0; // سمك ثابت
    final beadThickness = 4.0; // سمك ثابت
    final gap = 3.0; // مسافة ثابتة

    // حساب موضع الباكتة داخل الحلق
    final beadX = x + frameThickness + gap;
    final beadY = y + frameThickness + gap;
    final beadWidth = width - (frameThickness * 2) - (gap * 2);
    final beadHeight = height - (frameThickness * 2) - (gap * 2);

    // التأكد من أن منطقة الباكتة موجبة
    if (beadWidth <= 0 || beadHeight <= 0) return;

    // رسم الباكتة (إطار رفيع)
    _drawBeadFrame(canvas, beadX, beadY, beadWidth, beadHeight, paint, fillPaint, beadThickness);

    // رسم الزجاج مع السؤاسات داخل الباكتة
    _drawFixedPanelGlass(canvas, beadX, beadY, beadWidth, beadHeight, beadThickness, divisions, isVertical, paint, fillPaint);
  }

  // رسم الزجاج مع السؤاسات في الثابت
  void _drawFixedPanelGlass(Canvas canvas, double beadX, double beadY, double beadWidth, double beadHeight, double beadThickness, int divisions, bool isVertical, Paint paint, Paint fillPaint) {
    // حساب منطقة الزجاج داخل الباكتة
    final glassX = beadX + beadThickness;
    final glassY = beadY + beadThickness;
    final glassWidth = beadWidth - (beadThickness * 2);
    final glassHeight = beadHeight - (beadThickness * 2);

    if (glassWidth <= 0 || glassHeight <= 0) return;

    if (divisions == 0) {
      // بدون سؤاس - زجاج واحد
      _drawSingleGlassPane(canvas, glassX, glassY, glassWidth, glassHeight, paint, fillPaint);
    } else {
      // مع سؤاسات - تقسيم الزجاج
      final mullionThickness = 6.0; // سمك السؤاس
      final glassCount = divisions + 1; // عدد قطع الزجاج = عدد السؤاسات + 1

      if (isVertical) {
        // سؤاسات أفقية (للثوابت الجانبية - يمين/شمال)
        final singleGlassHeight = (glassHeight - (divisions * mullionThickness)) / glassCount;

        // رسم قطع الزجاج والسؤاسات الأفقية
        for (int i = 0; i < glassCount; i++) {
          final currentGlassY = glassY + (i * (singleGlassHeight + mullionThickness));

          // رسم قطعة الزجاج
          if (singleGlassHeight > 0) {
            _drawSingleGlassPane(canvas, glassX, currentGlassY, glassWidth, singleGlassHeight, paint, fillPaint);
          }

          // رسم السؤاس الأفقي (إلا بعد القطعة الأخيرة)
          if (i < glassCount - 1) {
            final mullionY = currentGlassY + singleGlassHeight;
            _drawMullion(canvas, glassX, mullionY, glassWidth, mullionThickness, paint, fillPaint);
          }
        }
      } else {
        // سؤاسات عمودية (للثوابت العلوية والسفلية - أعلى/أسفل)
        final singleGlassWidth = (glassWidth - (divisions * mullionThickness)) / glassCount;

        // رسم قطع الزجاج والسؤاسات العمودية
        for (int i = 0; i < glassCount; i++) {
          final currentGlassX = glassX + (i * (singleGlassWidth + mullionThickness));

          // رسم قطعة الزجاج
          if (singleGlassWidth > 0) {
            _drawSingleGlassPane(canvas, currentGlassX, glassY, singleGlassWidth, glassHeight, paint, fillPaint);
          }

          // رسم السؤاس العمودي (إلا بعد القطعة الأخيرة)
          if (i < glassCount - 1) {
            final mullionX = currentGlassX + singleGlassWidth;
            _drawMullion(canvas, mullionX, glassY, mullionThickness, glassHeight, paint, fillPaint);
          }
        }
      }
    }
  }

  // رسم السؤاس (المولة)
  void _drawMullion(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint) {
    // لون السؤاس (نفس لون الباكتة)
    paint.color = const Color(0xFFE6D3A3); // لون بيج أغمق
    fillPaint.color = const Color(0xFFE6D3A3); // لون بيج أغمق

    // رسم السؤاس
    canvas.drawRect(
      Rect.fromLTWH(x, y, width, height),
      fillPaint,
    );

    // رسم حدود السؤاس
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح

    canvas.drawRect(
      Rect.fromLTWH(x, y, width, height),
      paint,
    );

    paint.style = PaintingStyle.fill;
  }

  // رسم الباكتة (الإطار الرفيع)
  void _drawBeadFrame(Canvas canvas, double x, double y, double width, double height, Paint paint, Paint fillPaint, double thickness) {
    // لون الباكتة (بيج أغمق قليلاً من الحلق)
    paint.color = const Color(0xFFE6D3A3); // لون بيج أغمق
    fillPaint.color = const Color(0xFFE6D3A3); // لون بيج أغمق

    // الجزء العلوي للباكتة
    canvas.drawRect(
      Rect.fromLTWH(x, y, width, thickness),
      fillPaint,
    );

    // الجزء السفلي للباكتة
    canvas.drawRect(
      Rect.fromLTWH(x, y + height - thickness, width, thickness),
      fillPaint,
    );

    // الجزء الأيسر للباكتة
    canvas.drawRect(
      Rect.fromLTWH(x, y + thickness, thickness, height - (thickness * 2)),
      fillPaint,
    );

    // الجزء الأيمن للباكتة
    canvas.drawRect(
      Rect.fromLTWH(x + width - thickness, y + thickness, thickness, height - (thickness * 2)),
      fillPaint,
    );

    // رسم حدود الباكتة
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    paint.color = const Color(0xFFD2B48C); // لون بني فاتح

    // حدود الجزء العلوي
    canvas.drawRect(Rect.fromLTWH(x, y, width, thickness), paint);

    // حدود الجزء السفلي
    canvas.drawRect(Rect.fromLTWH(x, y + height - thickness, width, thickness), paint);

    // حدود الجزء الأيسر
    canvas.drawRect(Rect.fromLTWH(x, y + thickness, thickness, height - (thickness * 2)), paint);

    // حدود الجزء الأيمن
    canvas.drawRect(Rect.fromLTWH(x + width - thickness, y + thickness, thickness, height - (thickness * 2)), paint);

    paint.style = PaintingStyle.fill;
  }

  // رسم مقاسات البند (العرض والارتفاع)
  void _drawItemDimensions(Canvas canvas, double startX, double startY, double width, double height, Paint paint, Paint fillPaint) {
    // إعداد النص للمقاسات
    final textPainter = TextPainter(
      textDirection: TextDirection.rtl,
      textAlign: TextAlign.center,
    );

    // رسم مقاس العرض (أسفل النافذة)
    textPainter.text = TextSpan(
      text: '${item.width.toStringAsFixed(0)} سم',
      style: const TextStyle(
        color: Colors.blue,
        fontSize: 14,
        fontWeight: FontWeight.bold,
        backgroundColor: Colors.white,
      ),
    );
    textPainter.layout();

    // موضع النص أسفل النافذة
    final widthTextX = startX + width/2 - textPainter.width/2;
    final widthTextY = startY + height + 10;
    textPainter.paint(canvas, Offset(widthTextX, widthTextY));

    // رسم مقاس الارتفاع (يسار النافذة)
    canvas.save();
    canvas.translate(startX - 30, startY + height/2);
    canvas.rotate(-math.pi / 2); // دوران 90 درجة عكس عقارب الساعة

    textPainter.text = TextSpan(
      text: '${item.height.toStringAsFixed(0)} سم',
      style: const TextStyle(
        color: Colors.blue,
        fontSize: 14,
        fontWeight: FontWeight.bold,
        backgroundColor: Colors.white,
      ),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(-textPainter.width/2, -textPainter.height/2));
    canvas.restore();

    // رسم خطوط المقاسات
    paint.color = Colors.blue;
    paint.strokeWidth = 1.0;
    paint.style = PaintingStyle.stroke;

    // خط مقاس العرض
    canvas.drawLine(
      Offset(startX, startY + height + 5),
      Offset(startX + width, startY + height + 5),
      paint,
    );

    // خط مقاس الارتفاع
    canvas.drawLine(
      Offset(startX - 5, startY),
      Offset(startX - 5, startY + height),
      paint,
    );

    // أسهم المقاسات
    _drawHorizontalArrow(canvas, Offset(startX, startY + height + 5), true, paint); // سهم يسار
    _drawHorizontalArrow(canvas, Offset(startX + width, startY + height + 5), false, paint); // سهم يمين
    _drawVerticalArrow(canvas, Offset(startX - 5, startY), true, paint); // سهم أعلى (عمودي)
    _drawVerticalArrow(canvas, Offset(startX - 5, startY + height), false, paint); // سهم أسفل (عمودي)

    paint.style = PaintingStyle.fill;
  }

  // رسم سهم أفقي للمقاسات
  void _drawHorizontalArrow(Canvas canvas, Offset position, bool isLeft, Paint paint) {
    final arrowSize = 4.0;
    final arrowPath = Path();

    if (isLeft) {
      // سهم يسار
      arrowPath.moveTo(position.dx, position.dy);
      arrowPath.lineTo(position.dx + arrowSize, position.dy - arrowSize/2);
      arrowPath.lineTo(position.dx + arrowSize, position.dy + arrowSize/2);
    } else {
      // سهم يمين
      arrowPath.moveTo(position.dx, position.dy);
      arrowPath.lineTo(position.dx - arrowSize, position.dy - arrowSize/2);
      arrowPath.lineTo(position.dx - arrowSize, position.dy + arrowSize/2);
    }

    arrowPath.close();
    canvas.drawPath(arrowPath, paint);
  }

  // رسم سهم عمودي للمقاسات
  void _drawVerticalArrow(Canvas canvas, Offset position, bool isUp, Paint paint) {
    final arrowSize = 4.0;
    final arrowPath = Path();

    if (isUp) {
      // سهم أعلى
      arrowPath.moveTo(position.dx, position.dy);
      arrowPath.lineTo(position.dx - arrowSize/2, position.dy + arrowSize);
      arrowPath.lineTo(position.dx + arrowSize/2, position.dy + arrowSize);
    } else {
      // سهم أسفل
      arrowPath.moveTo(position.dx, position.dy);
      arrowPath.lineTo(position.dx - arrowSize/2, position.dy - arrowSize);
      arrowPath.lineTo(position.dx + arrowSize/2, position.dy - arrowSize);
    }

    arrowPath.close();
    canvas.drawPath(arrowPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
