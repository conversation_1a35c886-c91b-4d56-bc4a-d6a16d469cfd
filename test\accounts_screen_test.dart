import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:uptime_smart_assist/screens/accounting/customer_accounts_screen.dart';
import 'package:uptime_smart_assist/screens/accounting/supplier_accounts_screen.dart';

void main() {
  testWidgets('Customer Accounts Screen can be created', (WidgetTester tester) async {
    // Test that the Customer Accounts Screen can be instantiated
    await tester.pumpWidget(
      MaterialApp(
        home: CustomerAccountsScreen(),
      ),
    );
    
    // Check if the screen contains expected elements
    expect(find.text('حسابات العملاء'), findsOneWidget);
    expect(find.byType(FloatingActionButton), findsOneWidget);
  });

  testWidgets('Supplier Accounts Screen can be created', (WidgetTester tester) async {
    // Test that the Supplier Accounts Screen can be instantiated
    await tester.pumpWidget(
      MaterialApp(
        home: SupplierAccountsScreen(),
      ),
    );
    
    // Check if the screen contains expected elements
    expect(find.text('حسابات الموردين'), findsOneWidget);
    expect(find.byType(FloatingActionButton), findsOneWidget);
  });
}
