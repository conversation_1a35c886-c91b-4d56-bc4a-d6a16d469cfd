import 'panel_order_item.dart';

class PanelCuttingProject {
  final String id;
  final String projectName;
  final String customerName;
  final double panelWidth;
  final double panelHeight;
  final List<PanelOrderItem> orderItems;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  PanelCuttingProject({
    required this.id,
    required this.projectName,
    required this.customerName,
    required this.panelWidth,
    required this.panelHeight,
    required this.orderItems,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PanelCuttingProject.fromMap(Map<String, dynamic> map) {
    return PanelCuttingProject(
      id: map['id'] ?? '',
      projectName: map['project_name'] ?? '',
      customerName: map['customer_name'] ?? '',
      panelWidth: (map['panel_width'] ?? 0).toDouble(),
      panelHeight: (map['panel_height'] ?? 0).toDouble(),
      orderItems: (map['order_items'] as List<dynamic>? ?? [])
          .map((item) => PanelOrderItem.fromMap(item))
          .toList(),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_name': projectName,
      'customer_name': customerName,
      'panel_width': panelWidth,
      'panel_height': panelHeight,
      'order_items': orderItems.map((item) => item.toMap()).toList(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PanelCuttingProject copyWith({
    String? id,
    String? projectName,
    String? customerName,
    double? panelWidth,
    double? panelHeight,
    List<PanelOrderItem>? orderItems,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PanelCuttingProject(
      id: id ?? this.id,
      projectName: projectName ?? this.projectName,
      customerName: customerName ?? this.customerName,
      panelWidth: panelWidth ?? this.panelWidth,
      panelHeight: panelHeight ?? this.panelHeight,
      orderItems: orderItems ?? this.orderItems,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // حساب إجمالي المساحة المطلوبة
  double get totalRequiredArea {
    return orderItems.fold<double>(0, (sum, item) => sum + item.totalArea);
  }

  // حساب إجمالي عدد القطع
  int get totalPieces {
    return orderItems.fold<int>(0, (sum, item) => sum + item.quantity);
  }

  // مساحة اللوح الواحد
  double get panelArea {
    return panelWidth * panelHeight;
  }
}
