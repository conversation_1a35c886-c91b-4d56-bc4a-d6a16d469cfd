class CuttingProject {
  final int? id;
  final String projectNumber;
  final String customerName;
  final DateTime date;
  final DateTime deliveryDate;
  final String phone;
  final String address;
  final String notes;

  CuttingProject({
    this.id,
    required this.projectNumber,
    required this.customerName,
    required this.date,
    required this.deliveryDate,
    required this.phone,
    required this.address,
    required this.notes,
  });

  // Convert from Map (from database)
  factory CuttingProject.fromMap(Map<String, dynamic> map) {
    return CuttingProject(
      id: map['id'],
      projectNumber: map['project_number'] ?? '',
      customerName: map['customer_name'] ?? '',
      date: DateTime.parse(map['date']),
      deliveryDate: DateTime.parse(map['delivery_date']),
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      notes: map['notes'] ?? '',
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    final now = DateTime.now().millisecondsSinceEpoch;
    return {
      'id': id,
      'project_number': projectNumber,
      'customer_name': customerName,
      'date': date.toIso8601String(),
      'delivery_date': deliveryDate.toIso8601String(),
      'phone': phone,
      'address': address,
      'notes': notes,
      'created_at': now,
      'updated_at': now,
    };
  }

  // Create a copy with updated values
  CuttingProject copyWith({
    int? id,
    String? projectNumber,
    String? customerName,
    DateTime? date,
    DateTime? deliveryDate,
    String? phone,
    String? address,
    String? notes,
  }) {
    return CuttingProject(
      id: id ?? this.id,
      projectNumber: projectNumber ?? this.projectNumber,
      customerName: customerName ?? this.customerName,
      date: date ?? this.date,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'CuttingProject{id: $id, projectNumber: $projectNumber, customerName: $customerName, date: $date, deliveryDate: $deliveryDate, phone: $phone, address: $address, notes: $notes}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CuttingProject &&
        other.id == id &&
        other.projectNumber == projectNumber &&
        other.customerName == customerName &&
        other.date == date &&
        other.deliveryDate == deliveryDate &&
        other.phone == phone &&
        other.address == address &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        projectNumber.hashCode ^
        customerName.hashCode ^
        date.hashCode ^
        deliveryDate.hashCode ^
        phone.hashCode ^
        address.hashCode ^
        notes.hashCode;
  }
}
