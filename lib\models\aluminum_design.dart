// نماذج التخصيمات للألمنيوم

class HingeDesign {
  final int? id;
  final int? seriesId; // ربط بمجموعة القطاعات
  final double? dalfaOnHalaf; // ركوب الضلفة على الحلق
  final double? maradDalfaComplete; // تخصيم المرد الضلفة بالكامل
  final double? maradDalfaWithKaab; // تخصيم المرد للضلفة بكعب
  final double? maradBetweenDalfa; // تخصيم بين الضلفتين (المرد)
  final double? dalfaFromGround; // تخصيم الضلفة من الارض فى حالة ابواب بكعب
  final double? dalfaGlass; // تخصيم زجاج الضلف
  final double? fixedGlass; // تخصيم زجاج الثابت
  final double? movingSilk; // تخصيم السلك المتحرك
  final double? fixedSilk; // تخصيم السلك الثابت
  final DateTime createdAt;
  final DateTime updatedAt;

  HingeDesign({
    this.id,
    this.seriesId,
    this.dalfaOnHalaf,
    this.maradDalfaComplete,
    this.maradDalfaWithKaab,
    this.maradBetweenDalfa,
    this.dalfaFromGround,
    this.dalfaGlass,
    this.fixedGlass,
    this.movingSilk,
    this.fixedSilk,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'series_id': seriesId,
      'dalfa_on_halaf': dalfaOnHalaf,
      'marad_dalfa_complete': maradDalfaComplete,
      'marad_dalfa_with_kaab': maradDalfaWithKaab,
      'marad_between_dalfa': maradBetweenDalfa,
      'dalfa_from_ground': dalfaFromGround,
      'dalfa_glass': dalfaGlass,
      'fixed_glass': fixedGlass,
      'moving_silk': movingSilk,
      'fixed_silk': fixedSilk,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory HingeDesign.fromMap(Map<String, dynamic> map) {
    return HingeDesign(
      id: map['id']?.toInt(),
      seriesId: map['series_id']?.toInt(),
      dalfaOnHalaf: map['dalfa_on_halaf']?.toDouble(),
      maradDalfaComplete: map['marad_dalfa_complete']?.toDouble(),
      maradDalfaWithKaab: map['marad_dalfa_with_kaab']?.toDouble(),
      maradBetweenDalfa: map['marad_between_dalfa']?.toDouble(),
      dalfaFromGround: map['dalfa_from_ground']?.toDouble(),
      dalfaGlass: map['dalfa_glass']?.toDouble(),
      fixedGlass: map['fixed_glass']?.toDouble(),
      movingSilk: map['moving_silk']?.toDouble(),
      fixedSilk: map['fixed_silk']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  HingeDesign copyWith({
    int? id,
    int? seriesId,
    double? dalfaOnHalaf,
    double? maradDalfaComplete,
    double? maradDalfaWithKaab,
    double? maradBetweenDalfa,
    double? dalfaFromGround,
    double? dalfaGlass,
    double? fixedGlass,
    double? movingSilk,
    double? fixedSilk,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HingeDesign(
      id: id ?? this.id,
      seriesId: seriesId ?? this.seriesId,
      dalfaOnHalaf: dalfaOnHalaf ?? this.dalfaOnHalaf,
      maradDalfaComplete: maradDalfaComplete ?? this.maradDalfaComplete,
      maradDalfaWithKaab: maradDalfaWithKaab ?? this.maradDalfaWithKaab,
      maradBetweenDalfa: maradBetweenDalfa ?? this.maradBetweenDalfa,
      dalfaFromGround: dalfaFromGround ?? this.dalfaFromGround,
      dalfaGlass: dalfaGlass ?? this.dalfaGlass,
      fixedGlass: fixedGlass ?? this.fixedGlass,
      movingSilk: movingSilk ?? this.movingSilk,
      fixedSilk: fixedSilk ?? this.fixedSilk,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum SlidingDalfaCount {
  two(2, 'ضلفتين'),
  three(3, 'ثلاث ضلف'),
  four(4, 'أربع ضلف'),
  six(6, '6 ضلف');

  const SlidingDalfaCount(this.count, this.arabicName);
  final int count;
  final String arabicName;
}

enum SlidingDalfaMethod {
  method45(45, '45'),
  method90(90, '90');

  const SlidingDalfaMethod(this.angle, this.name);
  final int angle;
  final String name;
}

class SlidingDesign {
  final int? id;
  final int? seriesId; // ربط بمجموعة القطاعات
  final SlidingDalfaCount dalfaCount; // عدد الضلف
  final SlidingDalfaMethod dalfaMethod; // طريقة الضلف 45 او 90
  final double? dalfaWidthPlus; // تخصيم الضلف عرض +
  final double? dalfaHeightMinus; // تخصيم الضلف ارتفاع -
  final double? skinehHeightMinus; // تخصيم السكينة ارتفاع -
  final double? silkWidthPlus; // تخصيم السلك عرض +
  final double? silkHeightMinus; // تخصيم السلك ارتفاع -
  final double? glassWidthMinus; // تخصيم الزجاج عرض -
  final double? glassHeightMinus; // تخصيم الزجاج ارتفاع -
  final DateTime createdAt;
  final DateTime updatedAt;

  SlidingDesign({
    this.id,
    this.seriesId,
    required this.dalfaCount,
    required this.dalfaMethod,
    this.dalfaWidthPlus,
    this.dalfaHeightMinus,
    this.skinehHeightMinus,
    this.silkWidthPlus,
    this.silkHeightMinus,
    this.glassWidthMinus,
    this.glassHeightMinus,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'series_id': seriesId,
      'dalfa_count': dalfaCount.count,
      'dalfa_method': dalfaMethod.angle,
      'dalfa_width_plus': dalfaWidthPlus,
      'dalfa_height_minus': dalfaHeightMinus,
      'skineh_height_minus': skinehHeightMinus,
      'silk_width_plus': silkWidthPlus,
      'silk_height_minus': silkHeightMinus,
      'glass_width_minus': glassWidthMinus,
      'glass_height_minus': glassHeightMinus,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory SlidingDesign.fromMap(Map<String, dynamic> map) {
    return SlidingDesign(
      id: map['id']?.toInt(),
      seriesId: map['series_id']?.toInt(),
      dalfaCount: SlidingDalfaCount.values.firstWhere(
        (c) => c.count == map['dalfa_count'],
        orElse: () => SlidingDalfaCount.two,
      ),
      dalfaMethod: SlidingDalfaMethod.values.firstWhere(
        (m) => m.angle == map['dalfa_method'],
        orElse: () => SlidingDalfaMethod.method45,
      ),
      dalfaWidthPlus: map['dalfa_width_plus']?.toDouble(),
      dalfaHeightMinus: map['dalfa_height_minus']?.toDouble(),
      skinehHeightMinus: map['skineh_height_minus']?.toDouble(),
      silkWidthPlus: map['silk_width_plus']?.toDouble(),
      silkHeightMinus: map['silk_height_minus']?.toDouble(),
      glassWidthMinus: map['glass_width_minus']?.toDouble(),
      glassHeightMinus: map['glass_height_minus']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  SlidingDesign copyWith({
    int? id,
    int? seriesId,
    SlidingDalfaCount? dalfaCount,
    SlidingDalfaMethod? dalfaMethod,
    double? dalfaWidthPlus,
    double? dalfaHeightMinus,
    double? skinehHeightMinus,
    double? silkWidthPlus,
    double? silkHeightMinus,
    double? glassWidthMinus,
    double? glassHeightMinus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SlidingDesign(
      id: id ?? this.id,
      seriesId: seriesId ?? this.seriesId,
      dalfaCount: dalfaCount ?? this.dalfaCount,
      dalfaMethod: dalfaMethod ?? this.dalfaMethod,
      dalfaWidthPlus: dalfaWidthPlus ?? this.dalfaWidthPlus,
      dalfaHeightMinus: dalfaHeightMinus ?? this.dalfaHeightMinus,
      skinehHeightMinus: skinehHeightMinus ?? this.skinehHeightMinus,
      silkWidthPlus: silkWidthPlus ?? this.silkWidthPlus,
      silkHeightMinus: silkHeightMinus ?? this.silkHeightMinus,
      glassWidthMinus: glassWidthMinus ?? this.glassWidthMinus,
      glassHeightMinus: glassHeightMinus ?? this.glassHeightMinus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
