# ميزة استيراد قطاعات الألومنيوم من قاعدة البيانات الأونلاين

## نظرة عامة

تم إضافة ميزة جديدة لاستيراد مجموعات القطاعات والقطاعات من قاعدة بيانات MySQL الأونلاين إلى قاعدة البيانات المحلية في تطبيق Uptime Smart Assist.

## الملفات المضافة/المحدثة

### الملفات الجديدة:
1. **`lib/services/aluminum_import_service.dart`** - خدمة استيراد قطاعات الألومنيوم
2. **`lib/widgets/import_progress_dialog.dart`** - حوار عرض تقدم عملية الاستيراد
3. **`lib/widgets/series_selection_dialog.dart`** - حوار اختيار المجموعات للاستيراد

### الملفات المحدثة:
1. **`lib/screens/aluminum/aluminum_settings_screen.dart`** - إضافة زر التحميل والوظائف المطلوبة
2. **`lib/services/mysql_service.dart`** - تحديث إعدادات الاتصال بقاعدة البيانات
3. **`lib/services/unified_aluminum_service.dart`** - إضافة طرق مساعدة للاستيراد

## إعدادات قاعدة البيانات

تم تكوين الاتصال بقاعدة البيانات الأونلاين بالمعلومات التالية:
- **الخادم**: 207.244.67.2
- **قاعدة البيانات**: hugowjjm_smart_assist
- **المستخدم**: hugowjjm_sm
- **كلمة المرور**: rS}5bEt.[sRN

## كيفية الاستخدام

### 1. الوصول إلى الميزة
- انتقل إلى **إعدادات الألومنيوم**
- اختر تبويب **المفصلي** أو **السحاب**
- ابحث عن زر التحميل (أيقونة السحابة) في شريط أدوات إدارة المجموعات

### 2. عملية الاستيراد
1. **اختبار الاتصال**: يتم اختبار الاتصال بقاعدة البيانات الأونلاين أولاً
2. **جلب المجموعات**: يتم جلب قائمة المجموعات المتاحة حسب النوع (مفصلي/سحاب)
3. **اختيار المجموعات**: يظهر حوار لاختيار المجموعات المراد استيرادها
4. **عملية الاستيراد**: تبدأ عملية الاستيراد مع عرض التقدم والتفاصيل
5. **النتائج**: يتم عرض نتائج الاستيراد مع عدد المجموعات والقطاعات المستوردة

### 3. ميزات الاستيراد
- **استيراد ذكي**: يتحقق من وجود المجموعات والقطاعات محلياً ويحدثها أو يضيفها حسب الحاجة
- **شريط التقدم**: عرض مرئي لتقدم عملية الاستيراد
- **تفاصيل مفصلة**: عرض رسائل تفصيلية عن كل خطوة في عملية الاستيراد
- **معالجة الأخطاء**: التعامل مع الأخطاء وعرض رسائل واضحة للمستخدم

## الوظائف الرئيسية

### خدمة الاستيراد (`AluminumImportService`)
- `testConnection()`: اختبار الاتصال بقاعدة البيانات الأونلاين
- `getOnlineProfileSeries()`: جلب مجموعات القطاعات من الأونلاين
- `getOnlineProfilesBySeries()`: جلب القطاعات لمجموعة معينة
- `importSeriesWithProfiles()`: استيراد مجموعة مع قطاعاتها
- `importMultipleSeries()`: استيراد عدة مجموعات

### حوار التقدم (`ImportProgressDialog`)
- عرض شريط التقدم الدائري والخطي
- رسائل تفصيلية عن حالة الاستيراد
- عرض النتائج النهائية مع الإحصائيات
- منع إغلاق الحوار أثناء عملية الاستيراد

### حوار اختيار المجموعات (`SeriesSelectionDialog`)
- عرض قائمة المجموعات المتاحة
- إمكانية تحديد مجموعات متعددة
- خيار تحديد/إلغاء تحديد الكل
- عرض معلومات تفصيلية عن كل مجموعة

## معالجة البيانات

### المجموعات (Profile Series)
- يتم التحقق من وجود المجموعة محلياً بناءً على الكود
- في حالة الوجود: يتم تحديث البيانات
- في حالة عدم الوجود: يتم إنشاء مجموعة جديدة

### القطاعات (Aluminum Profiles)
- يتم ربط القطاعات بالمجموعة المحلية الصحيحة
- يتم التحقق من وجود القطاع محلياً بناءً على الكود
- في حالة الوجود: يتم تحديث البيانات
- في حالة عدم الوجود: يتم إنشاء قطاع جديد

## رسائل الخطأ والتعامل معها

### أخطاء الاتصال
- "فشل الاتصال بقاعدة البيانات الأونلاين"
- "خطأ في جلب المجموعات"

### حالات خاصة
- "لا توجد مجموعات متاحة للاستيراد"
- "خطأ في استيراد المجموعة"
- "خطأ في استيراد قطاع"

## الأمان والأداء

### الأمان
- استخدام معاملات محضرة (Prepared Statements) لمنع SQL Injection
- التحقق من صحة البيانات قبل الإدراج
- معالجة شاملة للأخطاء

### الأداء
- استيراد تدريجي مع عرض التقدم
- معالجة البيانات على دفعات
- تحديث واجهة المستخدم بشكل دوري

## المتطلبات التقنية

### الحزم المطلوبة
- `mysql1`: للاتصال بقاعدة بيانات MySQL
- `sqflite`: لقاعدة البيانات المحلية
- `flutter/material.dart`: لواجهة المستخدم

### إصدار Flutter
- متوافق مع Flutter 3.0+
- يستخدم أحدث APIs لـ PopScope بدلاً من WillPopScope المهجور

## الاختبار والتطوير

### اختبار الميزة
1. تأكد من وجود اتصال إنترنت
2. تحقق من صحة إعدادات قاعدة البيانات
3. اختبر مع مجموعات مختلفة (مفصلي/سحاب)
4. تحقق من معالجة الأخطاء

### التطوير المستقبلي
- إضافة إمكانية استيراد انتقائي للقطاعات
- دعم استيراد الصور والملفات المرفقة
- إضافة ميزة المزامنة التلقائية
- تحسين الأداء للمجموعات الكبيرة

## الإصلاحات المطبقة

### مشكلة Blob Type Error
تم إصلاح مشكلة `'type 'Blob' is not a subtype of type 'String'` من خلال:

1. **تحديث نماذج البيانات**:
   - إضافة دالة `_convertToString()` في `ProfileSeries` و `AluminumProfile`
   - التعامل مع البيانات من نوع `Blob` وتحويلها إلى `String`

2. **تحسين خدمة MySQL**:
   - إضافة معالجة تلقائية لتحويل `Blob` إلى `String` في `mysql_service.dart`
   - تحسين معالجة البيانات المُستلمة من قاعدة البيانات

3. **معالجة أفضل للأخطاء**:
   - إضافة `try-catch` في خدمة الاستيراد لتجاهل البيانات التالفة
   - طباعة رسائل تشخيصية مفصلة للمطورين

### اختبار الإصلاحات
تم إنشاء ملف `test_mysql_connection.dart` لاختبار:
- الاتصال بقاعدة البيانات
- جلب مجموعات المفصلي والسحاب
- جلب القطاعات لمجموعة معينة
- التعامل مع أنواع البيانات المختلفة

## الدعم والصيانة

### مشاكل شائعة وحلولها

#### 1. خطأ Blob Type
**المشكلة**: `'type 'Blob' is not a subtype of type 'String'`
**الحل**: تم إصلاحها في الإصدار الحالي

#### 2. فشل الاتصال
**المشكلة**: عدم القدرة على الاتصال بقاعدة البيانات
**الحلول**:
- تحقق من الاتصال بالإنترنت
- تأكد من صحة إعدادات قاعدة البيانات
- تحقق من أن الخادم متاح

#### 3. بيانات فارغة
**المشكلة**: لا توجد مجموعات أو قطاعات
**الحلول**:
- تحقق من وجود البيانات في قاعدة البيانات الأونلاين
- تأكد من أن الحقل `is_active = 1`

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير مع تضمين:
- وصف المشكلة
- خطوات إعادة الإنتاج
- رسائل الخطأ (إن وجدت)
- معلومات البيئة (نظام التشغيل، إصدار التطبيق)
