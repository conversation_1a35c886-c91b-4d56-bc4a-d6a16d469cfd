import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class PdfExportService {
  /// Save image file with improved user experience
  static Future<bool> saveImageFile({
    required BuildContext context,
    required Uint8List imageBytes,
    required String fileName,
    bool showSuccessMessage = true,
  }) async {
    try {
      String? outputFile;

      if (Platform.isAndroid || Platform.isIOS) {
        // Request permissions first for mobile platforms
        bool hasPermission = await _requestStoragePermission();
        if (!hasPermission) {
          if (context.mounted) {
            _showErrorMessage(context, 'يجب منح إذن الوصول للتخزين لحفظ الصورة');
          }
          return false;
        }

        // For mobile platforms, save to Downloads folder
        outputFile = await _saveMobileImageFile(imageBytes, fileName);
      } else {
        // For desktop platforms, show save dialog
        outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'حفظ الصورة',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['png'],
        );

        if (outputFile != null) {
          final file = File(outputFile);
          await file.writeAsBytes(imageBytes);
        }
      }

      if (outputFile != null && showSuccessMessage && context.mounted) {
        _showImageSuccessMessage(context, outputFile);
        return true;
      } else if (outputFile == null && context.mounted) {
        _showCancelMessage(context);
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error in saveImageFile: $e');
      if (context.mounted) {
        _showErrorMessage(context, 'خطأ في حفظ الصورة: $e');
      }
      return false;
    }
  }

  /// Save PDF file with improved user experience
  static Future<bool> savePdfFile({
    required BuildContext context,
    required List<int> pdfBytes,
    required String fileName,
    bool showSuccessMessage = true,
  }) async {
    try {
      String? outputFile;
      
      if (Platform.isAndroid || Platform.isIOS) {
        // For mobile platforms, save to Downloads folder
        outputFile = await _saveMobileFile(pdfBytes, fileName);
      } else {
        // For desktop platforms, show save dialog
        outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'حفظ ملف PDF',
          fileName: fileName,
          type: FileType.custom,
          allowedExtensions: ['pdf'],
        );
        
        final file = File(outputFile!);
        await file.writeAsBytes(pdfBytes);
            }

      if (outputFile != null && showSuccessMessage && context.mounted) {
        _showSuccessMessage(context, outputFile);
        return true;
      } else if (outputFile == null && context.mounted) {
        _showCancelMessage(context);
        return false;
      }

      return true;
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, e.toString());
      }
      return false;
    }
  }

  /// Save file on mobile platforms (works for both PDF and images)
  static Future<String?> _saveMobileFile(List<int> fileBytes, String fileName) async {
    try {
      Directory? directory;

      if (Platform.isAndroid) {
        // Try multiple Android directories
        final List<Directory?> possibleDirs = [
          Directory('/storage/emulated/0/Download'),
          Directory('/storage/emulated/0/Downloads'),
          await getExternalStorageDirectory(),
          await getApplicationDocumentsDirectory(),
        ];

        for (final dir in possibleDirs) {
          if (dir != null) {
            try {
              if (!await dir.exists()) {
                await dir.create(recursive: true);
              }
              directory = dir;
              break;
            } catch (e) {
              debugPrint('Failed to use directory ${dir.path}: $e');
              continue;
            }
          }
        }
      } else if (Platform.isIOS) {
        // For iOS, use Documents directory
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory != null) {
        final file = File('${directory.path}/$fileName');
        await file.writeAsBytes(fileBytes);
        debugPrint('File saved successfully to: ${file.path}');
        return file.path;
      }

      debugPrint('No suitable directory found for saving file');
      return null;
    } catch (e) {
      debugPrint('Error saving mobile file: $e');
      return null;
    }
  }

  /// Show success message with file path and open folder option
  static void _showSuccessMessage(BuildContext context, String filePath) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تم حفظ الملف بنجاح!',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              'المسار: $filePath',
              style: const TextStyle(fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 5),
        action: Platform.isAndroid || Platform.isIOS 
          ? SnackBarAction(
              label: 'فتح',
              textColor: Colors.white,
              onPressed: () => _openFile(filePath),
            )
          : SnackBarAction(
              label: 'فتح المجلد',
              textColor: Colors.white,
              onPressed: () => _openFileLocation(filePath),
            ),
      ),
    );
  }

  /// Show cancellation message
  static void _showCancelMessage(BuildContext context) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إلغاء حفظ الملف'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  static void _showErrorMessage(BuildContext context, String error) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('خطأ في حفظ الملف: $error'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Open file location in file manager
  static Future<void> _openFileLocation(String filePath) async {
    try {
      final directory = File(filePath).parent.path;
      
      if (Platform.isWindows) {
        await Process.run('explorer', [directory]);
      } else if (Platform.isMacOS) {
        await Process.run('open', [directory]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [directory]);
      }
    } catch (e) {
      debugPrint('Error opening file location: $e');
    }
  }

  /// Open file directly (for mobile)
  static Future<void> _openFile(String filePath) async {
    try {
      if (Platform.isAndroid) {
        await Process.run('am', [
          'start',
          '-a', 'android.intent.action.VIEW',
          '-d', 'file://$filePath',
          '-t', 'application/pdf'
        ]);
      } else if (Platform.isIOS) {
        // iOS will handle this through the share sheet
        debugPrint('File saved to: $filePath');
      }
    } catch (e) {
      debugPrint('Error opening file: $e');
    }
  }

  /// Get default file name for PDF export
  static String getDefaultFileName(String prefix, {String? customerName}) {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    
    if (customerName != null && customerName.isNotEmpty) {
      return '${prefix}_${customerName}_$dateStr.pdf';
    } else {
      return '${prefix}_$dateStr.pdf';
    }
  }

  /// Request storage permission for mobile platforms
  static Future<bool> _requestStoragePermission() async {
    if (!Platform.isAndroid) return true;

    try {
      // For Android 13+ (API 33+), we need different permissions
      if (Platform.isAndroid) {
        // Try to get storage permission
        var status = await Permission.storage.status;
        if (status.isDenied) {
          status = await Permission.storage.request();
        }

        // For Android 13+, also try photos permission
        if (status.isDenied) {
          var photosStatus = await Permission.photos.status;
          if (photosStatus.isDenied) {
            photosStatus = await Permission.photos.request();
          }
          return photosStatus.isGranted;
        }

        return status.isGranted;
      }

      return true;
    } catch (e) {
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Save image file specifically for mobile platforms
  static Future<String?> _saveMobileImageFile(Uint8List imageBytes, String fileName) async {
    try {
      Directory? directory;

      if (Platform.isAndroid) {
        // Try multiple Android directories for images
        final List<String> possiblePaths = [
          '/storage/emulated/0/Pictures',
          '/storage/emulated/0/Download',
          '/storage/emulated/0/Downloads',
          '/storage/emulated/0/DCIM',
        ];

        for (final path in possiblePaths) {
          try {
            final dir = Directory(path);
            if (await dir.exists()) {
              directory = dir;
              debugPrint('Using directory: $path');
              break;
            }
          } catch (e) {
            debugPrint('Failed to access directory $path: $e');
            continue;
          }
        }

        // Fallback to app-specific directories
        if (directory == null) {
          final List<Directory?> fallbackDirs = [
            await getExternalStorageDirectory(),
            await getApplicationDocumentsDirectory(),
          ];

          for (final dir in fallbackDirs) {
            if (dir != null) {
              try {
                // Create a Pictures subdirectory
                final picturesDir = Directory('${dir.path}/Pictures');
                if (!await picturesDir.exists()) {
                  await picturesDir.create(recursive: true);
                }
                directory = picturesDir;
                debugPrint('Using fallback directory: ${picturesDir.path}');
                break;
              } catch (e) {
                debugPrint('Failed to create Pictures directory in ${dir.path}: $e');
                continue;
              }
            }
          }
        }
      } else if (Platform.isIOS) {
        // For iOS, use Documents directory
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory != null) {
        final file = File('${directory.path}/$fileName');
        await file.writeAsBytes(imageBytes);
        debugPrint('Image saved successfully to: ${file.path}');
        return file.path;
      }

      debugPrint('No suitable directory found for saving image');
      return null;
    } catch (e) {
      debugPrint('Error saving mobile image file: $e');
      return null;
    }
  }

  /// Show success message specifically for images
  static void _showImageSuccessMessage(BuildContext context, String filePath) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text(
                  'تم حفظ الصورة بنجاح!',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'المسار: $filePath',
              style: const TextStyle(fontSize: 12),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 6),
        action: Platform.isAndroid || Platform.isIOS
          ? SnackBarAction(
              label: 'فتح',
              textColor: Colors.white,
              onPressed: () => _openImageFile(filePath),
            )
          : SnackBarAction(
              label: 'فتح المجلد',
              textColor: Colors.white,
              onPressed: () => _openFileLocation(filePath),
            ),
      ),
    );
  }

  /// Open image file directly (for mobile)
  static Future<void> _openImageFile(String filePath) async {
    try {
      if (Platform.isAndroid) {
        await Process.run('am', [
          'start',
          '-a', 'android.intent.action.VIEW',
          '-d', 'file://$filePath',
          '-t', 'image/png'
        ]);
      } else if (Platform.isIOS) {
        // iOS will handle this through the share sheet
        debugPrint('Image saved to: $filePath');
      }
    } catch (e) {
      debugPrint('Error opening image file: $e');
    }
  }

  /// Check if storage permission is granted (Android)
  static Future<bool> checkStoragePermission() async {
    if (!Platform.isAndroid) return true;

    return await _requestStoragePermission();
  }

  /// Get common save locations info
  static Map<String, String> getSaveLocationInfo() {
    if (Platform.isWindows) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم فتح نافذة لاختيار مكان حفظ الملف',
        'defaultLocation': 'المجلد الذي تختاره',
      };
    } else if (Platform.isAndroid) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم حفظ الملف في مجلد التحميلات',
        'defaultLocation': '/storage/emulated/0/Download/',
      };
    } else if (Platform.isIOS) {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم حفظ الملف في مجلد المستندات',
        'defaultLocation': 'Documents folder',
      };
    } else {
      return {
        'title': 'مكان الحفظ',
        'description': 'سيتم فتح نافذة لاختيار مكان حفظ الملف',
        'defaultLocation': 'المجلد الذي تختاره',
      };
    }
  }
}
