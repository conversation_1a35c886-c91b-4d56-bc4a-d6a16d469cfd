import 'aluminum_profile.dart';

enum WindowDoorType {
  hinge('مفصلي', 'Hinge'),
  sliding('سحاب', 'Sliding');

  const WindowDoorType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

enum SashCount {
  one('ضلفة واحدة', 'One Sash', 1),
  two('ضلفتين', 'Two Sashes', 2),
  three('ثلاث ضلف', 'Three Sashes', 3),
  four('أربع ضلف', 'Four Sashes', 4),
  six('6 ضلف', 'Six Sashes', 6);

  const SashCount(this.arabicName, this.englishName, this.count);
  final String arabicName;
  final String englishName;
  final int count;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }

  static List<SashCount> getHingeOptions() {
    return [one, two];
  }

  static List<SashCount> getSlidingOptions() {
    return [two, three, four, six];
  }
}

enum TrackCount {
  one('سكة واحدة', 'One Track', 1),
  two('سكتين', 'Two Tracks', 2);

  const TrackCount(this.arabicName, this.englishName, this.count);
  final String arabicName;
  final String englishName;
  final int count;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// خيارات الثوابت
enum FixedPosition {
  none('بدون ثابت', 'No Fixed'),
  right('ثابت يمين', 'Fixed Right'),
  left('ثابت شمال', 'Fixed Left'),
  top('ثابت أعلى', 'Fixed Top'),
  bottom('ثابت أسفل', 'Fixed Bottom');

  const FixedPosition(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// كلاس لإدارة الثوابت المتعددة
class FixedPanels {
  final bool hasRight;
  final bool hasLeft;
  final bool hasTop;
  final bool hasBottom;
  final int rightDivisions;
  final int leftDivisions;
  final int topDivisions;
  final int bottomDivisions;
  final double rightSize;
  final double leftSize;
  final double topSize;
  final double bottomSize;

  const FixedPanels({
    this.hasRight = false,
    this.hasLeft = false,
    this.hasTop = false,
    this.hasBottom = false,
    this.rightDivisions = 1,
    this.leftDivisions = 1,
    this.topDivisions = 1,
    this.bottomDivisions = 1,
    this.rightSize = 10.0,
    this.leftSize = 10.0,
    this.topSize = 10.0,
    this.bottomSize = 10.0,
  });

  FixedPanels copyWith({
    bool? hasRight,
    bool? hasLeft,
    bool? hasTop,
    bool? hasBottom,
    int? rightDivisions,
    int? leftDivisions,
    int? topDivisions,
    int? bottomDivisions,
    double? rightSize,
    double? leftSize,
    double? topSize,
    double? bottomSize,
  }) {
    return FixedPanels(
      hasRight: hasRight ?? this.hasRight,
      hasLeft: hasLeft ?? this.hasLeft,
      hasTop: hasTop ?? this.hasTop,
      hasBottom: hasBottom ?? this.hasBottom,
      rightDivisions: rightDivisions ?? this.rightDivisions,
      leftDivisions: leftDivisions ?? this.leftDivisions,
      topDivisions: topDivisions ?? this.topDivisions,
      bottomDivisions: bottomDivisions ?? this.bottomDivisions,
      rightSize: rightSize ?? this.rightSize,
      leftSize: leftSize ?? this.leftSize,
      topSize: topSize ?? this.topSize,
      bottomSize: bottomSize ?? this.bottomSize,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fixed_has_right': hasRight ? 1 : 0,
      'fixed_has_left': hasLeft ? 1 : 0,
      'fixed_has_top': hasTop ? 1 : 0,
      'fixed_has_bottom': hasBottom ? 1 : 0,
      'fixed_right_divisions': rightDivisions,
      'fixed_left_divisions': leftDivisions,
      'fixed_top_divisions': topDivisions,
      'fixed_bottom_divisions': bottomDivisions,
      'fixed_right_size': rightSize,
      'fixed_left_size': leftSize,
      'fixed_top_size': topSize,
      'fixed_bottom_size': bottomSize,
    };
  }

  factory FixedPanels.fromMap(Map<String, dynamic> map) {
    return FixedPanels(
      hasRight: (map['fixed_has_right'] ?? 0) == 1,
      hasLeft: (map['fixed_has_left'] ?? 0) == 1,
      hasTop: (map['fixed_has_top'] ?? 0) == 1,
      hasBottom: (map['fixed_has_bottom'] ?? 0) == 1,
      rightDivisions: map['fixed_right_divisions'] ?? 1,
      leftDivisions: map['fixed_left_divisions'] ?? 1,
      topDivisions: map['fixed_top_divisions'] ?? 1,
      bottomDivisions: map['fixed_bottom_divisions'] ?? 1,
      rightSize: (map['fixed_right_size'] ?? 10.0).toDouble(),
      leftSize: (map['fixed_left_size'] ?? 10.0).toDouble(),
      topSize: (map['fixed_top_size'] ?? 10.0).toDouble(),
      bottomSize: (map['fixed_bottom_size'] ?? 10.0).toDouble(),
    );
  }

  bool get hasAnyFixed => hasRight || hasLeft || hasTop || hasBottom;
}

// نوع الفتح للمفصلي
enum HingeOpenType {
  normal('مفصلي عادي', 'Normal Hinge'),
  tilt('قلاب', 'Tilt');

  const HingeOpenType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// نوع السلك
enum WireType {
  none('بدون سلك', 'No Wire'),
  fixed('سلك ثابت', 'Fixed Wire'),
  movable('سلك متحرك', 'Movable Wire');

  const WireType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// موقع الشفاط
enum VentPosition {
  none('بدون شفاط', 'No Vent'),
  top('شفاط أعلى', 'Top Vent'),
  bottom('شفاط أسفل', 'Bottom Vent'),
  right('شفاط يمين', 'Right Vent'),
  left('شفاط شمال', 'Left Vent');

  const VentPosition(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// نوع الزجاج
enum GlassType {
  single('زجاج مفرد', 'Single Glass'),
  double('زجاج مزدوج', 'Double Glass');

  const GlassType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// نوع الجورجيا
enum GeorgiaType {
  none('بدون جورجيا', 'No Georgia'),
  classic('جورجيا كلاسيك', 'Classic Georgia'),
  modern('جورجيا حديثة', 'Modern Georgia'),
  decorative('جورجيا زخرفية', 'Decorative Georgia');

  const GeorgiaType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// نوع الفراغ للزجاج المزدوج
enum SpacingType {
  air('هواء', 'Air'),
  argon('أرجون', 'Argon'),
  vacuum('فراغ', 'Vacuum');

  const SpacingType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

// خيارات الرسمة المتقدمة
class DrawingOptions {
  final FixedPanels fixedPanels; // الثوابت المتعددة
  final HingeOpenType hingeOpenType;
  final bool hasPanda; // ضلفة باندا
  final WireType wireType;
  final VentPosition ventPosition;
  final GlassType glassType;
  final String? innerGlassType; // نوع الزجاج الداخلي للمزدوج
  final String? outerGlassType; // نوع الزجاج الخارجي للمزدوج
  final GeorgiaType georgiaType;
  final int georgiaCountWidth; // عدد الجورجيا في العرض
  final int georgiaCountHeight; // عدد الجورجيا في الارتفاع
  final SpacingType spacingType; // نوع الفراغ للزجاج المزدوج

  const DrawingOptions({
    this.fixedPanels = const FixedPanels(),
    this.hingeOpenType = HingeOpenType.normal,
    this.hasPanda = false,
    this.wireType = WireType.none,
    this.ventPosition = VentPosition.none,
    this.glassType = GlassType.single,
    this.innerGlassType,
    this.outerGlassType,
    this.georgiaType = GeorgiaType.none,
    this.georgiaCountWidth = 0,
    this.georgiaCountHeight = 0,
    this.spacingType = SpacingType.air,
  });

  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'hinge_open_type': hingeOpenType.name,
      'has_panda': hasPanda ? 1 : 0,
      'wire_type': wireType.name,
      'vent_position': ventPosition.name,
      'glass_type': glassType.name,
      'inner_glass_type': innerGlassType,
      'outer_glass_type': outerGlassType,
      'georgia_type': georgiaType.name,
      'georgia_count_width': georgiaCountWidth,
      'georgia_count_height': georgiaCountHeight,
      'spacing_type': spacingType.name,
    };

    // إضافة بيانات الثوابت المتعددة
    map.addAll(fixedPanels.toMap());

    return map;
  }

  factory DrawingOptions.fromMap(Map<String, dynamic> map) {
    return DrawingOptions(
      fixedPanels: FixedPanels.fromMap(map),
      hingeOpenType: HingeOpenType.values.firstWhere(
        (e) => e.name == map['hinge_open_type'],
        orElse: () => HingeOpenType.normal,
      ),
      hasPanda: (map['has_panda'] ?? 0) == 1,
      wireType: WireType.values.firstWhere(
        (e) => e.name == map['wire_type'],
        orElse: () => WireType.none,
      ),
      ventPosition: VentPosition.values.firstWhere(
        (e) => e.name == map['vent_position'],
        orElse: () => VentPosition.none,
      ),
      glassType: GlassType.values.firstWhere(
        (e) => e.name == map['glass_type'],
        orElse: () => GlassType.single,
      ),
      innerGlassType: map['inner_glass_type'],
      outerGlassType: map['outer_glass_type'],
      georgiaType: GeorgiaType.values.firstWhere(
        (e) => e.name == map['georgia_type'],
        orElse: () => GeorgiaType.none,
      ),
      georgiaCountWidth: map['georgia_count_width'] ?? 0,
      georgiaCountHeight: map['georgia_count_height'] ?? 0,
      spacingType: SpacingType.values.firstWhere(
        (e) => e.name == map['spacing_type'],
        orElse: () => SpacingType.air,
      ),
    );
  }

  DrawingOptions copyWith({
    FixedPanels? fixedPanels,
    HingeOpenType? hingeOpenType,
    bool? hasPanda,
    WireType? wireType,
    VentPosition? ventPosition,
    GlassType? glassType,
    String? innerGlassType,
    String? outerGlassType,
    GeorgiaType? georgiaType,
    int? georgiaCountWidth,
    int? georgiaCountHeight,
    SpacingType? spacingType,
  }) {
    return DrawingOptions(
      fixedPanels: fixedPanels ?? this.fixedPanels,
      hingeOpenType: hingeOpenType ?? this.hingeOpenType,
      hasPanda: hasPanda ?? this.hasPanda,
      wireType: wireType ?? this.wireType,
      ventPosition: ventPosition ?? this.ventPosition,
      glassType: glassType ?? this.glassType,
      innerGlassType: innerGlassType ?? this.innerGlassType,
      outerGlassType: outerGlassType ?? this.outerGlassType,
      georgiaType: georgiaType ?? this.georgiaType,
      georgiaCountWidth: georgiaCountWidth ?? this.georgiaCountWidth,
      georgiaCountHeight: georgiaCountHeight ?? this.georgiaCountHeight,
      spacingType: spacingType ?? this.spacingType,
    );
  }
}

class AluminumQuotationItem {
  final int? id;
  final int quotationId;
  final WindowDoorType type;
  final ProfileType profileType; // مفصلي أو سحاب
  final int? seriesId; // معرف مجموعة القطاعات
  final String? seriesName; // اسم مجموعة القطاعات
  final SashCount sashCount; // عدد الضلف
  final TrackCount? trackCount; // عدد السكك (للسحاب فقط)
  final double width;
  final double height;
  final int quantity;
  final String notes;
  final DrawingOptions drawingOptions; // خيارات الرسمة المتقدمة
  final DateTime createdAt;

  AluminumQuotationItem({
    this.id,
    required this.quotationId,
    required this.type,
    this.profileType = ProfileType.hinge, // افتراضي مفصلي
    this.seriesId,
    this.seriesName,
    this.sashCount = SashCount.one, // افتراضي ضلفة واحدة
    this.trackCount, // للسحاب فقط
    required this.width,
    required this.height,
    required this.quantity,
    this.notes = '',
    this.drawingOptions = const DrawingOptions(), // خيارات افتراضية
    required this.createdAt,
  });

  double get area => width * height;
  double get totalArea => area * quantity;
  double get perimeter => 2 * (width + height);
  double get totalPerimeter => perimeter * quantity;

  Map<String, dynamic> toMap() {
    final map = {
      'id': id,
      'quotation_id': quotationId,
      'type': type.name,
      'profile_type': profileType.key,
      'series_id': seriesId,
      'series_name': seriesName,
      'sash_count': sashCount.name,
      'track_count': trackCount?.name,
      'width': width,
      'height': height,
      'quantity': quantity,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
    };

    // إضافة خيارات الرسمة
    map.addAll(drawingOptions.toMap());

    return map;
  }

  factory AluminumQuotationItem.fromMap(Map<String, dynamic> map) {
    return AluminumQuotationItem(
      id: map['id']?.toInt(),
      quotationId: map['quotation_id']?.toInt() ?? 0,
      type: WindowDoorType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => WindowDoorType.hinge,
      ),
      profileType: ProfileType.values.firstWhere(
        (t) => t.key == map['profile_type'],
        orElse: () => ProfileType.hinge,
      ),
      seriesId: map['series_id']?.toInt(),
      seriesName: map['series_name'],
      sashCount: SashCount.values.firstWhere(
        (s) => s.name == map['sash_count'],
        orElse: () => SashCount.one,
      ),
      trackCount: map['track_count'] != null
          ? TrackCount.values.firstWhere(
              (t) => t.name == map['track_count'],
              orElse: () => TrackCount.one,
            )
          : null,
      width: (map['width'] ?? 0).toDouble(),
      height: (map['height'] ?? 0).toDouble(),
      quantity: map['quantity']?.toInt() ?? 1,
      notes: map['notes'] ?? '',
      drawingOptions: DrawingOptions.fromMap(map),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  AluminumQuotationItem copyWith({
    int? id,
    int? quotationId,
    WindowDoorType? type,
    ProfileType? profileType,
    int? seriesId,
    String? seriesName,
    SashCount? sashCount,
    TrackCount? trackCount,
    double? width,
    double? height,
    int? quantity,
    String? notes,
    DrawingOptions? drawingOptions,
    DateTime? createdAt,
  }) {
    return AluminumQuotationItem(
      id: id ?? this.id,
      quotationId: quotationId ?? this.quotationId,
      type: type ?? this.type,
      profileType: profileType ?? this.profileType,
      seriesId: seriesId ?? this.seriesId,
      seriesName: seriesName ?? this.seriesName,
      sashCount: sashCount ?? this.sashCount,
      trackCount: trackCount ?? this.trackCount,
      width: width ?? this.width,
      height: height ?? this.height,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      drawingOptions: drawingOptions ?? this.drawingOptions,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AluminumQuotationItem{id: $id, type: $type, width: $width, height: $height, quantity: $quantity}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AluminumQuotationItem &&
        other.id == id &&
        other.quotationId == quotationId &&
        other.type == type &&
        other.width == width &&
        other.height == height &&
        other.quantity == quantity &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        quotationId.hashCode ^
        type.hashCode ^
        width.hashCode ^
        height.hashCode ^
        quantity.hashCode ^
        notes.hashCode;
  }
}
