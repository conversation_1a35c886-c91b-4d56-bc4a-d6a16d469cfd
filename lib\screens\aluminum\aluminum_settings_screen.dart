import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:typed_data';
import '../../l10n/app_localizations.dart';
import '../../models/aluminum_profile.dart';
import '../../models/profile_series.dart';
import '../../models/aluminum_design.dart';
import '../../services/unified_aluminum_service.dart';
import '../../services/aluminum_import_service.dart';
import '../../services/aluminum_upload_service.dart';
import '../../widgets/import_progress_dialog.dart';
import '../../widgets/upload_progress_dialog.dart';
import '../../widgets/series_selection_dialog.dart';
import '../../widgets/existing_series_dialog.dart';
import '../../services/license_service.dart';

class AluminumSettingsScreen extends StatefulWidget {
  const AluminumSettingsScreen({super.key});

  @override
  State<AluminumSettingsScreen> createState() => _AluminumSettingsScreenState();
}

class _AluminumSettingsScreenState extends State<AluminumSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _mainTabController;
  late TabController _hingeTabController;
  late TabController _slidingTabController;
  late TabController _glassTabController;

  @override
  void initState() {
    super.initState();
    _mainTabController = TabController(length: 3, vsync: this);
    _hingeTabController = TabController(length: 2, vsync: this);
    _slidingTabController = TabController(length: 2, vsync: this);
    _glassTabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _mainTabController.dispose();
    _hingeTabController.dispose();
    _slidingTabController.dispose();
    _glassTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.aluminumSettings),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _mainTabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              icon: const Icon(Icons.door_front_door),
              text: localizations.hingeProfiles,
            ),
            Tab(
              icon: const Icon(Icons.door_sliding),
              text: localizations.slidingProfiles,
            ),
            Tab(
              icon: const Icon(Icons.window),
              text: localizations.glass,
            ),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: TabBarView(
          controller: _mainTabController,
          children: [
            _buildHingeProfilesTab(localizations, isTablet),
            _buildSlidingProfilesTab(localizations, isTablet),
            _buildGlassTab(localizations, isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildHingeProfilesTab(AppLocalizations localizations, bool isTablet) {
    return Column(
      children: [
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _hingeTabController,
            indicatorColor: const Color(0xFF607D8B),
            labelColor: const Color(0xFF607D8B),
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: localizations.profiles),
              Tab(text: localizations.designs),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _hingeTabController,
            children: [
              _buildProfilesContent('hinge', localizations, isTablet),
              _buildDesignsContent('hinge', localizations, isTablet),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSlidingProfilesTab(AppLocalizations localizations, bool isTablet) {
    return Column(
      children: [
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _slidingTabController,
            indicatorColor: const Color(0xFF607D8B),
            labelColor: const Color(0xFF607D8B),
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: localizations.profiles),
              Tab(text: localizations.designs),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _slidingTabController,
            children: [
              _buildProfilesContent('sliding', localizations, isTablet),
              _buildDesignsContent('sliding', localizations, isTablet),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGlassTab(AppLocalizations localizations, bool isTablet) {
    return Column(
      children: [
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _glassTabController,
            indicatorColor: const Color(0xFF607D8B),
            labelColor: const Color(0xFF607D8B),
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: localizations.glassTypes),
              Tab(text: localizations.georgia),
              Tab(text: localizations.spacing),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _glassTabController,
            children: [
              _buildGlassTypesContent(localizations, isTablet),
              _buildGeorgiaContent(localizations, isTablet),
              _buildSpacingContent(localizations, isTablet),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfilesContent(String type, AppLocalizations localizations, bool isTablet) {
    final profileType = type == 'hinge' ? ProfileType.hinge : ProfileType.sliding;

    return _ProfilesContentWidget(
      profileType: profileType,
      localizations: localizations,
      isTablet: isTablet,
    );
  }

  Widget _buildDesignsContent(String type, AppLocalizations localizations, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionHeader(
            '${type == 'hinge' ? localizations.hingeProfiles : localizations.slidingProfiles} - ${localizations.designs}',
            Icons.design_services,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: type == 'hinge'
                ? _buildHingeDesignsContent(localizations, isTablet)
                : _buildSlidingDesignsContent(localizations, isTablet),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassTypesContent(AppLocalizations localizations, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionHeader(
            localizations.glassTypes,
            Icons.window,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildComingSoonContent(
              'سيتم إضافة أنواع الزجاج قريباً',
              Icons.window,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeorgiaContent(AppLocalizations localizations, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionHeader(
            localizations.georgia,
            Icons.grid_view,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildComingSoonContent(
              'سيتم إضافة إعدادات الجورجيا قريباً',
              Icons.grid_view,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpacingContent(AppLocalizations localizations, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionHeader(
            localizations.spacing,
            Icons.space_bar,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildComingSoonContent(
              'سيتم إضافة إعدادات الفراغ قريباً',
              Icons.space_bar,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF607D8B),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComingSoonContent(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.orange.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.construction,
                  color: Colors.orange[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'قيد التطوير',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء واجهة تخصيمات المفصلي
  Widget _buildHingeDesignsContent(AppLocalizations localizations, bool isTablet) {
    return _HingeDesignsWidget(
      localizations: localizations,
      isTablet: isTablet,
    );
  }

  // بناء واجهة تخصيمات السحاب
  Widget _buildSlidingDesignsContent(AppLocalizations localizations, bool isTablet) {
    return _SlidingDesignsWidget(
      localizations: localizations,
      isTablet: isTablet,
    );
  }
}

class _ProfilesContentWidget extends StatefulWidget {
  final ProfileType profileType;
  final AppLocalizations localizations;
  final bool isTablet;

  const _ProfilesContentWidget({
    required this.profileType,
    required this.localizations,
    required this.isTablet,
  });

  @override
  State<_ProfilesContentWidget> createState() => _ProfilesContentWidgetState();
}

class _ProfilesContentWidgetState extends State<_ProfilesContentWidget> {
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();
  // استخدام الخدمة الموحدة لجميع العمليات
  UnifiedAluminumService get _seriesService => _aluminumService;
  UnifiedAluminumService get _profileService => _aluminumService;

  List<ProfileSeries> _series = [];
  ProfileSeries? _selectedSeries;
  List<AluminumProfile> _profiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل المجموعات والقطاعات أولاً
      await _loadSeries();

      // إذا لم توجد مجموعات، أدرج البيانات التجريبية
      if (_series.isEmpty) {
        await _seriesService.insertSampleData();
        await _profileService.insertSampleData();
        await _loadSeries();
      }
    } catch (e) {
      // في حالة وجود خطأ، أعد تعيين قاعدة البيانات
      await _profileService.resetDatabase();
      await _seriesService.resetDatabase();
      await _seriesService.insertSampleData();
      await _profileService.insertSampleData();
      await _loadSeries();
    }
  }

  Future<void> _loadSeries() async {
    setState(() => _isLoading = true);
    try {
      final seriesData = await _seriesService.getSeriesByType(widget.profileType);
      final series = seriesData.map((data) => ProfileSeries.fromMap(data)).toList();
      setState(() {
        _series = series;
        _isLoading = false;
        if (series.isNotEmpty) {
          _selectedSeries = series.first;
          _loadProfiles();
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المجموعات: $e')),
        );
      }
    }
  }

  Future<void> _loadProfiles() async {
    if (_selectedSeries == null) return;

    try {
      final profilesData = await _profileService.getProfilesBySeries(_selectedSeries!.id!);
      final profiles = profilesData.map((data) => AluminumProfile.fromMap(data)).toList();
      setState(() {
        _profiles = profiles;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل القطاعات: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_series.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: widget.profileType == ProfileType.hinge
                      ? const Color(0xFF607D8B).withValues(alpha: 0.1)
                      : const Color(0xFF00BCD4).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.profileType == ProfileType.hinge
                      ? Icons.door_front_door
                      : Icons.door_sliding,
                  size: 64,
                  color: widget.profileType == ProfileType.hinge
                      ? const Color(0xFF607D8B)
                      : const Color(0xFF00BCD4),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'لا توجد مجموعات قطاعات',
                style: TextStyle(
                  fontSize: 20,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'ابدأ بإضافة مجموعة قطاعات جديدة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 32),

              // أزرار العمليات
              Column(
                children: [
                  // زر تحميل من قاعدة البيانات الأونلاين
                  ElevatedButton.icon(
                    onPressed: () => _showImportFromOnlineDialog(),
                    icon: const Icon(Icons.cloud_download, size: 20),
                    label: const Text('تحميل من قاعدة البيانات الأونلاين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF607D8B),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // زر إضافة مجموعة جديدة
                  ElevatedButton.icon(
                    onPressed: () => _showAddEditSeriesDialog(),
                    icon: const Icon(Icons.add_circle_outline, size: 20),
                    label: const Text('إضافة مجموعة جديدة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.profileType == ProfileType.hinge
                          ? const Color(0xFF607D8B)
                          : const Color(0xFF00BCD4),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ComboBox للمجموعات مع أزرار الإدارة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF607D8B)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: widget.isTablet
                ? Row(
                    children: [
                      Icon(
                        widget.profileType == ProfileType.hinge
                            ? Icons.door_front_door
                            : Icons.door_sliding,
                        color: const Color(0xFF607D8B),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'اختر المجموعة:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<ProfileSeries>(
                            value: _selectedSeries,
                            isExpanded: true,
                            hint: const Text('اختر مجموعة القطاعات'),
                            onChanged: (ProfileSeries? newValue) {
                              setState(() {
                                _selectedSeries = newValue;
                                _profiles.clear();
                              });
                              if (newValue != null) {
                                _loadProfiles();
                              }
                            },
                            items: _series.map<DropdownMenuItem<ProfileSeries>>((ProfileSeries series) {
                              return DropdownMenuItem<ProfileSeries>(
                                value: series,
                                child: Text(
                                  '${series.name} (${series.code})',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // أزرار إدارة المجموعات
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // زر تحميل من قاعدة البيانات الأونلاين
                          IconButton(
                            onPressed: () => _showImportFromOnlineDialog(),
                            icon: const Icon(Icons.cloud_download),
                            color: const Color(0xFF607D8B),
                            tooltip: 'تحميل من قاعدة البيانات الأونلاين',
                          ),
                          // زر رفع إعدادات القطاع الحالي
                          if (LicenseService.isDevValue == 1)
                            IconButton(
                              onPressed: () => _showUploadConfirmationDialog(),
                              icon: const Icon(Icons.cloud_upload),
                              color: const Color(0xFF4CAF50),
                              tooltip: 'رفع إعدادات القطاع الحالي',
                            ),
                          // زر إضافة مجموعة جديدة
                          IconButton(
                            onPressed: () => _showAddEditSeriesDialog(),
                            icon: const Icon(Icons.add_circle_outline),
                            color: Colors.green,
                            tooltip: 'إضافة مجموعة جديدة',
                          ),
                          // زر تعديل المجموعة الحالية
                          if (_selectedSeries != null)
                            IconButton(
                              onPressed: () => _showAddEditSeriesDialog(series: _selectedSeries),
                              icon: const Icon(Icons.edit),
                              color: Colors.blue,
                              tooltip: 'تعديل المجموعة',
                            ),
                          // زر حذف المجموعة الحالية
                          if (_selectedSeries != null)
                            IconButton(
                              onPressed: () => _deleteSeries(_selectedSeries!),
                              icon: const Icon(Icons.delete_outline),
                              color: Colors.red,
                              tooltip: 'حذف المجموعة',
                            ),
                        ],
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            widget.profileType == ProfileType.hinge
                                ? Icons.door_front_door
                                : Icons.door_sliding,
                            color: const Color(0xFF607D8B),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'اختر المجموعة:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[700],
                            ),
                          ),
                          const Spacer(),
                          // أزرار إدارة المجموعات للهواتف
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _showImportFromOnlineDialog(),
                                icon: const Icon(Icons.cloud_download, size: 20),
                                color: const Color(0xFF607D8B),
                                tooltip: 'تحميل أونلاين',
                              ),
                              if (LicenseService.isDevValue == 1)
                                IconButton(
                                  onPressed: () => _showUploadConfirmationDialog(),
                                  icon: const Icon(Icons.cloud_upload, size: 20),
                                  color: const Color(0xFF4CAF50),
                                  tooltip: 'رفع إعدادات القطاع الحالي',
                                ),
                              IconButton(
                                onPressed: () => _showAddEditSeriesDialog(),
                                icon: const Icon(Icons.add_circle_outline, size: 20),
                                color: Colors.green,
                                tooltip: 'إضافة مجموعة',
                              ),
                              if (_selectedSeries != null)
                                IconButton(
                                  onPressed: () => _showAddEditSeriesDialog(series: _selectedSeries),
                                  icon: const Icon(Icons.edit, size: 20),
                                  color: Colors.blue,
                                  tooltip: 'تعديل',
                                ),
                              if (_selectedSeries != null)
                                IconButton(
                                  onPressed: () => _deleteSeries(_selectedSeries!),
                                  icon: const Icon(Icons.delete_outline, size: 20),
                                  color: Colors.red,
                                  tooltip: 'حذف',
                                ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      DropdownButtonHideUnderline(
                        child: DropdownButton<ProfileSeries>(
                          value: _selectedSeries,
                          isExpanded: true,
                          hint: const Text('اختر مجموعة القطاعات'),
                          onChanged: (ProfileSeries? newValue) {
                            setState(() {
                              _selectedSeries = newValue;
                              _profiles.clear();
                            });
                            if (newValue != null) {
                              _loadProfiles();
                            }
                          },
                          items: _series.map<DropdownMenuItem<ProfileSeries>>((ProfileSeries series) {
                            return DropdownMenuItem<ProfileSeries>(
                              value: series,
                              child: Text(
                                '${series.name} (${series.code})',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
          ),
          const SizedBox(height: 20),

          // عرض التبويبات
          Expanded(
            child: _selectedSeries == null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'اختر مجموعة لعرض القطاعات',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : _buildCategoryTabs(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    // الحصول على فئات القطاعات حسب النوع
    final categories = widget.profileType == ProfileType.hinge
        ? ProfileCategory.getHingeCategories()
        : ProfileCategory.getSlidingCategories();

    // تجميع القطاعات حسب الفئة
    Map<ProfileCategory, List<AluminumProfile>> groupedProfiles = {};
    for (var profile in _profiles) {
      if (!groupedProfiles.containsKey(profile.category)) {
        groupedProfiles[profile.category] = [];
      }
      groupedProfiles[profile.category]!.add(profile);
    }

    return DefaultTabController(
      length: categories.length,
      child: Column(
        children: [
          // شريط التبويبات
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              isScrollable: true,
              indicatorColor: const Color(0xFF607D8B),
              labelColor: const Color(0xFF607D8B),
              unselectedLabelColor: Colors.grey,
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
              tabAlignment: TabAlignment.start,
              tabs: categories.map((category) {
                final categoryProfiles = groupedProfiles[category] ?? [];
                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getCategoryIcon(category),
                        size: 18,
                        color: _getCategoryColor(category),
                      ),
                      const SizedBox(width: 8),
                      Text(category.arabicName),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getCategoryColor(category).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '${categoryProfiles.length}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: _getCategoryColor(category),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 16),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              children: categories.map((category) {
                final categoryProfiles = groupedProfiles[category] ?? [];
                return _buildCategoryContent(category, categoryProfiles);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryContent(ProfileCategory category, List<AluminumProfile> profiles) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط الأدوات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getCategoryColor(category),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  _getCategoryIcon(category),
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'قطاعات ${category.arabicName}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${profiles.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _addNewProfile(category),
                  icon: const Icon(Icons.add, color: Colors.white),
                  tooltip: 'إضافة قطاع جديد',
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // جدول القطاعات
          Expanded(
            child: _buildProfilesDataGrid(category, profiles),
          ),
        ],
      ),
    );
  }



  IconData _getCategoryIcon(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Icons.crop_square;
      case ProfileCategory.bar:
        return Icons.horizontal_rule;
      case ProfileCategory.dalfa:
        return Icons.door_front_door;
      case ProfileCategory.marad:
        return Icons.vertical_split;
      case ProfileCategory.baketa:
        return Icons.linear_scale;
      case ProfileCategory.soas:
        return Icons.horizontal_distribute;
      case ProfileCategory.dalfaSilk:
        return Icons.grid_4x4;
      case ProfileCategory.olba:
        return Icons.inventory_2;
      case ProfileCategory.filta:
        return Icons.circle;
      case ProfileCategory.skineh:
        return Icons.linear_scale;
      case ProfileCategory.anf:
        return Icons.arrow_forward;
    }
  }

  Color _getCategoryColor(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return const Color(0xFF2196F3); // Blue
      case ProfileCategory.bar:
        return const Color(0xFF4CAF50); // Green
      case ProfileCategory.dalfa:
        return const Color(0xFF9C27B0); // Purple
      case ProfileCategory.marad:
        return const Color(0xFFFF9800); // Orange
      case ProfileCategory.baketa:
        return const Color(0xFFF44336); // Red
      case ProfileCategory.soas:
        return const Color(0xFF00BCD4); // Cyan
      case ProfileCategory.dalfaSilk:
        return const Color(0xFF795548); // Brown
      case ProfileCategory.olba:
        return const Color(0xFF607D8B); // Blue Grey
      case ProfileCategory.filta:
        return const Color(0xFF9E9E9E); // Grey
      case ProfileCategory.skineh:
        return const Color(0xFF3F51B5); // Indigo
      case ProfileCategory.anf:
        return const Color(0xFFE91E63); // Pink
    }
  }

  Widget _buildProfilesDataGrid(ProfileCategory category, List<AluminumProfile> profiles) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // رأس الجدول
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: widget.isTablet
                ? Row(
                    children: [
                      const Expanded(flex: 1, child: Text('صورة', style: TextStyle(fontWeight: FontWeight.bold))),
                      const Expanded(flex: 3, child: Text('اسم القطاع', style: TextStyle(fontWeight: FontWeight.bold))),
                      const Expanded(flex: 2, child: Text('رقم القطاع', style: TextStyle(fontWeight: FontWeight.bold))),
                      const Expanded(flex: 1, child: Text('سمك (سم)', style: TextStyle(fontWeight: FontWeight.bold))),
                      // إظهار أعمدة الشفة للحلف فقط
                      if (category == ProfileCategory.halaf) ...[
                        const Expanded(flex: 2, child: Text('نوع الشفة', style: TextStyle(fontWeight: FontWeight.bold))),
                        const Expanded(flex: 1, child: Text('سمك الشفة (سم)', style: TextStyle(fontWeight: FontWeight.bold))),
                      ],
                      // إضافة عمود الباكتة للضلفة المفصلي فقط
                      if (category == ProfileCategory.dalfa && widget.profileType == ProfileType.hinge) ...[
                        const Expanded(flex: 1, child: Text('بالباكتة', style: TextStyle(fontWeight: FontWeight.bold))),
                      ],
                      // إضافة عمود الضلفة للسكينة السحاب فقط
                      if (category == ProfileCategory.skineh && widget.profileType == ProfileType.sliding) ...[
                        const Expanded(flex: 1, child: Text('بالضلفة', style: TextStyle(fontWeight: FontWeight.bold))),
                      ],
                      const Expanded(flex: 1, child: Text('الإجراءات', style: TextStyle(fontWeight: FontWeight.bold))),
                    ],
                  )
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        const SizedBox(width: 60, child: Text('صورة', style: TextStyle(fontWeight: FontWeight.bold))),
                        const SizedBox(width: 120, child: Text('اسم القطاع', style: TextStyle(fontWeight: FontWeight.bold))),
                        const SizedBox(width: 100, child: Text('رقم القطاع', style: TextStyle(fontWeight: FontWeight.bold))),
                        const SizedBox(width: 80, child: Text('سمك (سم)', style: TextStyle(fontWeight: FontWeight.bold))),
                        // إظهار أعمدة الشفة للحلف فقط
                        if (category == ProfileCategory.halaf) ...[
                          const SizedBox(width: 100, child: Text('نوع الشفة', style: TextStyle(fontWeight: FontWeight.bold))),
                          const SizedBox(width: 80, child: Text('سمك الشفة (سم)', style: TextStyle(fontWeight: FontWeight.bold))),
                        ],
                        // إضافة عمود الباكتة للضلفة المفصلي فقط
                        if (category == ProfileCategory.dalfa && widget.profileType == ProfileType.hinge) ...[
                          const SizedBox(width: 80, child: Text('بالباكتة', style: TextStyle(fontWeight: FontWeight.bold))),
                        ],
                        // إضافة عمود الضلفة للسكينة السحاب فقط
                        if (category == ProfileCategory.skineh && widget.profileType == ProfileType.sliding) ...[
                          const SizedBox(width: 80, child: Text('بالضلفة', style: TextStyle(fontWeight: FontWeight.bold))),
                        ],
                        const SizedBox(width: 100, child: Text('الإجراءات', style: TextStyle(fontWeight: FontWeight.bold))),
                      ],
                    ),
                  ),
          ),

          // محتوى الجدول
          Expanded(
            child: profiles.isEmpty
                ? Center(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: _getCategoryColor(category).withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _getCategoryIcon(category),
                              size: 64,
                              color: _getCategoryColor(category),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد قطاعات ${category.arabicName}',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            onPressed: () => _addNewProfile(category),
                            icon: const Icon(Icons.add, size: 16),
                            label: Text('إضافة ${category.arabicName}'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _getCategoryColor(category),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: profiles.length,
                    itemBuilder: (context, index) {
                      final profile = profiles[index];
                      return _buildProfileRow(profile, index);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileRow(AluminumProfile profile, int index) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
        color: index % 2 == 0 ? Colors.white : Colors.grey[50],
      ),
      child: widget.isTablet
          ? Row(
              children: [
                // صورة القطاع
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                        color: Colors.grey[50],
                      ),
                      child: profile.imagePath != null && profile.imagePath!.isNotEmpty
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.memory(
                                Uint8List.fromList(profile.imagePath!.codeUnits),
                                fit: BoxFit.contain,
                                width: double.infinity,
                                height: double.infinity,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.broken_image,
                                    color: Colors.grey[400],
                                    size: 24,
                                  );
                                },
                              ),
                            )
                          : Icon(
                              Icons.image,
                              color: Colors.grey[400],
                              size: 24,
                            ),
                    ),
                  ),
                ),

                // اسم القطاع
                Expanded(
                  flex: 3,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Text(
                      profile.name,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ),

                // رقم القطاع
                Expanded(
                  flex: 2,
                  child: Text(
                    profile.code,
                    textAlign: TextAlign.center,
                  ),
                ),

                // سمك القطاع
                Expanded(
                  flex: 1,
                  child: Text(
                    profile.thickness?.toString() ?? '-',
                    textAlign: TextAlign.center,
                  ),
                ),

                // إظهار أعمدة الشفة للحلف فقط
                if (profile.category == ProfileCategory.halaf) ...[
                  // نوع الشفة
                  Expanded(
                    flex: 2,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: profile.lipType != null ? Colors.blue[50] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: profile.lipType != null ? Colors.blue[200]! : Colors.grey[300]!,
                        ),
                      ),
                      child: Text(
                        profile.lipType ?? 'بدون شفة',
                        style: TextStyle(
                          color: profile.lipType != null ? Colors.blue[700] : Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // سمك الشفة
                  Expanded(
                    flex: 1,
                    child: Text(
                      profile.lipThickness?.toString() ?? '-',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],

                // إضافة عمود الباكتة للضلفة المفصلي فقط
                if (profile.category == ProfileCategory.dalfa && profile.type == ProfileType.hinge) ...[
                  Expanded(
                    flex: 1,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: profile.withBaketa == true ? Colors.purple[50] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: profile.withBaketa == true ? Colors.purple[200]! : Colors.grey[300]!,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            profile.withBaketa == true ? Icons.check_circle : Icons.cancel,
                            color: profile.withBaketa == true ? Colors.purple[600] : Colors.grey[600],
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            profile.withBaketa == true ? 'نعم' : 'لا',
                            style: TextStyle(
                              color: profile.withBaketa == true ? Colors.purple[700] : Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // إضافة عمود الضلفة للسكينة السحاب فقط
                if (profile.category == ProfileCategory.skineh && profile.type == ProfileType.sliding) ...[
                  Expanded(
                    flex: 1,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: profile.withDalfa == true ? Colors.indigo[50] : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: profile.withDalfa == true ? Colors.indigo[200]! : Colors.grey[300]!,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            profile.withDalfa == true ? Icons.check_circle : Icons.cancel,
                            color: profile.withDalfa == true ? Colors.indigo[600] : Colors.grey[600],
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            profile.withDalfa == true ? 'نعم' : 'لا',
                            style: TextStyle(
                              color: profile.withDalfa == true ? Colors.indigo[700] : Colors.grey[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // الإجراءات
                Expanded(
                  flex: 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () => _editProfile(profile),
                        borderRadius: BorderRadius.circular(4),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.blue,
                            size: 16,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      InkWell(
                        onTap: () => _deleteProfile(profile),
                        borderRadius: BorderRadius.circular(4),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // صورة القطاع
                  SizedBox(
                    width: 60,
                    child: Center(
                      child: Container(
                        height: 50,
                        width: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                          color: Colors.grey[50],
                        ),
                        child: profile.imagePath != null && profile.imagePath!.isNotEmpty
                            ? ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.memory(
                                  Uint8List.fromList(profile.imagePath!.codeUnits),
                                  fit: BoxFit.contain,
                                  width: double.infinity,
                                  height: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Icon(
                                      Icons.broken_image,
                                      color: Colors.grey[400],
                                      size: 24,
                                    );
                                  },
                                ),
                              )
                            : Icon(
                                Icons.image,
                                color: Colors.grey[400],
                                size: 24,
                              ),
                      ),
                    ),
                  ),

                  // اسم القطاع
                  SizedBox(
                    width: 120,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Text(
                        profile.name,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),

                  // رقم القطاع
                  SizedBox(
                    width: 100,
                    child: Text(
                      profile.code,
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // سمك القطاع
                  SizedBox(
                    width: 80,
                    child: Text(
                      profile.thickness?.toString() ?? '-',
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // إظهار أعمدة الشفة للحلف فقط
                  if (profile.category == ProfileCategory.halaf) ...[
                    // نوع الشفة
                    SizedBox(
                      width: 100,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: profile.lipType != null ? Colors.blue[50] : Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: profile.lipType != null ? Colors.blue[200]! : Colors.grey[300]!,
                          ),
                        ),
                        child: Text(
                          profile.lipType ?? 'بدون شفة',
                          style: TextStyle(
                            color: profile.lipType != null ? Colors.blue[700] : Colors.grey[600],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),

                    // سمك الشفة
                    SizedBox(
                      width: 80,
                      child: Text(
                        profile.lipThickness?.toString() ?? '-',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],

                  // إضافة عمود الباكتة للضلفة المفصلي فقط
                  if (profile.category == ProfileCategory.dalfa && profile.type == ProfileType.hinge) ...[
                    SizedBox(
                      width: 80,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: profile.withBaketa == true ? Colors.purple[50] : Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: profile.withBaketa == true ? Colors.purple[200]! : Colors.grey[300]!,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              profile.withBaketa == true ? Icons.check_circle : Icons.cancel,
                              color: profile.withBaketa == true ? Colors.purple[600] : Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              profile.withBaketa == true ? 'نعم' : 'لا',
                              style: TextStyle(
                                color: profile.withBaketa == true ? Colors.purple[700] : Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  // إضافة عمود الضلفة للسكينة السحاب فقط
                  if (profile.category == ProfileCategory.skineh && profile.type == ProfileType.sliding) ...[
                    SizedBox(
                      width: 80,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: profile.withDalfa == true ? Colors.indigo[50] : Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: profile.withDalfa == true ? Colors.indigo[200]! : Colors.grey[300]!,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              profile.withDalfa == true ? Icons.check_circle : Icons.cancel,
                              color: profile.withDalfa == true ? Colors.indigo[600] : Colors.grey[600],
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              profile.withDalfa == true ? 'نعم' : 'لا',
                              style: TextStyle(
                                color: profile.withDalfa == true ? Colors.indigo[700] : Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  // الإجراءات
                  SizedBox(
                    width: 100,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InkWell(
                          onTap: () => _editProfile(profile),
                          borderRadius: BorderRadius.circular(4),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            child: const Icon(
                              Icons.edit,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        InkWell(
                          onTap: () => _deleteProfile(profile),
                          borderRadius: BorderRadius.circular(4),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            child: const Icon(
                              Icons.delete,
                              color: Colors.red,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  void _addNewProfile(ProfileCategory category) {
    showDialog(
      context: context,
      builder: (context) => _AddProfileDialog(
        category: category,
        profileType: widget.profileType,
        seriesId: _selectedSeries?.id,
        onProfileAdded: () {
          _loadProfiles(); // إعادة تحميل القطاعات
        },
      ),
    );
  }

  void _editProfile(AluminumProfile profile) {
    showDialog(
      context: context,
      builder: (context) => _EditProfileDialog(
        profile: profile,
        onProfileUpdated: _loadProfiles,
      ),
    );
  }

  Future<void> _deleteProfile(AluminumProfile profile) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف القطاع "${profile.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _profileService.deleteProfile(profile.id!);
        await _loadProfiles();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف ${profile.name} بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف القطاع: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // وظائف إدارة المجموعات
  Future<void> _showAddEditSeriesDialog({ProfileSeries? series}) async {
    final result = await showDialog<ProfileSeries>(
      context: context,
      builder: (context) => _AddEditSeriesDialog(
        profileType: widget.profileType,
        series: series,
      ),
    );

    if (result != null) {
      try {
        if (series == null) {
          // إضافة مجموعة جديدة
          await _seriesService.insertSeries(result.toMap());
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إضافة المجموعة "${result.name}" بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          // تعديل مجموعة موجودة
          await _seriesService.updateProfileSeries(result);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحديث المجموعة "${result.name}" بنجاح'),
                backgroundColor: Colors.blue,
              ),
            );
          }
        }
        await _loadSeries();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ المجموعة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteSeries(ProfileSeries series) async {
    // التحقق من وجود قطاعات مرتبطة بالمجموعة
    int profilesCount = 0;
    try {
      final profilesData = await _profileService.getProfilesBySeries(series.id!);
      profilesCount = profilesData.length;
    } catch (e) {
      // في حالة وجود خطأ، نفترض عدم وجود قطاعات
      profilesCount = 0;
    }

    if (!mounted) return;

    // عرض رسالة تحذير مع تفاصيل ما سيتم حذفه
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red, size: 28),
            const SizedBox(width: 8),
            const Text('تحذير - حذف المجموعة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سيتم حذف المجموعة "${series.name}" نهائياً',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 12),
            if (profilesCount > 0) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.inventory_2, color: Colors.orange.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'القطاعات المرتبطة:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'سيتم حذف $profilesCount قطاع مرتبط بهذه المجموعة',
                      style: TextStyle(color: Colors.orange.shade700),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.delete_forever, color: Colors.red.shade700, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'هذا الإجراء لا يمكن التراجع عنه!',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل أنت متأكد من المتابعة؟',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.delete_forever, size: 18),
                const SizedBox(width: 4),
                const Text('حذف نهائي'),
              ],
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // استخدام الدالة الجديدة لحذف المجموعة مع جميع القطاعات المرتبطة بها
        await _profileService.deleteSeriesWithProfiles(series.id!);

        // إذا كانت المجموعة المحذوفة هي المختارة، قم بإلغاء الاختيار
        if (_selectedSeries?.id == series.id) {
          setState(() {
            _selectedSeries = null;
            _profiles.clear();
          });
        }

        await _loadSeries();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      profilesCount > 0
                        ? 'تم حذف المجموعة "${series.name}" و $profilesCount قطاع بنجاح'
                        : 'تم حذف المجموعة "${series.name}" بنجاح',
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text('خطأ في حذف المجموعة: $e')),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  /// عرض حوار استيراد من قاعدة البيانات الأونلاين
  Future<void> _showImportFromOnlineDialog() async {
    try {
      // عرض مؤشر التحميل أثناء اختبار الاتصال
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('جاري اختبار الاتصال بقاعدة البيانات...'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 10),
          ),
        );
      }

      // اختبار الاتصال أولاً
      final connectionResult = await AluminumImportService.testConnection();

      // إخفاء مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      if (!connectionResult['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ فشل الاتصال: ${connectionResult['error']}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      // عرض رسالة نجاح الاتصال
      if (mounted) {
        final tablesCount = connectionResult['tables_count'] ?? 0;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ ${connectionResult['message']} - تم العثور على $tablesCount جدول'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      // جلب المجموعات المتاحة
      final seriesResult = await AluminumImportService.getOnlineProfileSeries(widget.profileType);
      if (!seriesResult['success']) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في جلب المجموعات: ${seriesResult['error']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final seriesList = seriesResult['series'] as List;
      final availableSeries = seriesList.cast<ProfileSeries>();
      
      // عرض حوار اختيار المجموعات (حتى لو كانت القائمة فارغة)
      if (mounted) {
        final selectedSeries = await showDialog<List<ProfileSeries>>(
          context: context,
          builder: (context) => SeriesSelectionDialog(
            availableSeries: availableSeries,
            profileType: widget.profileType,
          ),
        );

        if (selectedSeries != null && selectedSeries.isNotEmpty) {
          // بدء عملية الاستيراد
          _startImportProcess(selectedSeries);
        }
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ غير متوقع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// بدء عملية الاستيراد
  Future<void> _startImportProcess(List<ProfileSeries> selectedSeries) async {
    if (!mounted) return;

    // التحقق من وجود مجموعات مطابقة أولاً
    Map<String, String> userChoices = {};
    
    for (final series in selectedSeries) {
      // التحقق من وجود المجموعة محلياً
      final existingSeries = await UnifiedAluminumService().getSeriesByCode(series.code);
      
      if (existingSeries != null) {
        // عرض نافذة الاختيار للمستخدم
        final userChoice = await showDialog<String>(
          context: context,
          barrierDismissible: false,
          builder: (context) => ExistingSeriesDialog(
            existingSeries: existingSeries,
            onlineSeries: series,
          ),
        );
        
        if (userChoice == null) {
          // المستخدم ألغى العملية
          return;
        }
        
        userChoices[series.code] = userChoice;
      }
    }

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ImportProgressDialog(
        title: 'استيراد مجموعات ${widget.profileType == ProfileType.hinge ? 'المفصلي' : 'السحاب'}',
        importFunction: (onProgress) async {
          return await AluminumImportService.importMultipleSeries(
            selectedSeries,
            onProgress,
            userChoices: userChoices,
          );
        },
      ),
    );

    if (result == true && mounted) {
      // إعادة تحميل البيانات
      await _loadSeries();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاستيراد بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// عرض نافذة تأكيد رفع البيانات
  Future<void> _showUploadConfirmationDialog() async {
    if (_selectedSeries == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار مجموعة قطاعات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.cloud_upload,
              color: const Color(0xFF4CAF50),
              size: 28,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'رفع البيانات إلى الأونلاين',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'المجموعة المحددة:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${_selectedSeries!.name} (${_selectedSeries!.code})',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'عدد القطاعات: ${_profiles.length}',
                    style: TextStyle(
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning_amber, color: Colors.orange[700], size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'تحذير مهم:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'سيتم تنفيذ العمليات التالية:',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. حذف المجموعة والقطاعات الموجودة في قاعدة البيانات الأونلاين',
                    style: TextStyle(color: Colors.orange[700]),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '2. رفع المجموعة والقطاعات من البيانات المحلية',
                    style: TextStyle(color: Colors.orange[700]),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل أنت متأكد من المتابعة؟',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
            ),
            child: const Text('نعم، ارفع البيانات'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _startUploadProcess();
    }
  }

  /// بدء عملية رفع البيانات
  Future<void> _startUploadProcess() async {
    if (!mounted || _selectedSeries == null) return;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => UploadProgressDialog(
        title: 'رفع مجموعة ${widget.profileType == ProfileType.hinge ? 'المفصلي' : 'السحاب'}',
        uploadFunction: (onProgress) async {
          return await AluminumUploadService.uploadSeriesWithProfiles(
            _selectedSeries!.id!,
            onProgress,
          );
        },
      ),
    );

    if (result == true && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم رفع البيانات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}

// نافذة حوار إضافة قطاع جديد
class _AddProfileDialog extends StatefulWidget {
  final ProfileCategory category;
  final ProfileType profileType;
  final int? seriesId;
  final VoidCallback onProfileAdded;

  const _AddProfileDialog({
    required this.category,
    required this.profileType,
    required this.seriesId,
    required this.onProfileAdded,
  });

  @override
  State<_AddProfileDialog> createState() => _AddProfileDialogState();
}

class _AddProfileDialogState extends State<_AddProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _thicknessController = TextEditingController();

  String _selectedLipType = 'بدون شفة';
  final _lipThicknessController = TextEditingController();
  bool _withBaketa = false; // للضلفة المفصلي
  bool _withDalfa = false; // للسكينة السحاب
  Uint8List? _selectedImageData;
  bool _isLoading = false;

  final List<String> _lipTypes = ['بدون شفة', 'شفة منه فيه', 'بار خارجى'];

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _thicknessController.dispose();
    _lipThicknessController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // عنوان النافذة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getCategoryColor(widget.category),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(widget.category),
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'إضافة قطاع ${widget.category.arabicName} جديد',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // محتوى النموذج
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 16),
                      // إخفاء قسم الشفة للقطاعات المحددة
                      if (_shouldShowLipSection()) ...[
                        _buildLipSection(),
                        const SizedBox(height: 16),
                      ],
                      // إضافة قسم الباكتة للضلفة المفصلي فقط
                      if (widget.category == ProfileCategory.dalfa &&
                          widget.profileType == ProfileType.hinge) ...[
                        _buildBaketaSection(),
                        const SizedBox(height: 16),
                      ],
                      // إضافة قسم الضلفة للسكينة السحاب فقط
                      if (widget.category == ProfileCategory.skineh &&
                          widget.profileType == ProfileType.sliding) ...[
                        _buildDalfaSection(),
                        const SizedBox(height: 16),
                      ],
                      _buildImageSection(),
                    ],
                  ),
                ),
              ),
            ),

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getCategoryColor(widget.category),
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // قسم المعلومات الأساسية
  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الأساسية',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسم القطاع *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.label),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم القطاع';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _codeController,
                  decoration: const InputDecoration(
                    labelText: 'رقم القطاع *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.qr_code),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم القطاع';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _thicknessController,
                  decoration: const InputDecoration(
                    labelText: 'السمك (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.straighten),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  // قسم الشفة
  Widget _buildLipSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الشفة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _lipTypes.contains(_selectedLipType) ? _selectedLipType : 'بدون شفة',
                  decoration: const InputDecoration(
                    labelText: 'نوع الشفة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.layers),
                  ),
                  items: _lipTypes.map((String type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedLipType = newValue;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _lipThicknessController,
                  decoration: const InputDecoration(
                    labelText: 'سمك الشفة (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.straighten),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة لتحديد ما إذا كان يجب عرض قسم الشفة
  bool _shouldShowLipSection() {
    // القطاعات التي تحتاج حقول الشفة (الحلف فقط)
    return widget.category == ProfileCategory.halaf;
  }

  // قسم الباكتة (للضلفة المفصلي فقط)
  Widget _buildBaketaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الباكتة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.check_box,
                color: Colors.purple[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'بالباكتة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              Switch(
                value: _withBaketa,
                onChanged: (bool value) {
                  setState(() {
                    _withBaketa = value;
                  });
                },
                activeColor: Colors.purple[600],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قسم الضلفة (للسكينة السحاب فقط)
  Widget _buildDalfaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الضلفة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.indigo[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.door_front_door,
                color: Colors.indigo[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'بالضلفة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              Switch(
                value: _withDalfa,
                onChanged: (bool value) {
                  setState(() {
                    _withDalfa = value;
                  });
                },
                activeColor: Colors.indigo[600],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قسم الصورة
  Widget _buildImageSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'صورة القطاع',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: _selectedImageData != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.memory(
                            _selectedImageData!,
                            fit: BoxFit.contain,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'لا توجد صورة',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(width: 16),
              Column(
                children: [
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.add_a_photo),
                    label: const Text('اختيار صورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                  if (_selectedImageData != null) ...[
                    const SizedBox(height: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedImageData = null;
                        });
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('حذف الصورة'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }



  // اختيار صورة
  Future<void> _pickImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true, // لقراءة بيانات الملف
      );

      if (result != null && result.files.single.bytes != null) {
        setState(() {
          _selectedImageData = result.files.single.bytes;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // حفظ القطاع
  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (widget.seriesId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار مجموعة القطاعات أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final profile = AluminumProfile(
        name: _nameController.text.trim(),
        code: _codeController.text.trim(),
        type: widget.profileType,
        category: widget.category,
        seriesId: widget.seriesId,
        thickness: _thicknessController.text.isNotEmpty
            ? double.tryParse(_thicknessController.text)
            : null,
        // حقول الشفة للحلف فقط
        lipType: _shouldShowLipSection() && _selectedLipType != 'بدون شفة'
            ? _selectedLipType
            : null,
        lipThickness: _shouldShowLipSection() && _lipThicknessController.text.isNotEmpty
            ? double.tryParse(_lipThicknessController.text)
            : null,
        // الباكتة للضلفة المفصلي فقط
        withBaketa: widget.category == ProfileCategory.dalfa &&
                    widget.profileType == ProfileType.hinge
            ? _withBaketa
            : null,
        // الضلفة للسكينة السحاب فقط
        withDalfa: widget.category == ProfileCategory.skineh &&
                   widget.profileType == ProfileType.sliding
            ? _withDalfa
            : null,
        imagePath: _selectedImageData != null ? String.fromCharCodes(_selectedImageData!) : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final profileService = UnifiedAluminumService();
      await profileService.insertProfile(profile.toMap());

      if (mounted) {
        Navigator.of(context).pop();
        widget.onProfileAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة ${widget.category.arabicName} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ القطاع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // دوال مساعدة للألوان والأيقونات
  Color _getCategoryColor(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return const Color(0xFF2196F3);
      case ProfileCategory.bar:
        return const Color(0xFF4CAF50);
      case ProfileCategory.dalfa:
        return const Color(0xFF9C27B0);
      case ProfileCategory.marad:
        return const Color(0xFFFF9800);
      case ProfileCategory.baketa:
        return const Color(0xFFF44336);
      case ProfileCategory.soas:
        return const Color(0xFF00BCD4);
      case ProfileCategory.dalfaSilk:
        return const Color(0xFF795548);
      case ProfileCategory.olba:
        return const Color(0xFF607D8B);
      case ProfileCategory.filta:
        return const Color(0xFF9E9E9E);
      case ProfileCategory.skineh:
        return const Color(0xFF3F51B5);
      case ProfileCategory.anf:
        return const Color(0xFFE91E63);
    }
  }

  IconData _getCategoryIcon(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Icons.crop_square;
      case ProfileCategory.bar:
        return Icons.horizontal_rule;
      case ProfileCategory.dalfa:
        return Icons.door_front_door;
      case ProfileCategory.marad:
        return Icons.vertical_split;
      case ProfileCategory.baketa:
        return Icons.linear_scale;
      case ProfileCategory.soas:
        return Icons.horizontal_distribute;
      case ProfileCategory.dalfaSilk:
        return Icons.grid_4x4;
      case ProfileCategory.olba:
        return Icons.inventory_2;
      case ProfileCategory.filta:
        return Icons.circle;
      case ProfileCategory.skineh:
        return Icons.linear_scale;
      case ProfileCategory.anf:
        return Icons.arrow_forward;
    }
  }
}

// نافذة حوار تعديل قطاع
class _EditProfileDialog extends StatefulWidget {
  final AluminumProfile profile;
  final VoidCallback onProfileUpdated;

  const _EditProfileDialog({
    required this.profile,
    required this.onProfileUpdated,
  });

  @override
  State<_EditProfileDialog> createState() => _EditProfileDialogState();
}

class _EditProfileDialogState extends State<_EditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _codeController;
  late final TextEditingController _thicknessController;
  late final TextEditingController _lipThicknessController;

  late String _selectedLipType;
  late bool _withBaketa; // للضلفة المفصلي
  late bool _withDalfa; // للسكينة السحاب
  Uint8List? _selectedImageData;
  bool _isLoading = false;

  final List<String> _lipTypes = ['بدون شفة', 'شفة منه فيه', 'بار خارجى'];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.profile.name);
    _codeController = TextEditingController(text: widget.profile.code);
    _thicknessController = TextEditingController(
      text: widget.profile.thickness?.toString() ?? '',
    );
    _lipThicknessController = TextEditingController(
      text: widget.profile.lipThickness?.toString() ?? '',
    );

    // التأكد من أن القيمة المحددة موجودة في القائمة
    String? profileLipType = widget.profile.lipType;
    if (profileLipType != null && _lipTypes.contains(profileLipType)) {
      _selectedLipType = profileLipType;
    } else {
      _selectedLipType = 'بدون شفة'; // القيمة الافتراضية
    }

    // تهيئة قيمة الباكتة
    _withBaketa = widget.profile.withBaketa ?? false;

    // تهيئة قيمة الضلفة
    _withDalfa = widget.profile.withDalfa ?? false;

    // تحويل imagePath من String إلى Uint8List إذا كانت تحتوي على بيانات
    if (widget.profile.imagePath != null && widget.profile.imagePath!.isNotEmpty) {
      try {
        _selectedImageData = Uint8List.fromList(widget.profile.imagePath!.codeUnits);
      } catch (e) {
        _selectedImageData = null;
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _thicknessController.dispose();
    _lipThicknessController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // عنوان النافذة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getCategoryColor(widget.profile.category),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(widget.profile.category),
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'تعديل قطاع ${widget.profile.category.arabicName}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // محتوى النموذج
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 16),
                      // إخفاء قسم الشفة للقطاعات المحددة
                      if (_shouldShowLipSection()) ...[
                        _buildLipSection(),
                        const SizedBox(height: 16),
                      ],
                      // إضافة قسم الباكتة للضلفة المفصلي فقط
                      if (widget.profile.category == ProfileCategory.dalfa &&
                          widget.profile.type == ProfileType.hinge) ...[
                        _buildBaketaSection(),
                        const SizedBox(height: 16),
                      ],
                      // إضافة قسم الضلفة للسكينة السحاب فقط
                      if (widget.profile.category == ProfileCategory.skineh &&
                          widget.profile.type == ProfileType.sliding) ...[
                        _buildDalfaSection(),
                        const SizedBox(height: 16),
                      ],
                      _buildImageSection(),
                    ],
                  ),
                ),
              ),
            ),

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _updateProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getCategoryColor(widget.profile.category),
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('حفظ التعديلات'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // قسم المعلومات الأساسية
  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الأساسية',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسم القطاع *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.label),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم القطاع';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _codeController,
                  decoration: const InputDecoration(
                    labelText: 'رقم القطاع *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.qr_code),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم القطاع';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 1,
                child: TextFormField(
                  controller: _thicknessController,
                  decoration: const InputDecoration(
                    labelText: 'السمك (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.straighten),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قسم الشفة
  Widget _buildLipSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الشفة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _lipTypes.contains(_selectedLipType) ? _selectedLipType : 'بدون شفة',
                  decoration: const InputDecoration(
                    labelText: 'نوع الشفة',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.layers),
                  ),
                  items: _lipTypes.map((String type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedLipType = newValue;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _lipThicknessController,
                  decoration: const InputDecoration(
                    labelText: 'سمك الشفة (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.straighten),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة لتحديد ما إذا كان يجب عرض قسم الشفة
  bool _shouldShowLipSection() {
    // القطاعات التي تحتاج حقول الشفة (الحلف فقط)
    return widget.profile.category == ProfileCategory.halaf;
  }

  // قسم الباكتة (للضلفة المفصلي فقط)
  Widget _buildBaketaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الباكتة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.check_box,
                color: Colors.purple[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'بالباكتة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              Switch(
                value: _withBaketa,
                onChanged: (bool value) {
                  setState(() {
                    _withBaketa = value;
                  });
                },
                activeColor: Colors.purple[600],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قسم الضلفة (للسكينة السحاب فقط)
  Widget _buildDalfaSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الضلفة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.indigo[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.door_front_door,
                color: Colors.indigo[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'بالضلفة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ),
              Switch(
                value: _withDalfa,
                onChanged: (bool value) {
                  setState(() {
                    _withDalfa = value;
                  });
                },
                activeColor: Colors.indigo[600],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // قسم الصورة
  Widget _buildImageSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'صورة القطاع',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: _selectedImageData != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.memory(
                            _selectedImageData!,
                            fit: BoxFit.contain,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        )
                      : Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'لا توجد صورة',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                ),
              ),
              const SizedBox(width: 16),
              Column(
                children: [
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.add_a_photo),
                    label: const Text('تغيير الصورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                  if (_selectedImageData != null) ...[
                    const SizedBox(height: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedImageData = null;
                        });
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('حذف الصورة'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // اختيار صورة
  Future<void> _pickImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true, // لقراءة بيانات الملف
      );

      if (result != null && result.files.single.bytes != null) {
        setState(() {
          _selectedImageData = result.files.single.bytes;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // تحديث القطاع
  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedProfile = AluminumProfile(
        id: widget.profile.id,
        name: _nameController.text.trim(),
        code: _codeController.text.trim(),
        type: widget.profile.type,
        category: widget.profile.category,
        seriesId: widget.profile.seriesId,
        thickness: _thicknessController.text.isNotEmpty
            ? double.tryParse(_thicknessController.text)
            : null,
        // حقول الشفة للحلف فقط
        lipType: _shouldShowLipSection() && _selectedLipType != 'بدون شفة'
            ? _selectedLipType
            : null,
        lipThickness: _shouldShowLipSection() && _lipThicknessController.text.isNotEmpty
            ? double.tryParse(_lipThicknessController.text)
            : null,
        // الباكتة للضلفة المفصلي فقط
        withBaketa: widget.profile.category == ProfileCategory.dalfa &&
                    widget.profile.type == ProfileType.hinge
            ? _withBaketa
            : null,
        // الضلفة للسكينة السحاب فقط
        withDalfa: widget.profile.category == ProfileCategory.skineh &&
                   widget.profile.type == ProfileType.sliding
            ? _withDalfa
            : null,
        imagePath: _selectedImageData != null ? String.fromCharCodes(_selectedImageData!) : null,
        createdAt: widget.profile.createdAt,
        updatedAt: DateTime.now(),
      );

      final profileService = UnifiedAluminumService();
      await profileService.updateProfile(updatedProfile);

      if (mounted) {
        Navigator.of(context).pop();
        widget.onProfileUpdated();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث ${widget.profile.category.arabicName} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث القطاع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // دوال مساعدة للألوان والأيقونات
  Color _getCategoryColor(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return const Color(0xFF2196F3);
      case ProfileCategory.bar:
        return const Color(0xFF4CAF50);
      case ProfileCategory.dalfa:
        return const Color(0xFF9C27B0);
      case ProfileCategory.marad:
        return const Color(0xFFFF9800);
      case ProfileCategory.baketa:
        return const Color(0xFFF44336);
      case ProfileCategory.soas:
        return const Color(0xFF00BCD4);
      case ProfileCategory.dalfaSilk:
        return const Color(0xFF795548);
      case ProfileCategory.olba:
        return const Color(0xFF607D8B);
      case ProfileCategory.filta:
        return const Color(0xFF9E9E9E);
      case ProfileCategory.skineh:
        return const Color(0xFF3F51B5);
      case ProfileCategory.anf:
        return const Color(0xFFE91E63);
    }
  }

  IconData _getCategoryIcon(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Icons.crop_square;
      case ProfileCategory.bar:
        return Icons.horizontal_rule;
      case ProfileCategory.dalfa:
        return Icons.door_front_door;
      case ProfileCategory.marad:
        return Icons.vertical_split;
      case ProfileCategory.baketa:
        return Icons.linear_scale;
      case ProfileCategory.soas:
        return Icons.horizontal_distribute;
      case ProfileCategory.dalfaSilk:
        return Icons.grid_4x4;
      case ProfileCategory.olba:
        return Icons.inventory_2;
      case ProfileCategory.filta:
        return Icons.circle;
      case ProfileCategory.skineh:
        return Icons.linear_scale;
      case ProfileCategory.anf:
        return Icons.arrow_forward;
    }
  }
}

// ويدجت تخصيمات المفصلي
class _HingeDesignsWidget extends StatefulWidget {
  final AppLocalizations localizations;
  final bool isTablet;

  const _HingeDesignsWidget({
    required this.localizations,
    required this.isTablet,
  });

  @override
  State<_HingeDesignsWidget> createState() => _HingeDesignsWidgetState();
}

class _HingeDesignsWidgetState extends State<_HingeDesignsWidget> {
  final UnifiedAluminumService _designService = UnifiedAluminumService();
  final UnifiedAluminumService _seriesService = UnifiedAluminumService();
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _dalfaOnHalafController = TextEditingController();
  final _maradDalfaCompleteController = TextEditingController();
  final _maradDalfaWithKaabController = TextEditingController();
  final _maradBetweenDalfaController = TextEditingController();
  final _dalfaFromGroundController = TextEditingController();
  final _dalfaGlassController = TextEditingController();
  final _fixedGlassController = TextEditingController();
  final _movingSilkController = TextEditingController();
  final _fixedSilkController = TextEditingController();

  List<ProfileSeries> _series = [];
  ProfileSeries? _selectedSeries;
  HingeDesign? _currentDesign;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);
    try {
      // تحميل المجموعات أولاً
      await _loadSeries();

      // إذا لم توجد مجموعات، أدرج البيانات التجريبية
      if (_series.isEmpty) {
        await _seriesService.insertSampleData();
        await _designService.insertSampleData();
        await _loadSeries();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      try {
        // في حالة وجود خطأ في قاعدة البيانات، أعد إنشائها
        await _designService.recreateDatabase();
        await _seriesService.resetDatabase();
        await _seriesService.insertSampleData();
        await _designService.insertSampleData();
        await _loadSeries();
      } catch (e2) {
        debugPrint('خطأ في إعادة إنشاء قاعدة البيانات: $e2');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحميل البيانات: $e2'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadSeries() async {
    try {
      final seriesData = await _seriesService.getSeriesByType(ProfileType.hinge);
      final series = seriesData.map((data) => ProfileSeries.fromMap(data)).toList();
      setState(() {
        _series = series;
        if (series.isNotEmpty) {
          _selectedSeries = series.first;
          _loadDesignForSeries();
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المجموعات: $e')),
        );
      }
    }
    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _dalfaOnHalafController.dispose();
    _maradDalfaCompleteController.dispose();
    _maradDalfaWithKaabController.dispose();
    _maradBetweenDalfaController.dispose();
    _dalfaFromGroundController.dispose();
    _dalfaGlassController.dispose();
    _fixedGlassController.dispose();
    _movingSilkController.dispose();
    _fixedSilkController.dispose();
    super.dispose();
  }

  Future<void> _loadDesignForSeries() async {
    if (_selectedSeries == null) return;

    try {
      final design = await _designService.getHingeDesignBySeries(_selectedSeries!.id!);
      if (design != null) {
        _currentDesign = design;
        _populateFields(design);
      } else {
        _currentDesign = null;
        _clearFields();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل التخصيمات: $e')),
        );
      }
    }
  }

  void _clearFields() {
    _dalfaOnHalafController.clear();
    _maradDalfaCompleteController.clear();
    _maradDalfaWithKaabController.clear();
    _maradBetweenDalfaController.clear();
    _dalfaFromGroundController.clear();
    _dalfaGlassController.clear();
    _fixedGlassController.clear();
    _movingSilkController.clear();
    _fixedSilkController.clear();
  }

  void _populateFields(HingeDesign design) {
    _dalfaOnHalafController.text = design.dalfaOnHalaf?.toString() ?? '';
    _maradDalfaCompleteController.text = design.maradDalfaComplete?.toString() ?? '';
    _maradDalfaWithKaabController.text = design.maradDalfaWithKaab?.toString() ?? '';
    _maradBetweenDalfaController.text = design.maradBetweenDalfa?.toString() ?? '';
    _dalfaFromGroundController.text = design.dalfaFromGround?.toString() ?? '';
    _dalfaGlassController.text = design.dalfaGlass?.toString() ?? '';
    _fixedGlassController.text = design.fixedGlass?.toString() ?? '';
    _movingSilkController.text = design.movingSilk?.toString() ?? '';
    _fixedSilkController.text = design.fixedSilk?.toString() ?? '';
  }

  Future<void> _saveDesign() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final now = DateTime.now();
      final design = HingeDesign(
        id: _currentDesign?.id,
        seriesId: _selectedSeries?.id,
        dalfaOnHalaf: double.tryParse(_dalfaOnHalafController.text),
        maradDalfaComplete: double.tryParse(_maradDalfaCompleteController.text),
        maradDalfaWithKaab: double.tryParse(_maradDalfaWithKaabController.text),
        maradBetweenDalfa: double.tryParse(_maradBetweenDalfaController.text),
        dalfaFromGround: double.tryParse(_dalfaFromGroundController.text),
        dalfaGlass: double.tryParse(_dalfaGlassController.text),
        fixedGlass: double.tryParse(_fixedGlassController.text),
        movingSilk: double.tryParse(_movingSilkController.text),
        fixedSilk: double.tryParse(_fixedSilkController.text),
        createdAt: _currentDesign?.createdAt ?? now,
        updatedAt: now,
      );

      if (_currentDesign?.id != null) {
        await _designService.updateHingeDesign(design);
      } else {
        await _designService.insertHingeDesign(design.toMap());
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التخصيمات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      await _loadDesignForSeries();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التخصيمات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF9C27B0),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(Icons.door_front_door, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'تخصيمات المفصلي',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: _selectedSeries != null ? _saveDesign : null,
                    icon: const Icon(Icons.save, size: 16),
                    label: const Text('حفظ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF9C27B0),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // اختيار المجموعة
            if (_series.isNotEmpty) ...[
              _buildSeriesSelection(),
              const SizedBox(height: 16),
            ],

            // الحقول
            Expanded(
              child: _selectedSeries == null
                  ? _buildNoSeriesSelected()
                  : SingleChildScrollView(
                      child: _buildHingeFields(),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHingeFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.isTablet
          ? Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _dalfaOnHalafController,
                        label: 'ركوب الضلفة على الحلق',
                        icon: Icons.layers,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _maradDalfaCompleteController,
                        label: 'تخصيم المرد الضلفة بالكامل',
                        icon: Icons.vertical_split,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _maradDalfaWithKaabController,
                        label: 'تخصيم المرد للضلفة بكعب',
                        icon: Icons.vertical_split,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _maradBetweenDalfaController,
                        label: 'تخصيم بين الضلفتين (المرد)',
                        icon: Icons.vertical_split,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _dalfaFromGroundController,
                        label: 'تخصيم الضلفة من الارض فى حالة ابواب بكعب',
                        icon: Icons.height,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _dalfaGlassController,
                        label: 'تخصيم زجاج الضلف',
                        icon: Icons.window,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _fixedGlassController,
                        label: 'تخصيم زجاج الثابت',
                        icon: Icons.window,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _movingSilkController,
                        label: 'تخصيم السلك المتحرك',
                        icon: Icons.grid_4x4,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _fixedSilkController,
                        label: 'تخصيم السلك الثابت',
                        icon: Icons.grid_4x4,
                      ),
                    ),
                    const Expanded(child: SizedBox()), // مساحة فارغة
                  ],
                ),
              ],
            )
          : Column(
              children: [
                _buildTextField(
                  controller: _dalfaOnHalafController,
                  label: 'ركوب الضلفة على الحلق',
                  icon: Icons.layers,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _maradDalfaCompleteController,
                  label: 'تخصيم المرد الضلفة بالكامل',
                  icon: Icons.vertical_split,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _maradDalfaWithKaabController,
                  label: 'تخصيم المرد للضلفة بكعب',
                  icon: Icons.vertical_split,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _maradBetweenDalfaController,
                  label: 'تخصيم بين الضلفتين (المرد)',
                  icon: Icons.vertical_split,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _dalfaFromGroundController,
                  label: 'تخصيم الضلفة من الارض فى حالة ابواب بكعب',
                  icon: Icons.height,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _dalfaGlassController,
                  label: 'تخصيم زجاج الضلف',
                  icon: Icons.window,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _fixedGlassController,
                  label: 'تخصيم زجاج الثابت',
                  icon: Icons.window,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _movingSilkController,
                  label: 'تخصيم السلك المتحرك',
                  icon: Icons.grid_4x4,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _fixedSilkController,
                  label: 'تخصيم السلك الثابت',
                  icon: Icons.grid_4x4,
                ),
              ],
            ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        suffixText: 'سم',
      ),
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildSeriesSelection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF9C27B0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.isTablet
          ? Row(
              children: [
                const Icon(
                  Icons.door_front_door,
                  color: Color(0xFF9C27B0),
                ),
                const SizedBox(width: 12),
                Text(
                  'اختر المجموعة:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<ProfileSeries>(
                      value: _selectedSeries,
                      isExpanded: true,
                      hint: const Text('اختر مجموعة القطاعات'),
                      onChanged: (ProfileSeries? newValue) async {
                        setState(() {
                          _selectedSeries = newValue;
                        });
                        if (newValue != null) {
                          await _loadDesignForSeries();
                        }
                      },
                      items: _series.map<DropdownMenuItem<ProfileSeries>>((ProfileSeries series) {
                        return DropdownMenuItem<ProfileSeries>(
                          value: series,
                          child: Text(
                            '${series.name} (${series.code})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.door_front_door,
                      color: Color(0xFF9C27B0),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'اختر المجموعة:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                DropdownButtonHideUnderline(
                  child: DropdownButton<ProfileSeries>(
                    value: _selectedSeries,
                    isExpanded: true,
                    hint: const Text('اختر مجموعة القطاعات'),
                    onChanged: (ProfileSeries? newValue) async {
                      setState(() {
                        _selectedSeries = newValue;
                      });
                      if (newValue != null) {
                        await _loadDesignForSeries();
                      }
                    },
                    items: _series.map<DropdownMenuItem<ProfileSeries>>((ProfileSeries series) {
                      return DropdownMenuItem<ProfileSeries>(
                        value: series,
                        child: Text(
                          '${series.name} (${series.code})',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildNoSeriesSelected() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF9C27B0).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.door_front_door,
              size: 64,
              color: Color(0xFF9C27B0),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'اختر مجموعة لعرض التخصيمات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// ويدجت تخصيمات السحاب
class _SlidingDesignsWidget extends StatefulWidget {
  final AppLocalizations localizations;
  final bool isTablet;

  const _SlidingDesignsWidget({
    required this.localizations,
    required this.isTablet,
  });

  @override
  State<_SlidingDesignsWidget> createState() => _SlidingDesignsWidgetState();
}

class _SlidingDesignsWidgetState extends State<_SlidingDesignsWidget> {
  final UnifiedAluminumService _designService = UnifiedAluminumService();
  final UnifiedAluminumService _seriesService = UnifiedAluminumService();
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _dalfaWidthPlusController = TextEditingController();
  final _dalfaHeightMinusController = TextEditingController();
  final _skinehHeightMinusController = TextEditingController();
  final _silkWidthPlusController = TextEditingController();
  final _silkHeightMinusController = TextEditingController();
  final _glassWidthMinusController = TextEditingController();
  final _glassHeightMinusController = TextEditingController();

  List<ProfileSeries> _series = [];
  ProfileSeries? _selectedSeries;
  SlidingDalfaCount _selectedDalfaCount = SlidingDalfaCount.two;
  SlidingDalfaMethod _selectedDalfaMethod = SlidingDalfaMethod.method45;
  SlidingDesign? _currentDesign;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);
    try {
      // تحميل المجموعات أولاً
      await _loadSeries();

      // إذا لم توجد مجموعات، أدرج البيانات التجريبية
      if (_series.isEmpty) {
        await _seriesService.insertSampleData();
        await _designService.insertSampleData();
        await _loadSeries();
      }
    } catch (e) {
      try {
        // في حالة وجود خطأ في قاعدة البيانات، أعد إنشائها
        await _designService.recreateDatabase();
        await _seriesService.resetDatabase();
        await _seriesService.insertSampleData();
        await _designService.insertSampleData();
        await _loadSeries();
      } catch (e2) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحميل البيانات: $e2'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadSeries() async {
    try {
      final seriesData = await _seriesService.getSeriesByType(ProfileType.sliding);
      final series = seriesData.map((data) => ProfileSeries.fromMap(data)).toList();
      setState(() {
        _series = series;
        if (series.isNotEmpty) {
          _selectedSeries = series.first;
          _loadDesignForSeries();
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المجموعات: $e')),
        );
      }
    }
    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _dalfaWidthPlusController.dispose();
    _dalfaHeightMinusController.dispose();
    _skinehHeightMinusController.dispose();
    _silkWidthPlusController.dispose();
    _silkHeightMinusController.dispose();
    _glassWidthMinusController.dispose();
    _glassHeightMinusController.dispose();
    super.dispose();
  }

  Future<void> _loadDesignForSeries() async {
    if (_selectedSeries == null) return;

    try {
      final design = await _designService.getSlidingDesignBySeriesAndDalfaCount(
        _selectedSeries!.id!,
        _selectedDalfaCount.index,
      );
      if (design != null) {
        _currentDesign = design;
        _selectedDalfaMethod = design.dalfaMethod;
        _populateFields(design);
      } else {
        _currentDesign = null;
        _clearFields();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل التخصيمات: $e')),
        );
      }
    }
  }



  void _populateFields(SlidingDesign design) {
    _dalfaWidthPlusController.text = design.dalfaWidthPlus?.toString() ?? '';
    _dalfaHeightMinusController.text = design.dalfaHeightMinus?.toString() ?? '';
    _skinehHeightMinusController.text = design.skinehHeightMinus?.toString() ?? '';
    _silkWidthPlusController.text = design.silkWidthPlus?.toString() ?? '';
    _silkHeightMinusController.text = design.silkHeightMinus?.toString() ?? '';
    _glassWidthMinusController.text = design.glassWidthMinus?.toString() ?? '';
    _glassHeightMinusController.text = design.glassHeightMinus?.toString() ?? '';
  }

  void _clearFields() {
    _dalfaWidthPlusController.clear();
    _dalfaHeightMinusController.clear();
    _skinehHeightMinusController.clear();
    _silkWidthPlusController.clear();
    _silkHeightMinusController.clear();
    _glassWidthMinusController.clear();
    _glassHeightMinusController.clear();
  }

  Future<void> _saveDesign() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final now = DateTime.now();
      final design = SlidingDesign(
        id: _currentDesign?.id,
        seriesId: _selectedSeries?.id,
        dalfaCount: _selectedDalfaCount,
        dalfaMethod: _selectedDalfaMethod,
        dalfaWidthPlus: double.tryParse(_dalfaWidthPlusController.text),
        dalfaHeightMinus: double.tryParse(_dalfaHeightMinusController.text),
        skinehHeightMinus: double.tryParse(_skinehHeightMinusController.text),
        silkWidthPlus: double.tryParse(_silkWidthPlusController.text),
        silkHeightMinus: double.tryParse(_silkHeightMinusController.text),
        glassWidthMinus: double.tryParse(_glassWidthMinusController.text),
        glassHeightMinus: double.tryParse(_glassHeightMinusController.text),
        createdAt: _currentDesign?.createdAt ?? now,
        updatedAt: now,
      );

      if (_currentDesign?.id != null) {
        await _designService.updateSlidingDesign(design);
      } else {
        await _designService.insertSlidingDesign(design.toMap());
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التخصيمات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      await _loadDesignForSeries();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التخصيمات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // عنوان القسم
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF00BCD4),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(Icons.door_sliding, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  const Text(
                    'تخصيمات السحاب',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: _selectedSeries != null ? _saveDesign : null,
                    icon: const Icon(Icons.save, size: 16),
                    label: const Text('حفظ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF00BCD4),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // اختيار المجموعة
            if (_series.isNotEmpty) ...[
              _buildSeriesSelectionSliding(),
              const SizedBox(height: 16),
            ],

            // اختيار عدد الضلف وطريقة الضلف
            if (_selectedSeries != null) ...[
              _buildSelectionSection(),
              const SizedBox(height: 16),
            ],

            // الحقول
            Expanded(
              child: _selectedSeries == null
                  ? _buildNoSeriesSelectedSliding()
                  : SingleChildScrollView(
                      child: _buildSlidingFields(),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات الضلف',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<SlidingDalfaCount>(
                  value: _selectedDalfaCount,
                  decoration: const InputDecoration(
                    labelText: 'عدد الضلف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.format_list_numbered),
                  ),
                  items: SlidingDalfaCount.values.map((count) {
                    return DropdownMenuItem<SlidingDalfaCount>(
                      value: count,
                      child: Text(count.arabicName),
                    );
                  }).toList(),
                  onChanged: (SlidingDalfaCount? newValue) async {
                    if (newValue != null) {
                      setState(() {
                        _selectedDalfaCount = newValue;
                      });
                      await _loadDesignForSeries();
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<SlidingDalfaMethod>(
                  value: _selectedDalfaMethod,
                  decoration: const InputDecoration(
                    labelText: 'طريقة الضلف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.rotate_90_degrees_ccw),
                  ),
                  items: SlidingDalfaMethod.values.map((method) {
                    return DropdownMenuItem<SlidingDalfaMethod>(
                      value: method,
                      child: Text(method.name),
                    );
                  }).toList(),
                  onChanged: (SlidingDalfaMethod? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedDalfaMethod = newValue;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSlidingFields() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSlidingTextField(
            controller: _dalfaWidthPlusController,
            label: 'تخصيم الضلف عرض',
            icon: Icons.width_full,
            suffix: '+ سم',
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _dalfaHeightMinusController,
            label: 'تخصيم الضلف ارتفاع',
            icon: Icons.height,
            suffix: '- سم',
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _skinehHeightMinusController,
            label: 'تخصيم السكينة ارتفاع',
            icon: Icons.linear_scale,
            suffix: '- سم',
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _silkWidthPlusController,
            label: 'تخصيم السلك عرض',
            icon: Icons.grid_4x4,
            suffix: '+ سم',
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _silkHeightMinusController,
            label: 'تخصيم السلك ارتفاع',
            icon: Icons.grid_4x4,
            suffix: '- سم',
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _glassWidthMinusController,
            label: 'تخصيم الزجاج عرض',
            icon: Icons.window,
            suffix: '- سم',
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          _buildSlidingTextField(
            controller: _glassHeightMinusController,
            label: 'تخصيم الزجاج ارتفاع',
            icon: Icons.window,
            suffix: '- سم',
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildSlidingTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String suffix,
    required Color color,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        suffixText: suffix,
        suffixStyle: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildSeriesSelectionSliding() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF00BCD4)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.door_sliding,
            color: Color(0xFF00BCD4),
          ),
          const SizedBox(width: 12),
          Text(
            'اختر المجموعة:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<ProfileSeries>(
                value: _selectedSeries,
                isExpanded: true,
                hint: const Text('اختر مجموعة القطاعات'),
                onChanged: (ProfileSeries? newValue) async {
                  setState(() {
                    _selectedSeries = newValue;
                  });
                  if (newValue != null) {
                    await _loadDesignForSeries();
                  }
                },
                items: _series.map<DropdownMenuItem<ProfileSeries>>((ProfileSeries series) {
                  return DropdownMenuItem<ProfileSeries>(
                    value: series,
                    child: Text(
                      '${series.name} (${series.code})',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSeriesSelectedSliding() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFF00BCD4).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.door_sliding,
              size: 64,
              color: Color(0xFF00BCD4),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'اختر مجموعة لعرض التخصيمات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// نافذة حوار إضافة/تعديل مجموعة قطاعات
class _AddEditSeriesDialog extends StatefulWidget {
  final ProfileType profileType;
  final ProfileSeries? series;

  const _AddEditSeriesDialog({
    required this.profileType,
    this.series,
  });

  @override
  State<_AddEditSeriesDialog> createState() => _AddEditSeriesDialogState();
}

class _AddEditSeriesDialogState extends State<_AddEditSeriesDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final UnifiedAluminumService _seriesService = UnifiedAluminumService();

  @override
  void initState() {
    super.initState();
    if (widget.series != null) {
      _nameController.text = widget.series!.name;
      _codeController.text = widget.series!.code;
      _descriptionController.text = widget.series!.description;
    } else {
      _generateCode();
    }
  }

  Future<void> _generateCode() async {
    try {
      final code = await _seriesService.generateSeriesCode(widget.profileType);
      setState(() {
        _codeController.text = code;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveSeries() {
    if (!_formKey.currentState!.validate()) return;

    final now = DateTime.now();
    final series = ProfileSeries(
      id: widget.series?.id,
      name: _nameController.text.trim(),
      code: _codeController.text.trim(),
      type: widget.profileType,
      description: _descriptionController.text.trim(),
      createdAt: widget.series?.createdAt ?? now,
      updatedAt: now,
    );

    Navigator.pop(context, series);
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.series != null;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // عنوان النافذة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.profileType == ProfileType.hinge
                    ? const Color(0xFF607D8B)
                    : const Color(0xFF00BCD4),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.profileType == ProfileType.hinge
                        ? Icons.door_front_door
                        : Icons.door_sliding,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    isEdit ? 'تعديل المجموعة' : 'إضافة مجموعة جديدة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // محتوى النموذج
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // اسم المجموعة
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المجموعة *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.label),
                          hintText: 'مثال: سوناتا 45',
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال اسم المجموعة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // كود المجموعة
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _codeController,
                              decoration: const InputDecoration(
                                labelText: 'كود المجموعة *',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.qr_code),
                                hintText: 'مثال: SON45',
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال كود المجموعة';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: _generateCode,
                            icon: const Icon(Icons.refresh),
                            tooltip: 'توليد كود جديد',
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // نوع المجموعة (للعرض فقط)
                      TextFormField(
                        initialValue: widget.profileType == ProfileType.hinge
                            ? 'مفصلي'
                            : 'سحاب',
                        decoration: const InputDecoration(
                          labelText: 'نوع المجموعة',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.category),
                        ),
                        enabled: false,
                      ),
                      const SizedBox(height: 16),

                      // الوصف
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.description),
                          hintText: 'وصف مختصر للمجموعة',
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveSeries,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.profileType == ProfileType.hinge
                          ? const Color(0xFF607D8B)
                          : const Color(0xFF00BCD4),
                      foregroundColor: Colors.white,
                    ),
                    child: Text(isEdit ? 'تحديث' : 'حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}