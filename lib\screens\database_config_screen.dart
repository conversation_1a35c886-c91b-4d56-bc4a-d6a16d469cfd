// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/mysql_service.dart';
import 'mysql_test_screen_simple.dart';
// import 'aluminum_sync_test_screen.dart'; // تم حذفه

class DatabaseConfigScreen extends StatefulWidget {
  const DatabaseConfigScreen({super.key});

  @override
  State<DatabaseConfigScreen> createState() => _DatabaseConfigScreenState();
}

class _DatabaseConfigScreenState extends State<DatabaseConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  final _hostController = TextEditingController();
  final _portController = TextEditingController(text: '3306');
  final _databaseController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isConnecting = false;
  bool _isConnected = false;
  bool _showPassword = false;
  String _connectionStatus = '';
  Map<String, dynamic>? _databaseInfo;

  @override
  void initState() {
    super.initState();
    _checkConnectionStatus();
  }

  @override
  void dispose() {
    _hostController.dispose();
    _portController.dispose();
    _databaseController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _checkConnectionStatus() async {
    final mysql = MySQLService.instance;
    setState(() {
      _isConnected = mysql.isConnected;
      _connectionStatus = _isConnected ? 'متصل' : 'غير متصل';
    });
    
    if (_isConnected) {
      try {
        _databaseInfo = await mysql.getDatabaseInfo();
        setState(() {});
      } catch (e) {
        debugPrint('خطأ في الحصول على معلومات قاعدة البيانات: $e');
      }
    }
  }
  Future<void> _testConnection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isConnecting = true;
      _connectionStatus = 'جاري اختبار الإعدادات...';
    });

    final mysql = MySQLService.instance;
    
    try {
      // أولاً: اختبار الإعدادات الجديدة دون تأثير على الاتصال الحالي
      final testSuccess = await mysql.testNewSettings(
        host: _hostController.text.trim(),
        port: int.tryParse(_portController.text) ?? 3306,
        database: _databaseController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
      );
      
      if (testSuccess) {
        setState(() {
          _connectionStatus = 'نجح الاختبار، جاري تطبيق الإعدادات...';
        });
        
        // ثانياً: تطبيق الإعدادات الجديدة
        mysql.setCustomSettings(
          host: _hostController.text.trim(),
          port: int.tryParse(_portController.text) ?? 3306,
          database: _databaseController.text.trim(),
          username: _usernameController.text.trim(),
          password: _passwordController.text,
        );

        // ثالثاً: الاتصال بالإعدادات الجديدة
        final connectSuccess = await mysql.testConnection(useNewSettings: true);
        
        setState(() {
          _isConnected = connectSuccess;
          _connectionStatus = connectSuccess ? 'تم الاتصال بنجاح' : 'فشل الاتصال النهائي';
          _isConnecting = false;
        });

        if (connectSuccess) {
          _databaseInfo = await mysql.getDatabaseInfo();
          setState(() {});
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم الاتصال بقاعدة البيانات بنجاح!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل الاتصال النهائي بقاعدة البيانات'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        setState(() {
          _isConnected = false;
          _connectionStatus = 'فشل اختبار الإعدادات';
          _isConnecting = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل اختبار إعدادات قاعدة البيانات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isConnected = false;
        _connectionStatus = 'خطأ: $e';
        _isConnecting = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاتصال: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _createTables() async {
    if (!_isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب الاتصال بقاعدة البيانات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isConnecting = true;
    });

    try {
      final mysql = MySQLService.instance;
      final success = await mysql.createTables();
      
      setState(() {
        _isConnecting = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الجداول بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // تحديث معلومات قاعدة البيانات
        _databaseInfo = await mysql.getDatabaseInfo();
        setState(() {});
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إنشاء الجداول'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isConnecting = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء الجداول: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _disconnect() async {
    await MySQLService.instance.disconnect();
    await _checkConnectionStatus();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم قطع الاتصال بقاعدة البيانات'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات قاعدة البيانات MySQL'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // بطاقة حالة الاتصال
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _isConnected ? Icons.check_circle : Icons.error,
                            color: _isConnected ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'حالة الاتصال: $_connectionStatus',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _isConnected ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                      if (_databaseInfo != null) ...[
                        const SizedBox(height: 12),
                        const Divider(),
                        const SizedBox(height: 8),
                        Text('معلومات قاعدة البيانات:', style: Theme.of(context).textTheme.titleMedium),
                        const SizedBox(height: 8),
                        _buildInfoRow('إصدار MySQL', _databaseInfo!['version']?.toString() ?? 'غير معروف'),
                        _buildInfoRow('اسم قاعدة البيانات', _databaseInfo!['database_name']?.toString() ?? 'غير معروف'),
                        _buildInfoRow('عنوان الخادم', _databaseInfo!['host']?.toString() ?? 'غير معروف'),
                        _buildInfoRow('المنفذ', _databaseInfo!['port']?.toString() ?? 'غير معروف'),
                        _buildInfoRow('عدد الجداول', _databaseInfo!['tables_count']?.toString() ?? '0'),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // نموذج إعدادات الاتصال
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إعدادات الاتصال',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _hostController,
                        decoration: const InputDecoration(
                          labelText: 'عنوان الخادم',
                          hintText: 'example.com أو IP address',
                          prefixIcon: Icon(Icons.dns),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال عنوان الخادم';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _portController,
                        decoration: const InputDecoration(
                          labelText: 'المنفذ',
                          hintText: '3306',
                          prefixIcon: Icon(Icons.router),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال رقم المنفذ';
                          }
                          final port = int.tryParse(value);
                          if (port == null || port < 1 || port > 65535) {
                            return 'رقم المنفذ غير صحيح';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _databaseController,
                        decoration: const InputDecoration(
                          labelText: 'اسم قاعدة البيانات',
                          hintText: 'my_database',
                          prefixIcon: Icon(Icons.storage),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم قاعدة البيانات';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _usernameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المستخدم',
                          hintText: 'username',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم المستخدم';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          labelText: 'كلمة المرور',
                          hintText: '••••••••',
                          prefixIcon: const Icon(Icons.lock),
                          suffixIcon: IconButton(
                            icon: Icon(_showPassword ? Icons.visibility_off : Icons.visibility),
                            onPressed: () {
                              setState(() {
                                _showPassword = !_showPassword;
                              });
                            },
                          ),
                          border: const OutlineInputBorder(),
                        ),
                        obscureText: !_showPassword,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال كلمة المرور';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // أزرار التحكم
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _isConnecting ? null : _testConnection,
                      icon: _isConnecting
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.wifi_find),
                      label: Text(_isConnecting ? 'جاري الاختبار...' : 'اختبار الاتصال'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  if (_isConnected) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isConnecting ? null : _disconnect,
                        icon: const Icon(Icons.wifi_off),
                        label: const Text('قطع الاتصال'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              
              if (_isConnected) ...[
                const SizedBox(height: 12),
                ElevatedButton.icon(
                  onPressed: _isConnecting ? null : _createTables,
                  icon: _isConnecting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.table_chart),
                  label: const Text('إنشاء الجداول'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
              
              const SizedBox(height: 24),
              
              // زر اختبار المزامنة
              if (_isConnected)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const MySQLTestScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.sync_alt),
                    label: const Text('اختبار المزامنة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),

              const SizedBox(height: 16),

              // زر اختبار مزامنة الألومنيوم
              if (_isConnected)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          // builder: (context) => const AluminumSyncTestScreen(), // تم حذفه
                        builder: (context) => const MySQLTestScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.widgets),
                    label: const Text('اختبار مزامنة الألومنيوم'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              
              // معلومات إضافية
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'ملاحظات مهمة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text('• تأكد من أن خادم MySQL يقبل الاتصالات من الخارج'),
                      const Text('• تحقق من إعدادات جدار الحماية للمنفذ 3306'),
                      const Text('• استخدم اتصال SSL في بيئة الإنتاج'),
                      const Text('• احفظ بيانات الاتصال في مكان آمن'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
