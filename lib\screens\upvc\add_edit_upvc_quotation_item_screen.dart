import 'package:flutter/material.dart';
import '../../models/upvc_quotation.dart';
import '../../models/upvc_quotation_item.dart';
import '../../services/upvc_quotation_service.dart';

class AddEditUpvcQuotationItemScreen extends StatefulWidget {
  final UpvcQuotation quotation;
  final UpvcQuotationItem? item;

  const AddEditUpvcQuotationItemScreen({
    super.key,
    required this.quotation,
    this.item,
  });

  @override
  State<AddEditUpvcQuotationItemScreen> createState() => _AddEditUpvcQuotationItemScreenState();
}

class _AddEditUpvcQuotationItemScreenState extends State<AddEditUpvcQuotationItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _quotationService = UnifiedUpvcService();

  // Controllers
  final TextEditingController _widthController = TextEditingController();
  final TextEditingController _heightController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // State variables
  WindowDoorType _selectedType = WindowDoorType.hinge; // افتراضي مفصلي
  SashCount _selectedSashCount = SashCount.one; // افتراضي ضلفة واحدة
  TrackCount? _selectedTrackCount; // للسحاب فقط
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _widthController.dispose();
    _heightController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeData() {
    if (widget.item != null) {
      final item = widget.item!;
      _selectedType = item.type;
      _selectedSashCount = item.sashCount;
      _selectedTrackCount = item.trackCount;
      _widthController.text = item.width.toString();
      _heightController.text = item.height.toString();
      _quantityController.text = item.quantity.toString();
      _notesController.text = item.notes;
    } else {
      _quantityController.text = '1';
      // تعيين القيم الافتراضية حسب النوع
      _updateDefaultValues();
    }
  }

  void _updateDefaultValues() {
    if (_selectedType == WindowDoorType.hinge) {
      _selectedSashCount = SashCount.one;
      _selectedTrackCount = null;
    } else {
      _selectedSashCount = SashCount.two;
      _selectedTrackCount = TrackCount.one;
    }
  }

  void _onTypeChanged(WindowDoorType? newType) {
    if (newType != null && newType != _selectedType) {
      setState(() {
        _selectedType = newType;
        _updateDefaultValues();
      });
    }
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final width = double.tryParse(_widthController.text);
    final height = double.tryParse(_heightController.text);
    final quantity = int.tryParse(_quantityController.text);

    if (width == null || height == null || quantity == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى التأكد من صحة البيانات المدخلة')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();

      final newItem = UpvcQuotationItem(
        id: widget.item?.id,
        quotationId: widget.quotation.id!,
        type: _selectedType,
        sashCount: _selectedSashCount,
        trackCount: _selectedTrackCount,
        width: width,
        height: height,
        quantity: quantity,
        notes: _notesController.text.trim(),
        createdAt: widget.item?.createdAt ?? now,
      );

      if (widget.item == null) {
        await _quotationService.insertQuotationItem(newItem);
      } else {
        await _quotationService.updateQuotationItem(newItem);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.item == null ? 'تم إضافة البند بنجاح' : 'تم تحديث البند بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البند: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.item == null ? 'إضافة بند جديد' : 'تعديل البند'),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildBasicInfoCard(),
              const SizedBox(height: 24),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          const Text(
            'معلومات البند الأساسية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF607D8B),
            ),
          ),
          const SizedBox(height: 16),

          // نوع الفتحة (مفصلي/سحاب)
          Row(
            children: [
              Expanded(
                flex: 2,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(12),
                        child: Text(
                          'نوع الفتحة',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<WindowDoorType>(
                              title: const Text('مفصلي'),
                              value: WindowDoorType.hinge,
                              groupValue: _selectedType,
                              onChanged: _onTypeChanged,
                              dense: true,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<WindowDoorType>(
                              title: const Text('سحاب'),
                              value: WindowDoorType.sliding,
                              groupValue: _selectedType,
                              onChanged: _onTypeChanged,
                              dense: true,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              
              // عدد الضلف
              Expanded(
                flex: 1,
                child: DropdownButtonFormField<SashCount>(
                  value: _selectedSashCount,
                  decoration: const InputDecoration(
                    labelText: 'عدد الضلف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.view_column),
                  ),
                  items: (_selectedType == WindowDoorType.hinge 
                      ? SashCount.getHingeOptions() 
                      : SashCount.getSlidingOptions()).map((count) {
                    return DropdownMenuItem(
                      value: count,
                      child: Text(count.arabicName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSashCount = value;
                        // إعادة تعيين عدد السكك إذا لزم الأمر
                        if (_selectedType == WindowDoorType.sliding && value.count <= 2) {
                          _selectedTrackCount = TrackCount.one;
                        }
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          
          // عدد السكك (للسحاب فقط عند أكثر من ضلفتين)
          if (_selectedType == WindowDoorType.sliding && _selectedSashCount.count > 2) ...[
            const SizedBox(height: 16),
            DropdownButtonFormField<TrackCount>(
              value: _selectedTrackCount,
              decoration: const InputDecoration(
                labelText: 'الحلق كام سكة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.linear_scale),
              ),
              items: TrackCount.values.map((track) {
                return DropdownMenuItem(
                  value: track,
                  child: Text(track.arabicName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTrackCount = value;
                });
              },
            ),
          ],
          const SizedBox(height: 16),

          // الأبعاد والكمية
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _widthController,
                  decoration: const InputDecoration(
                    labelText: 'العرض (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.straighten),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'العرض مطلوب';
                    }
                    final width = double.tryParse(value);
                    if (width == null || width <= 0) {
                      return 'العرض يجب أن يكون رقم موجب';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _heightController,
                  decoration: const InputDecoration(
                    labelText: 'الارتفاع (سم)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.height),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الارتفاع مطلوب';
                    }
                    final height = double.tryParse(value);
                    if (height == null || height <= 0) {
                      return 'الارتفاع يجب أن يكون رقم موجب';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.numbers),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الكمية مطلوبة';
                    }
                    final quantity = int.tryParse(value);
                    if (quantity == null || quantity <= 0) {
                      return 'الكمية يجب أن تكون رقم موجب';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // ملاحظات
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'ملاحظات',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 3,
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveItem,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF607D8B),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                widget.item == null ? 'إضافة البند' : 'تحديث البند',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}
