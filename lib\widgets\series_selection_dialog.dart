import 'package:flutter/material.dart';
import '../models/profile_series.dart';
import '../models/aluminum_profile.dart';

/// حوار اختيار مجموعات القطاعات للاستيراد
class SeriesSelectionDialog extends StatefulWidget {
  final List<ProfileSeries> availableSeries;
  final ProfileType profileType;

  const SeriesSelectionDialog({
    super.key,
    required this.availableSeries,
    required this.profileType,
  });

  @override
  State<SeriesSelectionDialog> createState() => _SeriesSelectionDialogState();
}

class _SeriesSelectionDialogState extends State<SeriesSelectionDialog> {
  final Set<ProfileSeries> _selectedSeries = {};
  bool _selectAll = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  widget.profileType == ProfileType.hinge
                      ? Icons.door_front_door
                      : Icons.door_sliding,
                  color: const Color(0xFF607D8B),
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'اختيار مجموعات ${widget.profileType == ProfileType.hinge ? 'المفصلي' : 'السحاب'}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'اختر المجموعات التي تريد استيرادها من قاعدة البيانات الأونلاين',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // معلومات إحصائية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF607D8B).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF607D8B).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: const Color(0xFF607D8B),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تم العثور على ${widget.availableSeries.length} مجموعة متاحة للاستيراد',
                      style: TextStyle(
                        fontSize: 14,
                        color: const Color(0xFF607D8B),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    'المحدد: ${_selectedSeries.length}',
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xFF607D8B),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // خيار تحديد الكل
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectAll,
                    onChanged: (value) {
                      setState(() {
                        _selectAll = value ?? false;
                        if (_selectAll) {
                          _selectedSeries.addAll(widget.availableSeries);
                        } else {
                          _selectedSeries.clear();
                        }
                      });
                    },
                    activeColor: const Color(0xFF607D8B),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'تحديد جميع المجموعات',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // قائمة المجموعات
            Expanded(
              child: widget.availableSeries.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      itemCount: widget.availableSeries.length,
                      itemBuilder: (context, index) {
                        final series = widget.availableSeries[index];
                        final isSelected = _selectedSeries.contains(series);
                        
                        return _buildSeriesCard(series, isSelected);
                      },
                    ),
            ),

            const SizedBox(height: 16),

            // أزرار التحكم
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton.icon(
                  onPressed: _selectedSeries.isEmpty
                      ? null
                      : () => Navigator.of(context).pop(_selectedSeries.toList()),
                  icon: const Icon(Icons.download, size: 18),
                  label: Text('استيراد (${_selectedSeries.length})'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF607D8B),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeriesCard(ProfileSeries series, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color(0xFF607D8B).withValues(alpha: 0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? const Color(0xFF607D8B)
              : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (value) {
          setState(() {
            if (value == true) {
              _selectedSeries.add(series);
            } else {
              _selectedSeries.remove(series);
            }
            
            // تحديث حالة تحديد الكل
            _selectAll = _selectedSeries.length == widget.availableSeries.length;
          });
        },
        activeColor: const Color(0xFF607D8B),
        title: Text(
          series.name,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isSelected ? const Color(0xFF607D8B) : Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF607D8B).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    series.code,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF607D8B),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  widget.profileType == ProfileType.hinge
                      ? Icons.door_front_door
                      : Icons.door_sliding,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  widget.profileType == ProfileType.hinge ? 'مفصلي' : 'سحاب',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (series.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                series.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        secondary: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF607D8B)
                : Colors.grey[100],
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.folder,
            color: isSelected ? Colors.white : Colors.grey[600],
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.folder_off,
              size: 64,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مجموعات متاحة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم العثور على مجموعات ${widget.profileType == ProfileType.hinge ? 'مفصلي' : 'سحاب'} في قاعدة البيانات الأونلاين',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
