class CuttingMeasurement {
  final int? id;
  final int orderItemId;
  final String pieceSize;
  final int quantity;
  final String type;
  final String number;
  final double stickLength;
  final double sawBladeThickness;
  final DateTime createdAt;

  CuttingMeasurement({
    this.id,
    required this.orderItemId,
    required this.pieceSize,
    required this.quantity,
    required this.type,
    required this.number,
    this.stickLength = 300.0,
    this.sawBladeThickness = 0.5,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_item_id': orderItemId,
      'piece_size': pieceSize,
      'quantity': quantity,
      'type': type,
      'number': number,
      'stick_length': stickLength,
      'saw_blade_thickness': sawBladeThickness,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory CuttingMeasurement.fromMap(Map<String, dynamic> map) {
    return CuttingMeasurement(
      id: map['id']?.toInt(),
      orderItemId: map['order_item_id']?.toInt() ?? 0,
      pieceSize: map['piece_size'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      type: map['type'] ?? '',
      number: map['number'] ?? '',
      stickLength: map['stick_length']?.toDouble() ?? 300.0,
      sawBladeThickness: map['saw_blade_thickness']?.toDouble() ?? 0.5,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  CuttingMeasurement copyWith({
    int? id,
    int? orderItemId,
    String? pieceSize,
    int? quantity,
    String? type,
    String? number,
    double? stickLength,
    double? sawBladeThickness,
    DateTime? createdAt,
  }) {
    return CuttingMeasurement(
      id: id ?? this.id,
      orderItemId: orderItemId ?? this.orderItemId,
      pieceSize: pieceSize ?? this.pieceSize,
      quantity: quantity ?? this.quantity,
      type: type ?? this.type,
      number: number ?? this.number,
      stickLength: stickLength ?? this.stickLength,
      sawBladeThickness: sawBladeThickness ?? this.sawBladeThickness,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CuttingMeasurement{id: $id, orderItemId: $orderItemId, pieceSize: $pieceSize, quantity: $quantity, type: $type, number: $number, stickLength: $stickLength, sawBladeThickness: $sawBladeThickness, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CuttingMeasurement &&
        other.id == id &&
        other.orderItemId == orderItemId &&
        other.pieceSize == pieceSize &&
        other.quantity == quantity &&
        other.type == type &&
        other.number == number &&
        other.stickLength == stickLength &&
        other.sawBladeThickness == sawBladeThickness &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderItemId.hashCode ^
        pieceSize.hashCode ^
        quantity.hashCode ^
        type.hashCode ^
        number.hashCode ^
        stickLength.hashCode ^
        sawBladeThickness.hashCode ^
        createdAt.hashCode;
  }
}
