import 'package:flutter/foundation.dart';
import 'package:mysql1/mysql1.dart';
import 'dart:async';

class MySQLService {
  static MySQLService? _instance;
  static MySQLService get instance => _instance ??= MySQLService._();
  
  MySQLService._();
  
  MySqlConnection? _connection;
  bool _isConnected = false;
  
  // إعدادات قاعدة البيانات
  static const String _host = '************';
  static const int _port = 3306;
  static const String _database = 'hugowjjm_smart_assist';
  static const String _username = 'hugowjjm_sm';
  static const String _password = 'rS}5bEt.[sRN';
  
  // إعدادات قاعدة البيانات القابلة للتخصيص
  String? customHost;
  int? customPort;
  String? customDatabase;
  String? customUsername;
  String? customPassword;
  
  /// تحديد إعدادات قاعدة البيانات المخصصة
  void setCustomSettings({
    required String host,
    required int port,
    required String database,
    required String username,
    required String password,
  }) {
    customHost = host;
    customPort = port;
    customDatabase = database;
    customUsername = username;
    customPassword = password;
  }
    /// الاتصال بقاعدة البيانات
  Future<bool> connect({bool forceReconnect = false}) async {
    try {
      debugPrint('🔗 محاولة الاتصال بقاعدة البيانات...');
      debugPrint('🌐 Host: ${customHost ?? _host}');
      debugPrint('🔌 Port: ${customPort ?? _port}');
      debugPrint('🗄️ Database: ${customDatabase ?? _database}');
      debugPrint('👤 Username: ${customUsername ?? _username}');
      
      // إذا كان مطلوب إعادة اتصال قسري، قطع الاتصال الحالي أولاً
      if (forceReconnect && _connection != null) {
        debugPrint('🔄 إعادة اتصال قسري - قطع الاتصال الحالي...');
        await disconnect();
      }
      
      // إذا كان هناك اتصال موجود ولم يطلب إعادة اتصال قسري
      if (_isConnected && _connection != null && !forceReconnect) {
        return true;
      }
      
      final settings = ConnectionSettings(
        host: customHost ?? _host,
        port: customPort ?? _port,
        user: customUsername ?? _username,
        password: customPassword ?? _password,
        db: customDatabase ?? _database,
        timeout: const Duration(seconds: 10),
      );
      
      _connection = await MySqlConnection.connect(settings);
      _isConnected = true;
      
      debugPrint('تم الاتصال بقاعدة البيانات MySQL بنجاح');
      return true;
      
    } catch (e) {
      debugPrint('خطأ في الاتصال بقاعدة البيانات: $e');
      _isConnected = false;
      _connection = null;
      return false;
    }
  }
  
  /// قطع الاتصال بقاعدة البيانات
  Future<void> disconnect() async {
    try {
      if (_connection != null) {
        await _connection!.close();
        _connection = null;
        _isConnected = false;
        debugPrint('تم قطع الاتصال بقاعدة البيانات');
      }
    } catch (e) {
      debugPrint('خطأ في قطع الاتصال: $e');
    }
  }
  
  /// التحقق من حالة الاتصال
  bool get isConnected => _isConnected && _connection != null;
  
  /// تنفيذ استعلام SELECT
  Future<List<Map<String, dynamic>>> select(String query, [List<Object?>? params]) async {
    try {
      if (!await _ensureConnection()) {
        throw Exception('لا يمكن الاتصال بقاعدة البيانات');
      }
      
      Results results;
      if (params != null && params.isNotEmpty) {
        results = await _connection!.query(query, params);
      } else {
        results = await _connection!.query(query);
      }
      
      List<Map<String, dynamic>> resultList = [];
      for (var row in results) {
        Map<String, dynamic> rowMap = {};
        for (int i = 0; i < row.length; i++) {
          String? fieldName = results.fields[i].name;
          dynamic value = row[i];

          // تحويل Blob إلى String إذا لزم الأمر
          if (value is List<int>) {
            try {
              value = String.fromCharCodes(value);
            } catch (e) {
              // إذا فشل التحويل، احتفظ بالقيمة الأصلية
            }
          }

          rowMap[fieldName ?? 'field_$i'] = value;
        }
        resultList.add(rowMap);
      }
      
      return resultList;
    } catch (e) {
      debugPrint('خطأ في تنفيذ استعلام SELECT: $e');
      rethrow;
    }
  }
  
  /// تنفيذ استعلام INSERT/UPDATE/DELETE
  Future<int> execute(String query, [List<Object?>? params]) async {
    try {
      if (!await _ensureConnection()) {
        throw Exception('لا يمكن الاتصال بقاعدة البيانات');
      }
      
      Results results;
      if (params != null && params.isNotEmpty) {
        results = await _connection!.query(query, params);
      } else {
        results = await _connection!.query(query);
      }
      
      return results.affectedRows ?? 0;
    } catch (e) {
      debugPrint('خطأ في تنفيذ الاستعلام: $e');
      rethrow;
    }
  }
  
  /// تنفيذ معاملة (Transaction)
  Future<T> transaction<T>(Future<T> Function(MySqlConnection connection) action) async {
    try {
      if (!await _ensureConnection()) {
        throw Exception('لا يمكن الاتصال بقاعدة البيانات');
      }

      await _connection!.query('START TRANSACTION');

      try {
        T result = await action(_connection!);
        await _connection!.query('COMMIT');
        return result;
      } catch (e) {
        await _connection!.query('ROLLBACK');
        rethrow;
      }
    } catch (e) {
      debugPrint('خطأ في تنفيذ المعاملة: $e');
      rethrow;
    }
  }

  /// تنفيذ استعلام عام (للتوافق مع الكود الموجود)
  Future<List<Map<String, dynamic>>> query(String query, [List<Object?>? params]) async {
    return await select(query, params);
  }

  /// إدراج أو تحديث سجل
  Future<int> insertOrUpdate(String tableName, Map<String, dynamic> data, String uniqueKey) async {
    try {
      if (!await _ensureConnection()) {
        throw Exception('لا يمكن الاتصال بقاعدة البيانات');
      }

      // إنشاء استعلام INSERT ... ON DUPLICATE KEY UPDATE
      final columns = data.keys.toList();
      final placeholders = List.filled(columns.length, '?').join(', ');
      final updateClause = columns.map((col) => '$col = VALUES($col)').join(', ');

      final query = '''
        INSERT INTO $tableName (${columns.join(', ')})
        VALUES ($placeholders)
        ON DUPLICATE KEY UPDATE $updateClause
      ''';

      final values = data.values.toList();
      return await execute(query, values);
    } catch (e) {
      debugPrint('خطأ في insertOrUpdate: $e');
      rethrow;
    }
  }
    /// التأكد من وجود اتصال صالح
  Future<bool> _ensureConnection() async {
    if (!_isConnected || _connection == null) {
      return await connect();
    }
    
    try {
      // اختبار الاتصال الحالي
      await _connection!.query('SELECT 1');
      return true;
    } catch (e) {
      debugPrint('الاتصال منقطع، محاولة إعادة الاتصال...');
      _isConnected = false;
      _connection = null;
      return await connect();
    }
  }
  
  /// إنشاء الجداول الأساسية
  Future<bool> createTables() async {
    try {
      if (!await _ensureConnection()) {
        return false;
      }
      
      // جدول العملاء
      await execute('''
        CREATE TABLE IF NOT EXISTS customers (
          id INT PRIMARY KEY AUTO_INCREMENT,
          name VARCHAR(255) NOT NULL,
          phone VARCHAR(50),
          email VARCHAR(255),
          address TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      ''');
      
      // جدول الفواتير
      await execute('''
        CREATE TABLE IF NOT EXISTS invoices (
          id INT PRIMARY KEY AUTO_INCREMENT,
          customer_id INT,
          invoice_number VARCHAR(100) UNIQUE NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL,
          tax_amount DECIMAL(10,2) DEFAULT 0,
          discount_amount DECIMAL(10,2) DEFAULT 0,
          status ENUM('draft', 'sent', 'paid', 'cancelled') DEFAULT 'draft',
          issue_date DATE NOT NULL,
          due_date DATE,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
        )
      ''');
      
      // جدول عناصر الفاتورة
      await execute('''
        CREATE TABLE IF NOT EXISTS invoice_items (
          id INT PRIMARY KEY AUTO_INCREMENT,
          invoice_id INT NOT NULL,
          description TEXT NOT NULL,
          quantity DECIMAL(10,3) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        )
      ''');
      
      // جدول المدفوعات
      await execute('''
        CREATE TABLE IF NOT EXISTS payments (
          id INT PRIMARY KEY AUTO_INCREMENT,
          invoice_id INT,
          amount DECIMAL(10,2) NOT NULL,
          payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'cheque') NOT NULL,
          payment_date DATE NOT NULL,
          reference_number VARCHAR(100),
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE SET NULL
        )
      ''');
      
      // إنشاء جداول الألومنيوم
      await _createAluminumTables();

      // إنشاء جداول التقطيع والمشاريع
      await _createCuttingTables();

      // إنشاء جداول الخزينة
      await _createTreasuryTables();

      // إنشاء جداول إدارة المهام
      await _createTaskTables();

      // إنشاء جداول الموردين
      await _createSupplierTables();

      // إنشاء جداول مقايسات UPVC
      await _createUpvcTables();

      debugPrint('تم إنشاء جميع الجداول بنجاح (شامل جميع الوحدات)');
      return true;

    } catch (e) {
      debugPrint('خطأ في إنشاء الجداول: $e');
      return false;
    }
  }

  /// إنشاء جداول الألومنيوم
  Future<void> _createAluminumTables() async {
    try {
      // جدول مجموعات القطاعات
      await execute('''
        CREATE TABLE IF NOT EXISTS profile_series (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          code VARCHAR(100) NOT NULL UNIQUE,
          type VARCHAR(50) NOT NULL,
          description TEXT,
          image_path VARCHAR(500),
          is_active TINYINT(1) DEFAULT 1,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          INDEX idx_series_type (type),
          INDEX idx_series_code (code),
          INDEX idx_series_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول القطاعات
      await execute('''
        CREATE TABLE IF NOT EXISTS aluminum_profiles (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          code VARCHAR(100) NOT NULL UNIQUE,
          type VARCHAR(50) NOT NULL,
          category VARCHAR(50) NOT NULL,
          series_id INT,
          width DECIMAL(10,3),
          height DECIMAL(10,3),
          thickness DECIMAL(10,3),
          weight DECIMAL(10,3),
          color VARCHAR(100),
          description TEXT,
          lip_type VARCHAR(100),
          lip_thickness DECIMAL(10,3),
          with_baketa TINYINT(1),
          with_dalfa TINYINT(1),
          image_path VARCHAR(500),
          price_per_meter DECIMAL(10,2),
          is_active TINYINT(1) DEFAULT 1,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          FOREIGN KEY (series_id) REFERENCES profile_series(id) ON DELETE SET NULL,
          INDEX idx_profile_type (type),
          INDEX idx_profile_category (category),
          INDEX idx_profile_series (series_id),
          INDEX idx_profile_code (code),
          INDEX idx_profile_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول ربط القطاعات بمجموعات القطاعات
      await execute('''
        CREATE TABLE IF NOT EXISTS aluminum_profile_series (
          id INT AUTO_INCREMENT PRIMARY KEY,
          profile_id INT NOT NULL,
          series_id INT NOT NULL,
          is_primary TINYINT(1) DEFAULT 0,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          FOREIGN KEY (profile_id) REFERENCES aluminum_profiles(id) ON DELETE CASCADE,
          FOREIGN KEY (series_id) REFERENCES profile_series(id) ON DELETE CASCADE,
          UNIQUE KEY unique_profile_series (profile_id, series_id),
          INDEX idx_profile_series_profile (profile_id),
          INDEX idx_profile_series_series (series_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول المقايسات
      await execute('''
        CREATE TABLE IF NOT EXISTS aluminum_quotations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          quotation_number VARCHAR(100) NOT NULL UNIQUE,
          quotation_date BIGINT NOT NULL,
          client_name VARCHAR(255) NOT NULL,
          client_phone VARCHAR(50),
          client_address TEXT,
          notes TEXT,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          INDEX idx_quotation_number (quotation_number),
          INDEX idx_quotation_date (quotation_date),
          INDEX idx_client_name (client_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول عناصر المقايسات
      await execute('''
        CREATE TABLE IF NOT EXISTS aluminum_quotation_items (
          id INT AUTO_INCREMENT PRIMARY KEY,
          quotation_id INT NOT NULL,
          type VARCHAR(50) NOT NULL,
          profile_type VARCHAR(50),
          series_id INT,
          series_name VARCHAR(255),
          sash_count VARCHAR(50),
          track_count VARCHAR(50),
          width DECIMAL(10,3) NOT NULL,
          height DECIMAL(10,3) NOT NULL,
          quantity INT NOT NULL,
          notes TEXT,
          hinge_open_type VARCHAR(50) DEFAULT 'normal',
          has_panda TINYINT(1) DEFAULT 0,
          wire_type VARCHAR(50) DEFAULT 'none',
          vent_position VARCHAR(50) DEFAULT 'none',
          glass_type VARCHAR(50) DEFAULT 'single',
          inner_glass_type VARCHAR(100),
          outer_glass_type VARCHAR(100),
          georgia_type VARCHAR(50) DEFAULT 'none',
          georgia_count_width INT DEFAULT 0,
          georgia_count_height INT DEFAULT 0,
          spacing_type VARCHAR(50) DEFAULT 'air',
          created_at BIGINT NOT NULL,
          FOREIGN KEY (quotation_id) REFERENCES aluminum_quotations(id) ON DELETE CASCADE,
          FOREIGN KEY (series_id) REFERENCES profile_series(id) ON DELETE SET NULL,
          INDEX idx_quotation_item_quotation (quotation_id),
          INDEX idx_quotation_item_type (type),
          INDEX idx_quotation_item_series (series_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول تخصيمات المفصلي
      await execute('''
        CREATE TABLE IF NOT EXISTS hinge_designs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          series_id INT,
          dalfa_on_halaf DECIMAL(10,3),
          marad_dalfa_complete DECIMAL(10,3),
          marad_dalfa_with_kaab DECIMAL(10,3),
          marad_between_dalfa DECIMAL(10,3),
          dalfa_from_ground DECIMAL(10,3),
          dalfa_glass DECIMAL(10,3),
          fixed_glass DECIMAL(10,3),
          moving_silk DECIMAL(10,3),
          fixed_silk DECIMAL(10,3),
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          FOREIGN KEY (series_id) REFERENCES profile_series(id) ON DELETE CASCADE,
          INDEX idx_hinge_series (series_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول تخصيمات السحاب
      await execute('''
        CREATE TABLE IF NOT EXISTS sliding_designs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          series_id INT,
          dalfa_count INT,
          dalfa_method INT,
          dalfa_width_plus DECIMAL(10,3),
          dalfa_height_minus DECIMAL(10,3),
          skineh_height_minus DECIMAL(10,3),
          silk_width_plus DECIMAL(10,3),
          silk_height_minus DECIMAL(10,3),
          glass_width_minus DECIMAL(10,3),
          glass_height_minus DECIMAL(10,3),
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          FOREIGN KEY (series_id) REFERENCES profile_series(id) ON DELETE CASCADE,
          INDEX idx_sliding_series (series_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول الألومنيوم بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول الألومنيوم: $e');
      rethrow;
    }
  }

  /// إنشاء جداول التقطيع والمشاريع
  Future<void> _createCuttingTables() async {
    try {
      // جدول مشاريع التقطيع
      await execute('''
        CREATE TABLE IF NOT EXISTS cutting_projects (
          id INT AUTO_INCREMENT PRIMARY KEY,
          project_number VARCHAR(100) NOT NULL UNIQUE,
          customer_name VARCHAR(255) NOT NULL,
          date DATE NOT NULL,
          delivery_date DATE,
          phone VARCHAR(50),
          address TEXT,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_project_number (project_number),
          INDEX idx_customer_name (customer_name),
          INDEX idx_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول عناصر الطلبات
      await execute('''
        CREATE TABLE IF NOT EXISTS order_items (
          id INT AUTO_INCREMENT PRIMARY KEY,
          project_id INT NOT NULL,
          item_number VARCHAR(100) NOT NULL,
          item_name VARCHAR(255) NOT NULL,
          item_type VARCHAR(50) NOT NULL,
          required_boards INT DEFAULT 0,
          unit_price DECIMAL(10,2) DEFAULT 0.0,
          total_amount DECIMAL(10,2) DEFAULT 0.0,
          discount_percent DECIMAL(5,2) DEFAULT 0.0,
          discount_amount DECIMAL(10,2) DEFAULT 0.0,
          final_amount DECIMAL(10,2) DEFAULT 0.0,
          created_at BIGINT NOT NULL,
          FOREIGN KEY (project_id) REFERENCES cutting_projects(id) ON DELETE CASCADE,
          INDEX idx_order_project (project_id),
          INDEX idx_order_item_type (item_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول قياسات التقطيع
      await execute('''
        CREATE TABLE IF NOT EXISTS cutting_measurements (
          id INT AUTO_INCREMENT PRIMARY KEY,
          order_item_id INT NOT NULL,
          piece_size VARCHAR(100) NOT NULL,
          quantity INT NOT NULL,
          type VARCHAR(50) NOT NULL,
          number VARCHAR(50) NOT NULL,
          stick_length DECIMAL(10,3) DEFAULT 300.0,
          saw_blade_thickness DECIMAL(10,3) DEFAULT 0.5,
          created_at BIGINT NOT NULL,
          FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE CASCADE,
          INDEX idx_cutting_order_item (order_item_id),
          INDEX idx_cutting_type (type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول قياسات الألواح
      await execute('''
        CREATE TABLE IF NOT EXISTS panel_measurements (
          id INT AUTO_INCREMENT PRIMARY KEY,
          order_item_id INT NOT NULL,
          piece_width DECIMAL(10,3) NOT NULL,
          piece_height DECIMAL(10,3) NOT NULL,
          quantity INT NOT NULL,
          type VARCHAR(50) NOT NULL,
          number VARCHAR(50) NOT NULL,
          created_at BIGINT NOT NULL,
          FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE CASCADE,
          INDEX idx_panel_order_item (order_item_id),
          INDEX idx_panel_type (type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول التقطيع والمشاريع بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول التقطيع والمشاريع: $e');
      rethrow;
    }
  }

  /// إنشاء جداول الخزينة
  Future<void> _createTreasuryTables() async {
    try {
      // جدول الخزائن
      await execute('''
        CREATE TABLE IF NOT EXISTS treasuries (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          previous_balance DECIMAL(15,2) DEFAULT 0.0,
          current_balance DECIMAL(15,2) DEFAULT 0.0,
          date DATE NOT NULL,
          created_at BIGINT NOT NULL,
          INDEX idx_treasury_name (name),
          INDEX idx_treasury_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول معاملات الخزينة
      await execute('''
        CREATE TABLE IF NOT EXISTS treasury_transactions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          treasury_id INT NOT NULL,
          description TEXT NOT NULL,
          income DECIMAL(15,2) DEFAULT 0.0,
          expenses DECIMAL(15,2) DEFAULT 0.0,
          notes TEXT,
          date DATE NOT NULL,
          created_at BIGINT NOT NULL,
          invoice_id INT,
          invoice_number VARCHAR(100),
          FOREIGN KEY (treasury_id) REFERENCES treasuries(id) ON DELETE CASCADE,
          INDEX idx_treasury_trans_treasury (treasury_id),
          INDEX idx_treasury_trans_date (date),
          INDEX idx_treasury_trans_invoice (invoice_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول الخزينة بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول الخزينة: $e');
      rethrow;
    }
  }

  /// إنشاء جداول إدارة المهام
  Future<void> _createTaskTables() async {
    try {
      // جدول فئات المهام
      await execute('''
        CREATE TABLE IF NOT EXISTS task_categories (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          color INT NOT NULL,
          icon INT NOT NULL,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          INDEX idx_task_category_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول المهام
      await execute('''
        CREATE TABLE IF NOT EXISTS tasks (
          id INT AUTO_INCREMENT PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          category_id INT NOT NULL,
          priority INT NOT NULL,
          status INT NOT NULL,
          due_date BIGINT,
          notes TEXT,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          FOREIGN KEY (category_id) REFERENCES task_categories(id) ON DELETE CASCADE,
          INDEX idx_task_category (category_id),
          INDEX idx_task_priority (priority),
          INDEX idx_task_status (status),
          INDEX idx_task_due_date (due_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول إدارة المهام بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول إدارة المهام: $e');
      rethrow;
    }
  }

  /// إنشاء جداول الموردين
  Future<void> _createSupplierTables() async {
    try {
      // جدول الموردين
      await execute('''
        CREATE TABLE IF NOT EXISTS suppliers (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          phone VARCHAR(50),
          address TEXT,
          email VARCHAR(255),
          company VARCHAR(255),
          balance DECIMAL(15,2) DEFAULT 0.0,
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_supplier_name (name),
          INDEX idx_supplier_company (company),
          INDEX idx_supplier_phone (phone)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول الموردين بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول الموردين: $e');
      rethrow;
    }
  }

  /// إنشاء جداول مقايسات UPVC
  Future<void> _createUpvcTables() async {
    try {
      // جدول مقايسات UPVC
      await execute('''
        CREATE TABLE IF NOT EXISTS upvc_quotations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          quotation_number VARCHAR(100) NOT NULL UNIQUE,
          quotation_date BIGINT NOT NULL,
          client_name VARCHAR(255) NOT NULL,
          client_phone VARCHAR(50),
          client_address TEXT,
          notes TEXT,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          INDEX idx_upvc_quotation_number (quotation_number),
          INDEX idx_upvc_quotation_date (quotation_date),
          INDEX idx_upvc_client_name (client_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      // جدول عناصر مقايسات UPVC
      await execute('''
        CREATE TABLE IF NOT EXISTS upvc_quotation_items (
          id INT AUTO_INCREMENT PRIMARY KEY,
          quotation_id INT NOT NULL,
          type VARCHAR(50) NOT NULL,
          sash_count VARCHAR(50),
          track_count VARCHAR(50),
          width DECIMAL(10,3) NOT NULL,
          height DECIMAL(10,3) NOT NULL,
          quantity INT NOT NULL,
          notes TEXT,
          created_at BIGINT NOT NULL,
          FOREIGN KEY (quotation_id) REFERENCES upvc_quotations(id) ON DELETE CASCADE,
          INDEX idx_upvc_quotation_item_quotation (quotation_id),
          INDEX idx_upvc_quotation_item_type (type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      ''');

      debugPrint('تم إنشاء جداول مقايسات UPVC بنجاح');
    } catch (e) {
      debugPrint('خطأ في إنشاء جداول مقايسات UPVC: $e');
      rethrow;
    }
  }
    /// اختبار الاتصال بالإعدادات الحالية
  Future<bool> testConnection({bool useNewSettings = false}) async {
    try {
      // إذا كان المطلوب اختبار إعدادات جديدة، استخدم الاتصال القسري
      if (useNewSettings) {
        if (await connect(forceReconnect: true)) {
          await select('SELECT 1 as test');
          return true;
        }
      } else {
        // اختبار الاتصال الموجود أو إنشاء اتصال جديد
        if (await connect()) {
          await select('SELECT 1 as test');
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('فشل اختبار الاتصال: $e');
      _isConnected = false;
      _connection = null;
      return false;
    }
  }
  
  /// الحصول على معلومات قاعدة البيانات
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      if (!await _ensureConnection()) {
        throw Exception('لا يمكن الاتصال بقاعدة البيانات');
      }
      
      var result = await select('SELECT VERSION() as version');
      var tables = await select('SHOW TABLES');
      
      return {
        'version': result.isNotEmpty ? result.first['version'] : 'غير معروف',
        'tables_count': tables.length,
        'database_name': customDatabase ?? _database,
        'host': customHost ?? _host,
        'port': customPort ?? _port,
      };
    } catch (e) {
      debugPrint('خطأ في الحصول على معلومات قاعدة البيانات: $e');
      rethrow;
    }
  }
  
  /// مزامنة البيانات المحلية مع قاعدة البيانات السحابية
  Future<bool> syncData() async {
    try {
      // يمكن تنفيذ منطق المزامنة هنا
      // مثل رفع البيانات المحلية أو تحميل البيانات من السحابة
      
      debugPrint('تم بدء عملية المزامنة...');
      
      // مثال: رفع العملاء الجدد
      // await _syncCustomers();
      
      // مثال: رفع الفواتير الجديدة
      // await _syncInvoices();
      
      debugPrint('تمت المزامنة بنجاح');
      return true;
      
    } catch (e) {
      debugPrint('خطأ في المزامنة: $e');
      return false;
    }
  }
  
  /// اختبار إعدادات اتصال جديدة دون تأثير على الاتصال الحالي
  Future<bool> testNewSettings({
    required String host,
    required int port,
    required String database,
    required String username,
    required String password,
  }) async {
    MySqlConnection? testConnection;
    try {
      final settings = ConnectionSettings(
        host: host,
        port: port,
        user: username,
        password: password,
        db: database,
        timeout: const Duration(seconds: 10),
      );
      
      // إنشاء اتصال اختبار منفصل
      testConnection = await MySqlConnection.connect(settings);
      
      // اختبار بسيط للتأكد من عمل الاتصال
      await testConnection.query('SELECT 1 as test');
      
      debugPrint('نجح اختبار الإعدادات الجديدة');
      return true;
      
    } catch (e) {
      debugPrint('فشل اختبار الإعدادات الجديدة: $e');
      return false;
    } finally {
      // إغلاق اتصال الاختبار
      if (testConnection != null) {
        try {
          await testConnection.close();
        } catch (e) {
          debugPrint('خطأ في إغلاق اتصال الاختبار: $e');
        }
      }
    }
  }
}
