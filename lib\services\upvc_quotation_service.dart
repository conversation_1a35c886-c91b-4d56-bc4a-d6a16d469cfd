import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/upvc_quotation.dart';
import '../models/upvc_quotation_item.dart';

class UnifiedUpvcService {
  static Database? _database;
  static const String _quotationsTable = 'upvc_quotations';
  static const String _itemsTable = 'upvc_quotation_items';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'upvc_quotations.db');
    return await openDatabase(
      path,
      version: 2, // زيادة رقم الإصدار
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create quotations table
    await db.execute('''
      CREATE TABLE $_quotationsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_number TEXT NOT NULL UNIQUE,
        quotation_date INTEGER NOT NULL,
        client_name TEXT NOT NULL,
        client_phone TEXT,
        client_address TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create quotation items table
    await db.execute('''
      CREATE TABLE $_itemsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        sash_count TEXT,
        track_count TEXT,
        width REAL NOT NULL,
        height REAL NOT NULL,
        quantity INTEGER NOT NULL,
        notes TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (quotation_id) REFERENCES $_quotationsTable (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_quotation_number ON $_quotationsTable (quotation_number)');
    await db.execute('CREATE INDEX idx_quotation_date ON $_quotationsTable (quotation_date)');
    await db.execute('CREATE INDEX idx_client_name ON $_quotationsTable (client_name)');
    await db.execute('CREATE INDEX idx_quotation_items_quotation_id ON $_itemsTable (quotation_id)');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // إضافة الحقول الجديدة للجدول الموجود
      await db.execute('ALTER TABLE $_itemsTable ADD COLUMN sash_count TEXT');
      await db.execute('ALTER TABLE $_itemsTable ADD COLUMN track_count TEXT');
    }
  }

  // Quotation CRUD operations
  Future<int> insertQuotation(UpvcQuotation quotation) async {
    final db = await database;
    return await db.insert(_quotationsTable, quotation.toMap());
  }

  Future<List<UpvcQuotation>> getAllQuotations() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _quotationsTable,
      orderBy: 'quotation_date DESC',
    );
    return List.generate(maps.length, (i) => UpvcQuotation.fromMap(maps[i]));
  }

  Future<UpvcQuotation?> getQuotationById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _quotationsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return UpvcQuotation.fromMap(maps.first);
    }
    return null;
  }

  Future<UpvcQuotation?> getQuotationByNumber(String quotationNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _quotationsTable,
      where: 'quotation_number = ?',
      whereArgs: [quotationNumber],
    );
    if (maps.isNotEmpty) {
      return UpvcQuotation.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateQuotation(UpvcQuotation quotation) async {
    final db = await database;
    return await db.update(
      _quotationsTable,
      quotation.toMap(),
      where: 'id = ?',
      whereArgs: [quotation.id],
    );
  }

  Future<int> deleteQuotation(int id) async {
    final db = await database;
    // Delete items first (cascade should handle this, but being explicit)
    await db.delete(_itemsTable, where: 'quotation_id = ?', whereArgs: [id]);
    return await db.delete(_quotationsTable, where: 'id = ?', whereArgs: [id]);
  }

  Future<List<UpvcQuotation>> searchQuotations(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _quotationsTable,
      where: 'quotation_number LIKE ? OR client_name LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'quotation_date DESC',
    );
    return List.generate(maps.length, (i) => UpvcQuotation.fromMap(maps[i]));
  }

  // Quotation Items CRUD operations
  Future<int> insertQuotationItem(UpvcQuotationItem item) async {
    final db = await database;
    return await db.insert(_itemsTable, item.toMap());
  }

  Future<List<UpvcQuotationItem>> getQuotationItems(int quotationId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _itemsTable,
      where: 'quotation_id = ?',
      whereArgs: [quotationId],
      orderBy: 'created_at ASC',
    );
    return List.generate(maps.length, (i) => UpvcQuotationItem.fromMap(maps[i]));
  }

  Future<int> updateQuotationItem(UpvcQuotationItem item) async {
    final db = await database;
    return await db.update(
      _itemsTable,
      item.toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<int> deleteQuotationItem(int id) async {
    final db = await database;
    return await db.delete(_itemsTable, where: 'id = ?', whereArgs: [id]);
  }

  // Utility methods
  Future<String> generateQuotationNumber() async {
    final now = DateTime.now();
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');

    final db = await database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM $_quotationsTable
      WHERE quotation_number LIKE ?
    ''', ['$year$month%']);

    final count = result.first['count'] as int;
    final sequence = (count + 1).toString().padLeft(3, '0');

    return '$year$month$sequence';
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
