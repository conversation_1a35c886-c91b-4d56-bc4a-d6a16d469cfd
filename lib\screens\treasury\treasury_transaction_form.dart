// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';
import '../../services/unified_treasury_service.dart' as unified;
import '../../providers/database_provider.dart';

class TreasuryTransactionForm extends StatefulWidget {
  final int treasuryId;
  final TreasuryTransaction? transaction;

  const TreasuryTransactionForm({
    super.key,
    required this.treasuryId,
    this.transaction,
  });

  @override
  State<TreasuryTransactionForm> createState() => _TreasuryTransactionFormState();
}

class _TreasuryTransactionFormState extends State<TreasuryTransactionForm> {
  late unified.UnifiedTreasuryService _databaseHelper;
  final _formKey = GlobalKey<FormState>();

  late TextEditingController _descriptionController;
  late TextEditingController _incomeController;
  late TextEditingController _expensesController;
  late TextEditingController _notesController;
  late TextEditingController _dateController;

  Treasury? _treasury;
  List<TreasuryTransaction> _transactions = [];
  bool _isLoading = true;
  bool _isLoadingDayTransactions = false;
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  // ignore: unused_field
  double _balance = 0.0;
  double _selectedDayIncome = 0.0;
  double _selectedDayExpenses = 0.0;
  // ignore: unused_field
  double _selectedDayBalance = 0.0;

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController();
    _incomeController = TextEditingController(text: '0.00');
    _expensesController = TextEditingController(text: '0.00');
    _notesController = TextEditingController();    final now = DateTime.now();
    _dateController = TextEditingController(
      text: DateFormat('yyyy-MM-dd').format(now),
    );

    _loadData();
    _loadTransactionsForDate(now);

    if (widget.transaction != null) {
      _descriptionController.text = widget.transaction!.description;
      _incomeController.text = widget.transaction!.income.toStringAsFixed(2);
      _expensesController.text = widget.transaction!.expenses.toStringAsFixed(2);
      _notesController.text = widget.transaction!.notes;
      _dateController.text = DateFormat('yyyy-MM-dd').format(widget.transaction!.date);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _databaseHelper = DatabaseProvider.of(context);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _incomeController.dispose();
    _expensesController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final treasuryData = await _databaseHelper.getTreasuryById(widget.treasuryId);
      final treasury = treasuryData != null ? Treasury.fromMap(treasuryData) : null;
      final transactionsData = await _databaseHelper.getTreasuryTransactions(widget.treasuryId);
      final transactions = transactionsData.map((data) => TreasuryTransaction.fromMap(data)).toList();
      final stats = await _databaseHelper.getTreasuryStatistics(widget.treasuryId);

      setState(() {
        _treasury = treasury;
        _transactions = transactions;
        _totalIncome = stats['total_income'] as double? ?? 0.0;
        _totalExpenses = stats['total_expenses'] as double? ?? 0.0;
        _balance = stats['current_balance'] as double? ?? 0.0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadTransactionsForDate(DateTime date) async {
    setState(() {
      _isLoadingDayTransactions = true;
    });

    try {
      final startDate = DateTime(date.year, date.month, date.day);
      final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final transactionsData = await _databaseHelper.getTransactionsByDateRange(
        widget.treasuryId,
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      );
      final transactions = transactionsData.map((data) => TreasuryTransaction.fromMap(data)).toList();

      // Calculate totals for the selected date
      double dayIncome = 0.0;
      double dayExpenses = 0.0;

      for (final transaction in transactions) {
        dayIncome += transaction.income;
        dayExpenses += transaction.expenses;
      }

      setState(() {
        _transactions = transactions;
        _selectedDayIncome = dayIncome;
        _selectedDayExpenses = dayExpenses;
        _selectedDayBalance = dayIncome - dayExpenses;
        _isLoadingDayTransactions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingDayTransactions = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading transactions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final description = _descriptionController.text.trim();
    final income = double.tryParse(_incomeController.text) ?? 0.0;
    final expenses = double.tryParse(_expensesController.text) ?? 0.0;
    final notes = _notesController.text.trim();
    final date = DateFormat('yyyy-MM-dd').parse(_dateController.text);

    if (income == 0.0 && expenses == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.incomeOrExpensesRequired),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      if (widget.transaction == null) {
        // Create new transaction
        final transaction = TreasuryTransaction(
          treasuryId: widget.treasuryId,
          description: description,
          income: income,
          expenses: expenses,
          notes: notes,
          date: date,
        );

        await _databaseHelper.insertTreasuryTransaction(transaction.toMap());
      } else {
        // Update existing transaction
        final updatedTransaction = widget.transaction!.copyWith(
          description: description,
          income: income,
          expenses: expenses,
          notes: notes,
          date: date,
        );

        await _databaseHelper.updateTreasuryTransaction(updatedTransaction.id!, updatedTransaction.toMap());
      }

      // Reset form and reload data
      if (mounted) {
        _resetForm();
        _loadData();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.transaction == null
                  ? AppLocalizations.of(context)!.transactionAdded
                  : AppLocalizations.of(context)!.transactionUpdated,
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resetForm() {
    _descriptionController.clear();
    _incomeController.text = '0.00';
    _expensesController.text = '0.00';
    _notesController.clear();
    final now = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(now);
    _formKey.currentState?.reset();
    _loadTransactionsForDate(now);
  }
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(_treasury?.name ?? localizations.treasury),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(24),
                      bottomRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                  child: _buildTreasuryInfoCard(),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: size.width < 600 ? 16 : size.width * 0.1,
                    vertical: 24,
                  ),
                  child: Column(
                    children: [
                      _buildTransactionForm(localizations),
                      const SizedBox(height: 24),
                      _buildTransactionsForSelectedDate(),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildTreasuryInfoCard() {
    if (_treasury == null) return const SizedBox.shrink();

    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          _treasury!.name,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 24),
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            _buildInfoBox(
              localizations.previousBalance,
              NumberFormat("#,##0.00").format(_treasury!.previousBalance),
              Colors.white.withValues(alpha: 0.2),
              textColor: Colors.white,
            ),
            _buildInfoBox(
              localizations.currentBalance,
              NumberFormat("#,##0.00").format(_treasury!.currentBalance),
              Colors.white.withValues(alpha: 0.2),
              textColor: Colors.white,
            ),
            _buildInfoBox(
              localizations.income,
              NumberFormat("#,##0.00").format(_totalIncome),
              Colors.white.withValues(alpha: 0.2),
              textColor: Colors.white,
              isIncome: true,
            ),
            _buildInfoBox(
              localizations.expenses,
              NumberFormat("#,##0.00").format(_totalExpenses),
              Colors.white.withValues(alpha: 0.2),
              textColor: Colors.white,
              isIncome: false,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoBox(
    String label,
    String value,
    Color backgroundColor,
    {
      Color? textColor,
      bool? isIncome,
    }
  ) {
    return Container(
      constraints: const BoxConstraints(minWidth: 140),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: textColor?.withValues(alpha: 0.7),
                ),
              ),
              if (isIncome != null) ...[
                const SizedBox(width: 4),
                Icon(
                  isIncome ? Icons.arrow_upward : Icons.arrow_downward,
                  color: isIncome ? Colors.green : Colors.red,
                  size: 14,
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionForm(AppLocalizations localizations) {
    final selectedDate = DateFormat('yyyy-MM-dd').parse(_dateController.text);
    final formattedDate = DateFormat('MM/dd/yyyy').format(selectedDate);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.transaction == null
                    ? localizations.addTransaction
                    : localizations.editTransaction,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 20),
              // تاريخ المعاملة
              InkWell(
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    setState(() {
                      _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                    });
                    _loadTransactionsForDate(date);
                  }
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.date,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.primary,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            formattedDate,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              // وصف المعاملة
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: localizations.description,
                  hintText: localizations.enterDescription,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.description),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.descriptionRequired;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              // المبالغ
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _incomeController,
                      decoration: InputDecoration(
                        labelText: localizations.income,
                        hintText: '0.00',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.arrow_downward, color: Colors.green),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _expensesController,
                      decoration: InputDecoration(
                        labelText: localizations.expenses,
                        hintText: '0.00',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.arrow_upward, color: Colors.red),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // الملاحظات
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: localizations.notes,
                  hintText: localizations.enterNotes,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: const Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              // أزرار العمليات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _resetForm,
                      icon: const Icon(Icons.refresh),
                      label: Text(localizations.reset),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: FilledButton.icon(
                      onPressed: _saveTransaction,
                      icon: const Icon(Icons.save),
                      label: Text(localizations.save),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionsForSelectedDate() {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    final selectedDate = DateFormat('yyyy-MM-dd').parse(_dateController.text);
    final formattedDate = DateFormat('MM/dd/yyyy').format(selectedDate);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          formattedDate,
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          localizations.transactions,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(20),
                      ),                      child: Text(
                        '${_transactions.length} ${localizations.transactions}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: _buildDaySummaryBox(
                        localizations.income,
                        _selectedDayIncome,
                        Colors.white.withValues(alpha: 0.7),
                        isPositive: true,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDaySummaryBox(
                        localizations.expenses,
                        _selectedDayExpenses,
                        Colors.white.withValues(alpha: 0.7),
                        isPositive: false,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isLoadingDayTransactions)
            const Padding(
              padding: EdgeInsets.all(20),
              child: Center(
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                ),
              ),
            )
          else if (_transactions.isEmpty)
            Container(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_long_outlined,
                      size: 48,
                      color: theme.colorScheme.primary.withValues(alpha: 0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      localizations.noTransactions,
                      style: TextStyle(
                        color: theme.colorScheme.primary.withValues(alpha: 0.7),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _transactions.length,
              separatorBuilder: (context, index) => Divider(
                color: theme.dividerColor.withValues(alpha: 0.7),
              ),
              itemBuilder: (context, index) {
                final transaction = _transactions[index];
                return ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  title: Text(
                    transaction.description,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: transaction.notes.isNotEmpty
                      ? Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            transaction.notes,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: theme.textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                            ),
                          ),
                        )
                      : null,
                  trailing: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (transaction.income > 0)
                        Text(
                          '+${NumberFormat("#,##0.00").format(transaction.income)}',
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                      if (transaction.expenses > 0)
                        Text(
                          '-${NumberFormat("#,##0.00").format(transaction.expenses)}',
                          style: const TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                    ],
                  ),
                  onTap: () => _showTransactionActions(transaction),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildDaySummaryBox(String label, double value, Color backgroundColor, {bool isPositive = true}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 13,
                ),
              ),
              const SizedBox(width: 4),
              Icon(
                isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                color: Colors.white,
                size: 14,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            NumberFormat("#,##0.00").format(value),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
          ),
        ],
      ),
    );
  }

  void _showTransactionActions(TreasuryTransaction transaction) {
    final localizations = AppLocalizations.of(context)!;

    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: Text(localizations.edit),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _descriptionController.text = transaction.description;
                  _incomeController.text = transaction.income.toStringAsFixed(2);
                  _expensesController.text = transaction.expenses.toStringAsFixed(2);
                  _notesController.text = transaction.notes;
                  _dateController.text = DateFormat('yyyy-MM-dd').format(transaction.date);
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: Text(localizations.delete),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmationDialog(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(TreasuryTransaction transaction) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteTransaction),
        content: Text(localizations.deleteTransactionConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          FilledButton(
            onPressed: () async {
              try {
                await _databaseHelper.deleteTreasuryTransaction(transaction.id!);
                if (mounted) {
                  Navigator.pop(context);
                  _loadData();
                  _loadTransactionsForDate(transaction.date);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(localizations.transactionDeleted),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}
