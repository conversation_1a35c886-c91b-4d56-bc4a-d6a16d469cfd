import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'lib/services/unified_database_service.dart';

/// اختبار مبسط للخدمات الموحدة الجديدة
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🧪 اختبار مبسط للخدمات الموحدة الجديدة...\n');

  try {
    final unifiedDb = UnifiedDatabaseService();
    
    // اختبار الاتصال بقاعدة البيانات الموحدة
    // ignore: unused_local_variable
    final db = await unifiedDb.database;
    print('✅ تم الاتصال بقاعدة البيانات الموحدة بنجاح');
    
    // اختبار استرجاع البيانات الموجودة
    await testInvoiceData(unifiedDb);
    await testTreasuryData(unifiedDb);
    await testCuttingData(unifiedDb);
    await testAluminumData(unifiedDb);
    await testUpvcData(unifiedDb);
    await testTaskData(unifiedDb);
    
    // اختبار العمليات الأساسية
    await testBasicOperations(unifiedDb);
    
    await unifiedDb.close();
    
    print('\n🎉 تم اكتمال جميع اختبارات الخدمات الموحدة بنجاح!');
    print('✅ جميع الخدمات الموحدة تعمل بشكل صحيح!');
    
  } catch (e) {
    print('❌ خطأ في اختبار الخدمات الموحدة: $e');
  }
}

/// اختبار بيانات الفواتير
Future<void> testInvoiceData(UnifiedDatabaseService unifiedDb) async {
  print('📋 اختبار بيانات الفواتير...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final customers = await unifiedDb.getAllCustomers();
    print('   👥 العملاء: ${customers.length} عميل');
    
    final suppliers = await unifiedDb.getAllSuppliers();
    print('   🏢 الموردين: ${suppliers.length} مورد');
    
    final invoices = await unifiedDb.getAllInvoices();
    print('   📄 الفواتير: ${invoices.length} فاتورة');
    
    // اختبار إحصائيات المبيعات
    final db = await unifiedDb.database;
    final salesResult = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM invoices WHERE type = ?',
      ['sale']
    );
    final totalSales = (salesResult.first['total'] as double?) ?? 0.0;
    print('   💰 إجمالي المبيعات: ${totalSales.toStringAsFixed(2)} ريال');
    
    // اختبار البحث
    final searchResults = await db.query('invoices', 
      where: 'invoiceNumber LIKE ? OR supplierOrCustomerName LIKE ?',
      whereArgs: ['%فاتورة%', '%عميل%']
    );
    print('   🔍 نتائج البحث: ${searchResults.length} فاتورة');
    
    print('   ✅ بيانات الفواتير تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات الفواتير: $e');
  }
}

/// اختبار بيانات الخزينة
Future<void> testTreasuryData(UnifiedDatabaseService unifiedDb) async {
  print('\n💰 اختبار بيانات الخزينة...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final treasuries = await unifiedDb.getAllTreasuries();
    print('   🏦 الخزائن: ${treasuries.length} خزينة');
    
    final db = await unifiedDb.database;
    final transactions = await db.query('treasury_transactions');
    print('   💳 المعاملات: ${transactions.length} معاملة');
    
    // اختبار إحصائيات الخزينة إذا كانت موجودة
    if (treasuries.isNotEmpty) {
      final treasuryId = treasuries.first['id'] as int;
      
      // حساب الإحصائيات
      final statsResult = await db.rawQuery('''
        SELECT 
          SUM(income) as total_income,
          SUM(expenses) as total_expenses
        FROM treasury_transactions
        WHERE treasury_id = ?
      ''', [treasuryId]);
      
      final totalIncome = (statsResult.first['total_income'] as double?) ?? 0.0;
      final totalExpenses = (statsResult.first['total_expenses'] as double?) ?? 0.0;
      
      print('   📊 إحصائيات الخزينة الأولى:');
      print('      📈 إجمالي الإيرادات: $totalIncome ريال');
      print('      📉 إجمالي المصروفات: $totalExpenses ريال');
      print('      💰 الرصيد الصافي: ${totalIncome - totalExpenses} ريال');
    }
    
    print('   ✅ بيانات الخزينة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات الخزينة: $e');
  }
}

/// اختبار بيانات التقطيع
Future<void> testCuttingData(UnifiedDatabaseService unifiedDb) async {
  print('\n✂️ اختبار بيانات التقطيع...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final projects = await unifiedDb.getAllCuttingProjects();
    print('   📋 مشاريع التقطيع: ${projects.length} مشروع');
    
    final db = await unifiedDb.database;
    
    // عدد العناصر
    final itemsResult = await db.rawQuery('SELECT COUNT(*) as count FROM order_items');
    final itemsCount = itemsResult.first['count'] as int;
    print('   📦 عناصر الطلبات: $itemsCount عنصر');
    
    // عدد القياسات
    final measurementsResult = await db.rawQuery('SELECT COUNT(*) as count FROM cutting_measurements');
    final measurementsCount = measurementsResult.first['count'] as int;
    print('   📏 قياسات التقطيع: $measurementsCount قياس');
    
    print('   ✅ بيانات التقطيع تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات التقطيع: $e');
  }
}

/// اختبار بيانات الألومنيوم
Future<void> testAluminumData(UnifiedDatabaseService unifiedDb) async {
  print('\n🔧 اختبار بيانات الألومنيوم...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final profiles = await unifiedDb.getAllAluminumProfiles();
    print('   🔧 قطاعات الألومنيوم: ${profiles.length} قطاع');
    
    final db = await unifiedDb.database;
    
    // عدد عروض الأسعار
    final quotationsResult = await db.rawQuery('SELECT COUNT(*) as count FROM aluminum_quotations');
    final quotationsCount = quotationsResult.first['count'] as int;
    print('   💰 عروض أسعار الألومنيوم: $quotationsCount عرض سعر');
    
    // عدد التصاميم
    final hingeDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM hinge_designs');
    final hingeDesignsCount = hingeDesignsResult.first['count'] as int;
    print('   🚪 تصاميم المفصلي: $hingeDesignsCount تصميم');
    
    final slidingDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM sliding_designs');
    final slidingDesignsCount = slidingDesignsResult.first['count'] as int;
    print('   🪟 تصاميم السحاب: $slidingDesignsCount تصميم');
    
    // اختبار البحث
    if (profiles.isNotEmpty) {
      final searchResults = await db.query('aluminum_profiles',
        where: 'name LIKE ? OR code LIKE ?',
        whereArgs: ['%ألومنيوم%', '%ALU%']
      );
      print('   🔍 نتائج البحث: ${searchResults.length} قطاع');
    }
    
    print('   ✅ بيانات الألومنيوم تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات الألومنيوم: $e');
  }
}

/// اختبار بيانات uPVC
Future<void> testUpvcData(UnifiedDatabaseService unifiedDb) async {
  print('\n🔩 اختبار بيانات uPVC...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final profiles = await unifiedDb.getAllUpvcProfiles();
    print('   🔩 قطاعات uPVC: ${profiles.length} قطاع');
    
    final db = await unifiedDb.database;
    
    // عدد عروض الأسعار
    final quotationsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_quotations');
    final quotationsCount = quotationsResult.first['count'] as int;
    print('   💰 عروض أسعار uPVC: $quotationsCount عرض سعر');
    
    // عدد التصاميم
    final hingeDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_hinge_designs');
    final hingeDesignsCount = hingeDesignsResult.first['count'] as int;
    print('   🚪 تصاميم المفصلي uPVC: $hingeDesignsCount تصميم');
    
    final slidingDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_sliding_designs');
    final slidingDesignsCount = slidingDesignsResult.first['count'] as int;
    print('   🪟 تصاميم السحاب uPVC: $slidingDesignsCount تصميم');
    
    print('   ✅ بيانات uPVC تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات uPVC: $e');
  }
}

/// اختبار بيانات المهام
Future<void> testTaskData(UnifiedDatabaseService unifiedDb) async {
  print('\n📝 اختبار بيانات المهام...');
  
  try {
    // اختبار استرجاع البيانات الموجودة
    final tasks = await unifiedDb.getAllTasks();
    print('   📝 المهام: ${tasks.length} مهمة');
    
    final categories = await unifiedDb.getAllTaskCategories();
    print('   📂 فئات المهام: ${categories.length} فئة');
    
    print('   ✅ بيانات المهام تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في بيانات المهام: $e');
  }
}

/// اختبار العمليات الأساسية
Future<void> testBasicOperations(UnifiedDatabaseService unifiedDb) async {
  print('\n🧪 اختبار العمليات الأساسية...');
  
  try {
    // اختبار إدراج عميل جديد
    final newCustomerId = await unifiedDb.insertCustomer({
      'name': 'عميل تجريبي من الخدمات الموحدة',
      'phone': '0501111111',
      'address': 'جدة، السعودية',
      'email': '<EMAIL>',
      'balance': 2000.0,
      'notes': 'عميل تجريبي للاختبار',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج عميل جديد (ID: $newCustomerId)');
    
    // اختبار إدراج فاتورة جديدة
    final newInvoiceId = await unifiedDb.insertInvoice({
      'invoiceNumber': 'UNI-SRV-001',
      'date': DateTime.now().toIso8601String(),
      'supplierOrCustomerName': 'عميل تجريبي من الخدمات الموحدة',
      'type': 'sale',
      'totalAmount': 3000.0,
      'discount': 50.0,
      'expenses': 25.0,
      'netAmount': 2975.0,
      'notes': 'فاتورة تجريبية من الخدمات الموحدة',
      'treasuryId': null,
      'isPaid': 0,
      'treasuryTransactionId': null,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج فاتورة جديدة (ID: $newInvoiceId)');
    
    // اختبار إدراج قطاع ألومنيوم جديد
    final newProfileId = await unifiedDb.insertAluminumProfile({
      'name': 'قطاع ألومنيوم تجريبي من الخدمات الموحدة',
      'code': 'UNI-SRV-ALU-001',
      'type': 'frame',
      'category': 'window',
      'series_id': null,
      'width': 60.0,
      'height': 40.0,
      'thickness': 2.5,
      'weight': 2.0,
      'color': 'فضي',
      'description': 'قطاع ألومنيوم تجريبي من الخدمات الموحدة',
      'lip_type': 'standard',
      'lip_thickness': 1.2,
      'with_baketa': 1,
      'with_dalfa': 0,
      'image_path': null,
      'price_per_meter': 30.0,
      'is_active': 1,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج قطاع ألومنيوم جديد (ID: $newProfileId)');
    
    // عرض الإحصائيات النهائية
    print('\n📊 الإحصائيات النهائية بعد الاختبار:');
    final finalCustomers = await unifiedDb.getAllCustomers();
    final finalInvoices = await unifiedDb.getAllInvoices();
    final finalProfiles = await unifiedDb.getAllAluminumProfiles();
    
    print('   👥 إجمالي العملاء: ${finalCustomers.length}');
    print('   📄 إجمالي الفواتير: ${finalInvoices.length}');
    print('   🔧 إجمالي قطاعات الألومنيوم: ${finalProfiles.length}');
    
    print('   ✅ جميع العمليات الأساسية تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في العمليات الأساسية: $e');
  }
}
