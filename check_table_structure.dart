import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';

/// فحص هيكل الجداول في قاعدة البيانات الموحدة
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🔍 فحص هيكل الجداول في قاعدة البيانات الموحدة...\n');

  try {
    // فتح قاعدة البيانات الموحدة
    final databasesPath = await getDatabasesPath();
    final unifiedDbPath = join(databasesPath, 'uptime_unified_database.db');
    final unifiedDb = await openDatabase(unifiedDbPath);
    
    // فحص هيكل جدول العملاء
    print('📋 هيكل جدول العملاء في قاعدة البيانات الموحدة:');
    final customersSchema = await unifiedDb.rawQuery("PRAGMA table_info(customers)");
    for (final column in customersSchema) {
      print('   📄 ${column['name']}: ${column['type']} ${column['notnull'] == 1 ? 'NOT NULL' : ''} ${column['dflt_value'] != null ? 'DEFAULT ${column['dflt_value']}' : ''}');
    }
    
    // فحص هيكل جدول العملاء في قاعدة البيانات القديمة
    print('\n📋 هيكل جدول العملاء في قاعدة البيانات القديمة:');
    final oldDbPath = join(databasesPath, 'invoices_database_v2.db');
    final oldDb = await openDatabase(oldDbPath);
    final oldCustomersSchema = await oldDb.rawQuery("PRAGMA table_info(customers)");
    for (final column in oldCustomersSchema) {
      print('   📄 ${column['name']}: ${column['type']} ${column['notnull'] == 1 ? 'NOT NULL' : ''} ${column['dflt_value'] != null ? 'DEFAULT ${column['dflt_value']}' : ''}');
    }
    
    // مقارنة البيانات
    print('\n📊 مقارنة البيانات:');
    final unifiedCustomers = await unifiedDb.query('customers');
    print('   📈 عدد العملاء في قاعدة البيانات الموحدة: ${unifiedCustomers.length}');
    
    final oldCustomers = await oldDb.query('customers');
    print('   📈 عدد العملاء في قاعدة البيانات القديمة: ${oldCustomers.length}');
    
    if (oldCustomers.isNotEmpty) {
      print('\n📋 عينة من بيانات العملاء في قاعدة البيانات القديمة:');
      for (final customer in oldCustomers.take(3)) {
        print('   👤 ${customer['name']} - الأعمدة: ${customer.keys.join(', ')}');
      }
    }
    
    await unifiedDb.close();
    await oldDb.close();
    
  } catch (e) {
    print('❌ خطأ في فحص هيكل الجداول: $e');
  }
}
