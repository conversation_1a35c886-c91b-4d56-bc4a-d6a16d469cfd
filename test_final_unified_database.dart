import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'lib/services/unified_database_service.dart';

/// اختبار شامل لقاعدة البيانات الموحدة النهائية
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🎯 اختبار شامل لقاعدة البيانات الموحدة النهائية...\n');

  try {
    final unifiedDb = UnifiedDatabaseService();
    
    // عرض معلومات قاعدة البيانات
    final dbInfo = await unifiedDb.getDatabaseInfo();
    
    print('📊 معلومات قاعدة البيانات الموحدة:');
    print('   📁 اسم قاعدة البيانات: ${dbInfo['database_name']}');
    print('   🔢 إصدار قاعدة البيانات: ${dbInfo['version']}');
    print('   📊 عدد الجداول: ${dbInfo['tables_count']}');
    print('   📈 إجمالي السجلات: ${(dbInfo['table_counts'] as Map<String, int>).values.fold(0, (sum, count) => sum + count)}');
    
    // اختبار استرجاع البيانات المنقولة
    print('\n🔍 اختبار استرجاع البيانات المنقولة:');
    
    // اختبار العملاء
    final customers = await unifiedDb.getAllCustomers();
    print('   👥 العملاء: ${customers.length} عميل');
    if (customers.isNotEmpty) {
      print('      📋 عينة: ${customers.first['name']} - ${customers.first['phone']}');
    }
    
    // اختبار الموردين
    final suppliers = await unifiedDb.getAllSuppliers();
    print('   🏢 الموردين: ${suppliers.length} مورد');
    if (suppliers.isNotEmpty) {
      print('      📋 عينة: ${suppliers.first['name']} - ${suppliers.first['company']}');
    }
    
    // اختبار الفواتير
    final invoices = await unifiedDb.getAllInvoices();
    print('   📄 الفواتير: ${invoices.length} فاتورة');
    if (invoices.isNotEmpty) {
      print('      📋 عينة: ${invoices.first['invoiceNumber']} - ${invoices.first['supplierOrCustomerName']} - ${invoices.first['totalAmount']} ريال');
    }
    
    // اختبار عناصر الفواتير
    final db = await unifiedDb.database;
    final invoiceItems = await db.query('invoice_items');
    print('   📦 عناصر الفواتير: ${invoiceItems.length} عنصر');
    if (invoiceItems.isNotEmpty) {
      print('      📋 عينة: ${invoiceItems.first['itemName']} - ${invoiceItems.first['quantity']} ${invoiceItems.first['unit']}');
    }
    
    // اختبار قطاعات الألومنيوم
    final aluminumProfiles = await unifiedDb.getAllAluminumProfiles();
    print('   🔧 قطاعات الألومنيوم: ${aluminumProfiles.length} قطاع');
    if (aluminumProfiles.isNotEmpty) {
      print('      📋 عينة: ${aluminumProfiles.first['name']} - ${aluminumProfiles.first['code']}');
    }
    
    // اختبار العمليات الأساسية
    print('\n🧪 اختبار العمليات الأساسية:');
    
    // إدراج عميل جديد
    final newCustomerId = await unifiedDb.insertCustomer({
      'name': 'عميل جديد من قاعدة البيانات الموحدة',
      'phone': '0501111111',
      'address': 'جدة، السعودية',
      'email': '<EMAIL>',
      'balance': 2000.0,
      'notes': 'عميل جديد للاختبار',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج عميل جديد (ID: $newCustomerId)');
    
    // إدراج مورد جديد
    final newSupplierId = await unifiedDb.insertSupplier({
      'name': 'مورد جديد من قاعدة البيانات الموحدة',
      'phone': '0502222222',
      'address': 'الدمام، السعودية',
      'email': '<EMAIL>',
      'company': 'شركة الموردين الموحدة',
      'balance': 0.0,
      'notes': 'مورد جديد للاختبار',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج مورد جديد (ID: $newSupplierId)');
    
    // إدراج فاتورة جديدة
    final newInvoiceId = await unifiedDb.insertInvoice({
      'invoiceNumber': 'UNI000001',
      'date': DateTime.now().toIso8601String(),
      'supplierOrCustomerName': 'عميل جديد من قاعدة البيانات الموحدة',
      'type': 'sale',
      'totalAmount': 5000.0,
      'discount': 100.0,
      'expenses': 50.0,
      'netAmount': 4950.0,
      'notes': 'فاتورة تجريبية من قاعدة البيانات الموحدة',
      'treasuryId': null,
      'isPaid': 0,
      'treasuryTransactionId': null,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج فاتورة جديدة (ID: $newInvoiceId)');
    
    // إدراج عنصر فاتورة
    final newItemId = await unifiedDb.insertInvoiceItem({
      'invoiceId': newInvoiceId,
      'itemName': 'منتج تجريبي من قاعدة البيانات الموحدة',
      'unit': 'قطعة',
      'quantity': 10.0,
      'price': 500.0,
      'discount': 100.0,
      'total': 4900.0,
    });
    print('   ✅ تم إدراج عنصر فاتورة جديد (ID: $newItemId)');
    
    // إدراج قطاع ألومنيوم جديد
    final newProfileId = await unifiedDb.insertAluminumProfile({
      'name': 'قطاع ألومنيوم تجريبي موحد',
      'code': 'UNI-ALU-001',
      'type': 'frame',
      'category': 'window',
      'series_id': null,
      'width': 50.0,
      'height': 30.0,
      'thickness': 2.0,
      'weight': 1.5,
      'color': 'أبيض',
      'description': 'قطاع ألومنيوم تجريبي من قاعدة البيانات الموحدة',
      'lip_type': 'standard',
      'lip_thickness': 1.0,
      'with_baketa': 1,
      'with_dalfa': 0,
      'image_path': null,
      'price_per_meter': 25.0,
      'is_active': 1,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج قطاع ألومنيوم جديد (ID: $newProfileId)');
    
    // عرض الإحصائيات النهائية
    print('\n📊 الإحصائيات النهائية:');
    final finalCustomers = await unifiedDb.getAllCustomers();
    final finalSuppliers = await unifiedDb.getAllSuppliers();
    final finalInvoices = await unifiedDb.getAllInvoices();
    final finalProfiles = await unifiedDb.getAllAluminumProfiles();
    
    print('   👥 إجمالي العملاء: ${finalCustomers.length}');
    print('   🏢 إجمالي الموردين: ${finalSuppliers.length}');
    print('   📄 إجمالي الفواتير: ${finalInvoices.length}');
    print('   🔧 إجمالي قطاعات الألومنيوم: ${finalProfiles.length}');
    
    // اختبار البحث والاستعلامات
    print('\n🔍 اختبار البحث والاستعلامات:');
    
    // البحث عن عميل
    final searchCustomers = await db.query('customers', 
      where: 'name LIKE ?', 
      whereArgs: ['%عميل جديد%']
    );
    print('   🔍 البحث عن العملاء الجدد: ${searchCustomers.length} نتيجة');
    
    // البحث عن فواتير المبيعات
    final salesInvoices = await db.query('invoices', 
      where: 'type = ?', 
      whereArgs: ['sale']
    );
    print('   🔍 فواتير المبيعات: ${salesInvoices.length} فاتورة');
    
    // حساب إجمالي المبيعات
    final totalSales = await db.rawQuery(
      'SELECT SUM(totalAmount) as total FROM invoices WHERE type = ?',
      ['sale']
    );
    final totalAmount = totalSales.first['total'] ?? 0.0;
    print('   💰 إجمالي المبيعات: $totalAmount ريال');
    
    await unifiedDb.close();
    
    print('\n🎉 تم اكتمال جميع الاختبارات بنجاح!');
    print('✅ قاعدة البيانات الموحدة جاهزة للاستخدام في الإنتاج!');
    
  } catch (e) {
    print('❌ خطأ في اختبار قاعدة البيانات الموحدة: $e');
  }
}
