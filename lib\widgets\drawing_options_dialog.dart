import 'package:flutter/material.dart';
import '../models/aluminum_quotation_item.dart';

class DrawingOptionsDialog extends StatefulWidget {
  final AluminumQuotationItem item;
  final Function(AluminumQuotationItem) onSave;

  const DrawingOptionsDialog({
    super.key,
    required this.item,
    required this.onSave,
  });

  @override
  State<DrawingOptionsDialog> createState() => _DrawingOptionsDialogState();
}

class _DrawingOptionsDialogState extends State<DrawingOptionsDialog> {
  late DrawingOptions _options;
  final _innerGlassController = TextEditingController();
  final _outerGlassController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _options = widget.item.drawingOptions;
    _innerGlassController.text = _options.innerGlassType ?? '';
    _outerGlassController.text = _options.outerGlassType ?? '';
  }

  @override
  void dispose() {
    _innerGlassController.dispose();
    _outerGlassController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // العنوان
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'خيارات الرسمة المتقدمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // المحتوى
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFixedPositionSection(),
                    const SizedBox(height: 16),
                    if (widget.item.type == WindowDoorType.hinge) ...[
                      _buildHingeTypeSection(),
                      const SizedBox(height: 16),
                    ],
                    _buildPandaSection(),
                    const SizedBox(height: 16),
                    _buildWireSection(),
                    const SizedBox(height: 16),
                    _buildVentSection(),
                    const SizedBox(height: 16),
                    _buildGlassSection(),
                    const SizedBox(height: 16),
                    if (_options.glassType == GlassType.double) ...[
                      _buildDoubleGlassSection(),
                      const SizedBox(height: 16),
                    ],
                    _buildGeorgiaSection(),
                  ],
                ),
              ),
            ),

            // أزرار الحفظ والإلغاء
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _saveOptions,
                  child: const Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedPositionSection() {
    return _buildSection(
      title: 'الثوابت',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ثابت يمين
          _buildFixedPanelRow(
            title: 'ثابت يمين',
            isEnabled: _options.fixedPanels.hasRight,
            size: _options.fixedPanels.rightSize,
            divisions: _options.fixedPanels.rightDivisions,
            onEnabledChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(hasRight: value),
                );
              });
            },
            onSizeChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(rightSize: value),
                );
              });
            },
            onDivisionsChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(rightDivisions: value),
                );
              });
            },
          ),

          // ثابت شمال
          _buildFixedPanelRow(
            title: 'ثابت شمال',
            isEnabled: _options.fixedPanels.hasLeft,
            size: _options.fixedPanels.leftSize,
            divisions: _options.fixedPanels.leftDivisions,
            onEnabledChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(hasLeft: value),
                );
              });
            },
            onSizeChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(leftSize: value),
                );
              });
            },
            onDivisionsChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(leftDivisions: value),
                );
              });
            },
          ),

          // ثابت أعلى
          _buildFixedPanelRow(
            title: 'ثابت أعلى',
            isEnabled: _options.fixedPanels.hasTop,
            size: _options.fixedPanels.topSize,
            divisions: _options.fixedPanels.topDivisions,
            onEnabledChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(hasTop: value),
                );
              });
            },
            onSizeChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(topSize: value),
                );
              });
            },
            onDivisionsChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(topDivisions: value),
                );
              });
            },
          ),

          // ثابت أسفل
          _buildFixedPanelRow(
            title: 'ثابت أسفل',
            isEnabled: _options.fixedPanels.hasBottom,
            size: _options.fixedPanels.bottomSize,
            divisions: _options.fixedPanels.bottomDivisions,
            onEnabledChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(hasBottom: value),
                );
              });
            },
            onSizeChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(bottomSize: value),
                );
              });
            },
            onDivisionsChanged: (value) {
              setState(() {
                _options = _options.copyWith(
                  fixedPanels: _options.fixedPanels.copyWith(bottomDivisions: value),
                );
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHingeTypeSection() {
    return _buildSection(
      title: 'نوع الفتح',
      child: Wrap(
        spacing: 8,
        children: HingeOpenType.values.map((type) {
          return ChoiceChip(
            label: Text(type.arabicName),
            selected: _options.hingeOpenType == type,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _options = _options.copyWith(hingeOpenType: type);
                });
              }
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPandaSection() {
    return _buildSection(
      title: 'ضلفة باندا',
      child: SwitchListTile(
        title: const Text('إضافة ضلفة باندا'),
        value: _options.hasPanda,
        onChanged: (value) {
          setState(() {
            _options = _options.copyWith(hasPanda: value);
          });
        },
      ),
    );
  }

  Widget _buildWireSection() {
    return _buildSection(
      title: 'السلك',
      child: Wrap(
        spacing: 8,
        children: WireType.values.map((type) {
          return ChoiceChip(
            label: Text(type.arabicName),
            selected: _options.wireType == type,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _options = _options.copyWith(wireType: type);
                });
              }
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildVentSection() {
    return _buildSection(
      title: 'الشفاط',
      child: Wrap(
        spacing: 8,
        children: VentPosition.values.map((position) {
          return ChoiceChip(
            label: Text(position.arabicName),
            selected: _options.ventPosition == position,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _options = _options.copyWith(ventPosition: position);
                });
              }
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGlassSection() {
    return _buildSection(
      title: 'نوع الزجاج',
      child: Wrap(
        spacing: 8,
        children: GlassType.values.map((type) {
          return ChoiceChip(
            label: Text(type.arabicName),
            selected: _options.glassType == type,
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _options = _options.copyWith(glassType: type);
                });
              }
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDoubleGlassSection() {
    return _buildSection(
      title: 'تفاصيل الزجاج المزدوج',
      child: Column(
        children: [
          TextField(
            controller: _innerGlassController,
            decoration: const InputDecoration(
              labelText: 'نوع الزجاج الداخلي',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _outerGlassController,
            decoration: const InputDecoration(
              labelText: 'نوع الزجاج الخارجي',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text('نوع الفراغ: '),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: SpacingType.values.map((type) {
                    return ChoiceChip(
                      label: Text(type.arabicName),
                      selected: _options.spacingType == type,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _options = _options.copyWith(spacingType: type);
                          });
                        }
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGeorgiaSection() {
    return _buildSection(
      title: 'الجورجيا',
      child: Column(
        children: [
          Wrap(
            spacing: 8,
            children: GeorgiaType.values.map((type) {
              return ChoiceChip(
                label: Text(type.arabicName),
                selected: _options.georgiaType == type,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _options = _options.copyWith(georgiaType: type);
                    });
                  }
                },
              );
            }).toList(),
          ),
          if (_options.georgiaType != GeorgiaType.none) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: _options.georgiaCountWidth.toString(),
                    decoration: const InputDecoration(
                      labelText: 'عدد الجورجيا في العرض',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final count = int.tryParse(value) ?? 0;
                      setState(() {
                        _options = _options.copyWith(georgiaCountWidth: count);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    initialValue: _options.georgiaCountHeight.toString(),
                    decoration: const InputDecoration(
                      labelText: 'عدد الجورجيا في الارتفاع',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final count = int.tryParse(value) ?? 0;
                      setState(() {
                        _options = _options.copyWith(georgiaCountHeight: count);
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // بناء صف ثابت منظم
  Widget _buildFixedPanelRow({
    required String title,
    required bool isEnabled,
    required double size,
    required int divisions,
    required ValueChanged<bool> onEnabledChanged,
    required ValueChanged<double> onSizeChanged,
    required ValueChanged<int> onDivisionsChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: isEnabled ? Colors.blue.shade50 : Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع الـ Checkbox
          Row(
            children: [
              Checkbox(
                value: isEnabled,
                onChanged: (value) => onEnabledChanged(value ?? false),
                activeColor: Colors.blue,
              ),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isEnabled ? Colors.blue.shade700 : Colors.grey.shade600,
                ),
              ),
            ],
          ),

          // إعدادات المقاس والسؤاسات (تظهر فقط عند التفعيل)
          if (isEnabled) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                // المقاس
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'المقاس:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildNumberInput(
                        value: size,
                        onChanged: onSizeChanged,
                        min: 5.0,
                        max: 200.0,
                        step: 5.0,
                        isDouble: true,
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // عدد السؤاسات
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'عدد السؤاسات:',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      _buildNumberInput(
                        value: divisions.toDouble(),
                        onChanged: (value) => onDivisionsChanged(value.toInt()),
                        min: 0.0,
                        max: 10.0,
                        step: 1.0,
                        isDouble: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // بناء حقل إدخال رقمي مع أزرار الزيادة والنقصان وإمكانية الكتابة
  Widget _buildNumberInput({
    required double value,
    required Function(double) onChanged,
    required double min,
    required double max,
    required double step,
    required bool isDouble,
  }) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      child: Row(
        children: [
          // زر النقصان
          InkWell(
            onTap: () {
              final newValue = (value - step).clamp(min, max);
              onChanged(newValue);
            },
            child: Container(
              width: 32,
              height: 38,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(6),
                  bottomRight: Radius.circular(6),
                ),
                border: Border(
                  left: BorderSide(color: Colors.grey.shade400),
                ),
              ),
              child: const Icon(
                Icons.remove,
                size: 18,
                color: Colors.grey,
              ),
            ),
          ),

          // حقل الإدخال النصي
          Expanded(
            child: _NumberInputField(
              value: value,
              onChanged: onChanged,
              min: min,
              max: max,
              isDouble: isDouble,
            ),
          ),

          // زر الزيادة
          InkWell(
            onTap: () {
              final newValue = (value + step).clamp(min, max);
              onChanged(newValue);
            },
            child: Container(
              width: 32,
              height: 38,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  bottomLeft: Radius.circular(6),
                ),
                border: Border(
                  right: BorderSide(color: Colors.grey.shade400),
                ),
              ),
              child: const Icon(
                Icons.add,
                size: 18,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  void _saveOptions() {
    final updatedOptions = _options.copyWith(
      innerGlassType: _innerGlassController.text.isEmpty ? null : _innerGlassController.text,
      outerGlassType: _outerGlassController.text.isEmpty ? null : _outerGlassController.text,
    );

    final updatedItem = widget.item.copyWith(drawingOptions: updatedOptions);
    widget.onSave(updatedItem);
    Navigator.of(context).pop();
  }
}

// Widget منفصل لحقل الإدخال النصي مع إدارة صحيحة للـ controller
class _NumberInputField extends StatefulWidget {
  final double value;
  final Function(double) onChanged;
  final double min;
  final double max;
  final bool isDouble;

  const _NumberInputField({
    required this.value,
    required this.onChanged,
    required this.min,
    required this.max,
    required this.isDouble,
  });

  @override
  State<_NumberInputField> createState() => _NumberInputFieldState();
}

class _NumberInputFieldState extends State<_NumberInputField> {
  late TextEditingController _controller;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _updateControllerText();
  }

  @override
  void didUpdateWidget(_NumberInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value && !_isEditing) {
      _updateControllerText();
    }
  }

  void _updateControllerText() {
    _controller.text = widget.isDouble
        ? widget.value.toStringAsFixed(1)
        : widget.value.toInt().toString();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      textAlign: TextAlign.center,
      keyboardType: TextInputType.numberWithOptions(decimal: widget.isDouble),
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        isDense: true,
      ),
      onTap: () {
        _isEditing = true;
        _controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _controller.text.length,
        );
      },
      onChanged: (text) {
        if (text.isEmpty) return;

        final parsedValue = widget.isDouble
            ? double.tryParse(text)
            : double.tryParse(text)?.toInt().toDouble();

        if (parsedValue != null) {
          final clampedValue = parsedValue.clamp(widget.min, widget.max);
          widget.onChanged(clampedValue);
        }
      },
      onEditingComplete: () {
        _isEditing = false;
        _updateControllerText();
        FocusScope.of(context).unfocus();
      },
      onFieldSubmitted: (text) {
        _isEditing = false;
        _updateControllerText();
      },
    );
  }
}
