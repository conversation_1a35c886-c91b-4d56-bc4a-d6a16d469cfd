import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'dart:io';

/// خدمة قاعدة البيانات الموحدة
/// تحتوي على جميع الجداول من جميع الأقسام في قاعدة بيانات واحدة
class UnifiedDatabaseService {
  static final UnifiedDatabaseService _instance = UnifiedDatabaseService._internal();
  factory UnifiedDatabaseService() => _instance;
  UnifiedDatabaseService._internal();

  static Database? _database;
  static const String _databaseName = 'uptime_unified_database_v8.db';
  static const int _databaseVersion = 8;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Initialize FFI for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    debugPrint('🚀 إنشاء قاعدة البيانات الموحدة...');
    
    // إنشاء جداول الفواتير والعملاء
    await _createInvoiceTables(db);
    
    // إنشاء جداول الألومنيوم
    await _createAluminumTables(db);
    
    // إنشاء جداول uPVC
    await _createUpvcTables(db);
    
    // إنشاء جداول التقطيع والمشاريع
    await _createCuttingTables(db);
    
    // إنشاء جداول الخزينة
    await _createTreasuryTables(db);
    
    // إنشاء جداول إدارة المهام
    await _createTaskTables(db);
    
    debugPrint('✅ تم إنشاء قاعدة البيانات الموحدة بنجاح!');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // سيتم إضافة منطق التحديث هنا عند الحاجة
  }

  /// إنشاء جداول الفواتير والعملاء والموردين
  Future<void> _createInvoiceTables(Database db) async {
    debugPrint('📋 إنشاء جداول الفواتير والعملاء...');
    
    // جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT DEFAULT '',
        address TEXT DEFAULT '',
        email TEXT DEFAULT '',
        balance REAL DEFAULT 0.0,
        notes TEXT DEFAULT '',
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // جدول الموردين
    await db.execute('''
      CREATE TABLE suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT DEFAULT '',
        address TEXT DEFAULT '',
        email TEXT DEFAULT '',
        company TEXT DEFAULT '',
        balance REAL DEFAULT 0.0,
        notes TEXT DEFAULT '',
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // جدول الفواتير
    await db.execute('''
      CREATE TABLE invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoiceNumber TEXT NOT NULL UNIQUE,
        date TEXT NOT NULL,
        supplierOrCustomerName TEXT NOT NULL,
        type TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        discount REAL DEFAULT 0.0,
        expenses REAL DEFAULT 0.0,
        netAmount REAL NOT NULL,
        notes TEXT DEFAULT '',
        treasuryId INTEGER,
        isPaid INTEGER DEFAULT 0,
        treasuryTransactionId INTEGER,
        created_at INTEGER,
        updated_at INTEGER
      )
    ''');

    // جدول عناصر الفواتير
    await db.execute('''
      CREATE TABLE invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoiceId INTEGER NOT NULL,
        itemName TEXT NOT NULL,
        unit TEXT NOT NULL,
        quantity REAL NOT NULL,
        price REAL NOT NULL,
        discount REAL DEFAULT 0.0,
        total REAL NOT NULL,
        FOREIGN KEY (invoiceId) REFERENCES invoices (id) ON DELETE CASCADE
      )
    ''');

    debugPrint('✅ تم إنشاء جداول الفواتير والعملاء');
  }

  /// إنشاء جداول الألومنيوم
  Future<void> _createAluminumTables(Database db) async {
    debugPrint('🔧 إنشاء جداول الألومنيوم...');

    // جدول مجموعات القطاعات
    await db.execute('''
      CREATE TABLE profile_series (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        description TEXT,
        image_path BLOB,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // إنشاء فهارس لجدول مجموعات القطاعات
    await db.execute('CREATE INDEX idx_series_type ON profile_series (type)');
    await db.execute('CREATE INDEX idx_series_code ON profile_series (code)');
    await db.execute('CREATE INDEX idx_series_active ON profile_series (is_active)');

    // جدول قطاعات الألومنيوم
    await db.execute('''
      CREATE TABLE aluminum_profiles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        series_id INTEGER,
        width REAL,
        height REAL,
        thickness REAL,
        weight REAL,
        color TEXT,
        description TEXT,
        lip_type TEXT,
        lip_thickness REAL,
        with_baketa INTEGER,
        with_dalfa INTEGER,
        image_path BLOB,
        price_per_meter REAL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES profile_series (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء جداول عروض أسعار الألومنيوم
    await _createAluminumQuotationTables(db);

    // إنشاء جداول تصاميم الألومنيوم
    await _createAluminumDesignTables(db);

    debugPrint('✅ تم إنشاء جداول الألومنيوم الأساسية');
  }

  /// إنشاء جداول عروض أسعار الألومنيوم
  Future<void> _createAluminumQuotationTables(Database db) async {
    // جدول عروض أسعار الألومنيوم
    await db.execute('''
      CREATE TABLE aluminum_quotations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_number TEXT NOT NULL UNIQUE,
        quotation_date INTEGER NOT NULL,
        client_name TEXT NOT NULL,
        client_phone TEXT,
        client_address TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول عناصر عروض أسعار الألومنيوم
    await db.execute('''
      CREATE TABLE aluminum_quotation_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        sash_count TEXT,
        track_count TEXT,
        width REAL NOT NULL,
        height REAL NOT NULL,
        quantity INTEGER NOT NULL,
        notes TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (quotation_id) REFERENCES aluminum_quotations (id) ON DELETE CASCADE
      )
    ''');
  }

  /// إنشاء جداول تصاميم الألومنيوم
  Future<void> _createAluminumDesignTables(Database db) async {
    // جدول تصاميم المفصلي
    await db.execute('''
      CREATE TABLE hinge_designs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_on_halaf REAL,
        marad_dalfa_complete REAL,
        marad_dalfa_with_kaab REAL,
        marad_between_dalfa REAL,
        dalfa_from_ground REAL,
        dalfa_glass REAL,
        fixed_glass REAL,
        moving_silk REAL,
        fixed_silk REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES profile_series (id) ON DELETE CASCADE
      )
    ''');

    // جدول تصاميم السحاب
    await db.execute('''
      CREATE TABLE sliding_designs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_count INTEGER,
        dalfa_method INTEGER,
        dalfa_width_plus REAL,
        dalfa_height_minus REAL,
        skineh_height_minus REAL,
        silk_width_plus REAL,
        silk_height_minus REAL,
        glass_width_minus REAL,
        glass_height_minus REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES profile_series (id) ON DELETE CASCADE
      )
    ''');
  }

  /// إنشاء جداول uPVC
  Future<void> _createUpvcTables(Database db) async {
    debugPrint('🔩 إنشاء جداول uPVC...');

    // جدول مجموعات قطاعات uPVC
    await db.execute('''
      CREATE TABLE upvc_profile_series (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول قطاعات uPVC
    await db.execute('''
      CREATE TABLE upvc_profiles (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        category TEXT NOT NULL,
        series_id INTEGER,
        width REAL,
        height REAL,
        thickness REAL,
        weight REAL,
        color TEXT,
        description TEXT,
        lip_type TEXT,
        lip_thickness REAL,
        with_baketa INTEGER,
        with_dalfa INTEGER,
        image_path BLOB,
        price_per_meter REAL,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES upvc_profile_series (id) ON DELETE CASCADE
      )
    ''');

    // جدول عروض أسعار uPVC
    await db.execute('''
      CREATE TABLE upvc_quotations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_number TEXT NOT NULL UNIQUE,
        quotation_date INTEGER NOT NULL,
        client_name TEXT NOT NULL,
        client_phone TEXT,
        client_address TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول عناصر عروض أسعار uPVC
    await db.execute('''
      CREATE TABLE upvc_quotation_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quotation_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        sash_count TEXT,
        track_count TEXT,
        width REAL NOT NULL,
        height REAL NOT NULL,
        quantity INTEGER NOT NULL,
        notes TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (quotation_id) REFERENCES upvc_quotations (id) ON DELETE CASCADE
      )
    ''');

    // جدول تصاميم المفصلي uPVC
    await db.execute('''
      CREATE TABLE upvc_hinge_designs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_on_halaf REAL,
        marad_dalfa_complete REAL,
        marad_dalfa_with_kaab REAL,
        marad_between_dalfa REAL,
        dalfa_from_ground REAL,
        dalfa_glass REAL,
        fixed_glass REAL,
        moving_silk REAL,
        fixed_silk REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES upvc_profile_series (id) ON DELETE CASCADE
      )
    ''');

    // جدول تصاميم السحاب uPVC
    await db.execute('''
      CREATE TABLE upvc_sliding_designs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_count INTEGER NOT NULL,
        dalfa_method INTEGER NOT NULL,
        dalfa_width_plus REAL,
        dalfa_height_minus REAL,
        skineh_height_minus REAL,
        silk_width_plus REAL,
        silk_height_minus REAL,
        glass_width_minus REAL,
        glass_height_minus REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (series_id) REFERENCES upvc_profile_series (id) ON DELETE CASCADE
      )
    ''');

    debugPrint('✅ تم إنشاء جداول uPVC');
  }

  /// إنشاء جداول التقطيع والمشاريع
  Future<void> _createCuttingTables(Database db) async {
    debugPrint('✂️ إنشاء جداول التقطيع والمشاريع...');

    // جدول مشاريع التقطيع
    await db.execute('''
      CREATE TABLE cutting_projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_number TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        date TEXT NOT NULL,
        delivery_date TEXT,
        phone TEXT,
        address TEXT,
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول عناصر الطلبات
    await db.execute('''
      CREATE TABLE order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        item_number TEXT NOT NULL,
        item_name TEXT NOT NULL,
        item_type TEXT NOT NULL,
        required_boards INTEGER DEFAULT 0,
        unit_price REAL DEFAULT 0.0,
        total_amount REAL DEFAULT 0.0,
        discount_percent REAL DEFAULT 0.0,
        discount_amount REAL DEFAULT 0.0,
        final_amount REAL DEFAULT 0.0,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (project_id) REFERENCES cutting_projects (id) ON DELETE CASCADE
      )
    ''');

    // جدول قياسات التقطيع
    await db.execute('''
      CREATE TABLE cutting_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_item_id INTEGER NOT NULL,
        piece_size TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        type TEXT,
        number TEXT,
        stick_length REAL DEFAULT 300.0,
        saw_blade_thickness REAL DEFAULT 0.5,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (order_item_id) REFERENCES order_items (id) ON DELETE CASCADE
      )
    ''');

    // جدول قياسات الألواح
    await db.execute('''
      CREATE TABLE panel_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_item_id INTEGER NOT NULL,
        piece_width REAL NOT NULL,
        piece_height REAL NOT NULL,
        quantity INTEGER NOT NULL,
        type TEXT,
        number TEXT,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (order_item_id) REFERENCES order_items (id) ON DELETE CASCADE
      )
    ''');

    debugPrint('✅ تم إنشاء جداول التقطيع والمشاريع');
  }

  /// إنشاء جداول الخزينة
  Future<void> _createTreasuryTables(Database db) async {
    debugPrint('💰 إنشاء جداول الخزينة...');

    // جدول الخزائن
    await db.execute('''
      CREATE TABLE treasuries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        previous_balance REAL DEFAULT 0.0,
        current_balance REAL DEFAULT 0.0,
        date TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )
    ''');

    // جدول معاملات الخزينة
    await db.execute('''
      CREATE TABLE treasury_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        treasury_id INTEGER NOT NULL,
        description TEXT NOT NULL,
        income REAL DEFAULT 0.0,
        expenses REAL DEFAULT 0.0,
        notes TEXT NOT NULL,
        date TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        invoice_id INTEGER,
        invoice_number TEXT,
        FOREIGN KEY (treasury_id) REFERENCES treasuries (id) ON DELETE CASCADE
      )
    ''');

    debugPrint('✅ تم إنشاء جداول الخزينة');
  }

  /// إنشاء جداول إدارة المهام
  Future<void> _createTaskTables(Database db) async {
    debugPrint('📋 إنشاء جداول إدارة المهام...');

    // جدول فئات المهام
    await db.execute('''
      CREATE TABLE task_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        color INTEGER NOT NULL,
        icon INTEGER NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول المهام
    await db.execute('''
      CREATE TABLE tasks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        category_id INTEGER NOT NULL,
        priority INTEGER NOT NULL,
        status INTEGER NOT NULL,
        due_date INTEGER,
        notes TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        FOREIGN KEY (category_id) REFERENCES task_categories (id) ON DELETE CASCADE
      )
    ''');

    debugPrint('✅ تم إنشاء جداول إدارة المهام');
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }

  /// حذف قاعدة البيانات وإعادة إنشائها
  Future<void> recreateDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }

    String path = join(await getDatabasesPath(), _databaseName);
    await deleteDatabase(path);

    // إعادة إنشاء قاعدة البيانات
    _database = await _initDatabase();
  }

  /// الحصول على معلومات قاعدة البيانات
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;

    // الحصول على قائمة الجداول
    final tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );

    final tableNames = tables.map((table) => table['name'] as String).toList();

    // حساب عدد السجلات في كل جدول
    final tableCounts = <String, int>{};
    for (final tableName in tableNames) {
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
      tableCounts[tableName] = result.first['count'] as int;
    }

    return {
      'database_name': _databaseName,
      'version': _databaseVersion,
      'tables_count': tableNames.length,
      'tables': tableNames,
      'table_counts': tableCounts,
    };
  }

  // ==================== خدمات الفواتير ====================

  /// إدراج فاتورة جديدة
  Future<int> insertInvoice(Map<String, dynamic> invoice) async {
    final db = await database;
    return await db.insert('invoices', invoice);
  }

  /// الحصول على جميع الفواتير
  Future<List<Map<String, dynamic>>> getAllInvoices() async {
    final db = await database;
    return await db.query('invoices', orderBy: 'date DESC');
  }

  /// تحديث فاتورة
  Future<int> updateInvoice(int id, Map<String, dynamic> invoice) async {
    final db = await database;
    return await db.update('invoices', invoice, where: 'id = ?', whereArgs: [id]);
  }

  /// حذف فاتورة
  Future<int> deleteInvoice(int id) async {
    final db = await database;
    return await db.delete('invoices', where: 'id = ?', whereArgs: [id]);
  }

  /// إدراج عنصر فاتورة
  Future<int> insertInvoiceItem(Map<String, dynamic> item) async {
    final db = await database;
    return await db.insert('invoice_items', item);
  }

  /// الحصول على عناصر فاتورة
  Future<List<Map<String, dynamic>>> getInvoiceItems(int invoiceId) async {
    final db = await database;
    return await db.query('invoice_items', where: 'invoiceId = ?', whereArgs: [invoiceId]);
  }

  /// إدراج عميل
  Future<int> insertCustomer(Map<String, dynamic> customer) async {
    final db = await database;
    return await db.insert('customers', customer);
  }

  /// الحصول على جميع العملاء
  Future<List<Map<String, dynamic>>> getAllCustomers() async {
    final db = await database;
    return await db.query('customers', orderBy: 'name');
  }

  /// إدراج مورد
  Future<int> insertSupplier(Map<String, dynamic> supplier) async {
    final db = await database;
    return await db.insert('suppliers', supplier);
  }

  /// الحصول على جميع الموردين
  Future<List<Map<String, dynamic>>> getAllSuppliers() async {
    final db = await database;
    return await db.query('suppliers', orderBy: 'name');
  }

  // ==================== خدمات الألومنيوم ====================

  /// إدراج مجموعة قطاعات
  Future<int> insertProfileSeries(Map<String, dynamic> series) async {
    final db = await database;
    return await db.insert('profile_series', series);
  }

  /// الحصول على جميع مجموعات القطاعات
  Future<List<Map<String, dynamic>>> getAllProfileSeries() async {
    final db = await database;
    return await db.query('profile_series', where: 'is_active = ?', whereArgs: [1]);
  }

  /// إدراج قطاع ألومنيوم
  Future<int> insertAluminumProfile(Map<String, dynamic> profile) async {
    final db = await database;
    return await db.insert('aluminum_profiles', profile);
  }

  /// الحصول على جميع قطاعات الألومنيوم
  Future<List<Map<String, dynamic>>> getAllAluminumProfiles() async {
    final db = await database;
    return await db.query('aluminum_profiles', where: 'is_active = ?', whereArgs: [1]);
  }

  /// إدراج عرض سعر ألومنيوم
  Future<int> insertAluminumQuotation(Map<String, dynamic> quotation) async {
    final db = await database;
    return await db.insert('aluminum_quotations', quotation);
  }

  /// الحصول على جميع عروض أسعار الألومنيوم
  Future<List<Map<String, dynamic>>> getAllAluminumQuotations() async {
    final db = await database;
    return await db.query('aluminum_quotations', orderBy: 'quotation_date DESC');
  }

  /// تحديث عرض سعر ألومنيوم
  Future<int> updateAluminumQuotation(int id, Map<String, dynamic> quotation) async {
    final db = await database;
    return await db.update('aluminum_quotations', quotation, where: 'id = ?', whereArgs: [id]);
  }

  /// حذف عرض سعر ألومنيوم
  Future<int> deleteAluminumQuotation(int id) async {
    final db = await database;
    return await db.delete('aluminum_quotations', where: 'id = ?', whereArgs: [id]);
  }

  /// الحصول على عناصر عرض سعر ألومنيوم
  Future<List<Map<String, dynamic>>> getAluminumQuotationItems(int quotationId) async {
    final db = await database;
    return await db.query('aluminum_quotation_items', where: 'quotation_id = ?', whereArgs: [quotationId]);
  }

  /// إدراج عنصر عرض سعر ألومنيوم
  Future<int> insertAluminumQuotationItem(Map<String, dynamic> item) async {
    final db = await database;
    return await db.insert('aluminum_quotation_items', item);
  }

  /// تحديث عنصر عرض سعر ألومنيوم
  Future<int> updateAluminumQuotationItem(int id, Map<String, dynamic> item) async {
    final db = await database;
    return await db.update('aluminum_quotation_items', item, where: 'id = ?', whereArgs: [id]);
  }

  /// حذف عنصر عرض سعر ألومنيوم
  Future<int> deleteAluminumQuotationItem(int id) async {
    final db = await database;
    return await db.delete('aluminum_quotation_items', where: 'id = ?', whereArgs: [id]);
  }

  /// الحصول على قطاعات الألومنيوم حسب السلسلة
  Future<List<Map<String, dynamic>>> getAluminumProfilesBySeries(String seriesId) async {
    final db = await database;
    return await db.query('aluminum_profiles', where: 'series_id = ? AND is_active = ?', whereArgs: [seriesId, 1]);
  }

  /// الحصول على سلاسل قطاعات الألومنيوم
  Future<List<Map<String, dynamic>>> getAluminumProfileSeries() async {
    final db = await database;
    return await db.query('profile_series', where: 'is_active = ?', whereArgs: [1]);
  }

  /// تحديث قطاع ألومنيوم
  Future<int> updateAluminumProfile(int id, Map<String, dynamic> profile) async {
    final db = await database;
    return await db.update('aluminum_profiles', profile, where: 'id = ?', whereArgs: [id]);
  }

  /// حذف قطاع ألومنيوم
  Future<int> deleteAluminumProfile(int id) async {
    final db = await database;
    return await db.delete('aluminum_profiles', where: 'id = ?', whereArgs: [id]);
  }

  /// إدراج تصميم مفصلي
  Future<int> insertHingeDesign(Map<String, dynamic> design) async {
    final db = await database;
    return await db.insert('hinge_designs', design);
  }

  /// إدراج تصميم سحاب
  Future<int> insertSlidingDesign(Map<String, dynamic> design) async {
    final db = await database;
    return await db.insert('sliding_designs', design);
  }

  // ==================== خدمات uPVC ====================

  /// إدراج مجموعة قطاعات uPVC
  Future<int> insertUpvcProfileSeries(Map<String, dynamic> series) async {
    final db = await database;
    return await db.insert('upvc_profile_series', series);
  }

  /// إدراج قطاع uPVC
  Future<int> insertUpvcProfile(Map<String, dynamic> profile) async {
    final db = await database;
    return await db.insert('upvc_profiles', profile);
  }

  /// الحصول على جميع قطاعات uPVC
  Future<List<Map<String, dynamic>>> getAllUpvcProfiles() async {
    final db = await database;
    return await db.query('upvc_profiles', where: 'is_active = ?', whereArgs: [1]);
  }

  // ==================== خدمات التقطيع ====================

  /// إدراج مشروع تقطيع
  Future<int> insertCuttingProject(Map<String, dynamic> project) async {
    final db = await database;
    return await db.insert('cutting_projects', project);
  }

  /// الحصول على جميع مشاريع التقطيع
  Future<List<Map<String, dynamic>>> getAllCuttingProjects() async {
    final db = await database;
    return await db.query('cutting_projects', orderBy: 'date DESC');
  }

  /// إدراج عنصر طلب
  Future<int> insertOrderItem(Map<String, dynamic> item) async {
    final db = await database;
    return await db.insert('order_items', item);
  }

  /// الحصول على عناصر مشروع
  Future<List<Map<String, dynamic>>> getProjectItems(int projectId) async {
    final db = await database;
    return await db.query('order_items', where: 'project_id = ?', whereArgs: [projectId]);
  }

  // ==================== خدمات الخزينة ====================

  /// إدراج خزينة
  Future<int> insertTreasury(Map<String, dynamic> treasury) async {
    final db = await database;
    return await db.insert('treasuries', treasury);
  }

  /// الحصول على جميع الخزائن
  Future<List<Map<String, dynamic>>> getAllTreasuries() async {
    final db = await database;
    return await db.query('treasuries');
  }

  /// إدراج معاملة خزينة
  Future<int> insertTreasuryTransaction(Map<String, dynamic> transaction) async {
    final db = await database;
    return await db.insert('treasury_transactions', transaction);
  }

  /// الحصول على معاملات خزينة
  Future<List<Map<String, dynamic>>> getTreasuryTransactions(int treasuryId) async {
    final db = await database;
    return await db.query('treasury_transactions',
      where: 'treasury_id = ?',
      whereArgs: [treasuryId],
      orderBy: 'date DESC'
    );
  }

  // ==================== خدمات المهام ====================

  /// إدراج فئة مهام
  Future<int> insertTaskCategory(Map<String, dynamic> category) async {
    final db = await database;
    return await db.insert('task_categories', category);
  }

  /// الحصول على جميع فئات المهام
  Future<List<Map<String, dynamic>>> getAllTaskCategories() async {
    final db = await database;
    return await db.query('task_categories');
  }

  /// إدراج مهمة
  Future<int> insertTask(Map<String, dynamic> task) async {
    final db = await database;
    return await db.insert('tasks', task);
  }

  /// الحصول على جميع المهام
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    final db = await database;
    return await db.query('tasks', orderBy: 'created_at DESC');
  }

  /// تحديث مهمة
  Future<int> updateTask(int id, Map<String, dynamic> task) async {
    final db = await database;
    return await db.update('tasks', task, where: 'id = ?', whereArgs: [id]);
  }

  /// حذف مهمة
  Future<int> deleteTask(int id) async {
    final db = await database;
    return await db.delete('tasks', where: 'id = ?', whereArgs: [id]);
  }
}
