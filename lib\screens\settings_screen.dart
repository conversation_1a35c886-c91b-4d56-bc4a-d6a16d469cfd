import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../services/settings_service.dart';
import 'database_config_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _currentCurrency = 'ريال';
  String _currentCurrencySymbol = 'ر.س';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final currency = await SettingsService.instance.getCurrency();
      final symbol = await SettingsService.instance.getCurrencySymbol();
      setState(() {
        _currentCurrency = currency;
        _currentCurrencySymbol = symbol;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text(localizations.settings),
          backgroundColor: theme.colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.settings),
        backgroundColor: theme.colorScheme.inversePrimary,
        elevation: 2,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // قسم الإعدادات العامة
          _buildSectionHeader('الإعدادات العامة', Icons.settings),
          const SizedBox(height: 8),

          // إعدادات العملة
          Card(
            child: ListTile(
              leading: const Icon(Icons.attach_money, color: Colors.green),
              title: const Text('العملة'),
              subtitle: Text('العملة الحالية: $_currentCurrency ($_currentCurrencySymbol)'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showCurrencyDialog(),
            ),
          ),
          const SizedBox(height: 8),

          // إعدادات قاعدة البيانات
          Card(
            child: ListTile(
              leading: const Icon(Icons.storage, color: Colors.blue),
              title: const Text('قاعدة البيانات'),
              subtitle: const Text('إعدادات الاتصال وقاعدة البيانات'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DatabaseConfigScreen(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),

          // معلومات التطبيق
          Card(
            child: ListTile(
              leading: const Icon(Icons.info, color: Colors.orange),
              title: const Text('معلومات التطبيق'),
              subtitle: const Text('إصدار 2.0 - قاعدة بيانات موحدة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showAppInfoDialog(),
            ),
          ),

          // قسم النظام والصيانة
          const SizedBox(height: 24),
          _buildSectionHeader('النظام والصيانة', Icons.build),
          const SizedBox(height: 8),

          // حالة قاعدة البيانات
          Card(
            child: ListTile(
              leading: const Icon(Icons.storage, color: Colors.green),
              title: const Text('حالة قاعدة البيانات'),
              subtitle: const Text('قاعدة بيانات موحدة - تعمل بشكل مثالي'),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'متصل',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),

          // المزامنة
          Card(
            child: ListTile(
              leading: const Icon(Icons.sync, color: Colors.blue),
              title: const Text('المزامنة'),
              subtitle: const Text('جميع البيانات محفوظة في قاعدة البيانات الموحدة'),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'محدث',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),

          // النسخ الاحتياطي
          Card(
            child: ListTile(
              leading: const Icon(Icons.backup, color: Colors.purple),
              title: const Text('النسخ الاحتياطي'),
              subtitle: const Text('إنشاء نسخة احتياطية من البيانات'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupDialog(),
            ),
          ),

          // قسم المساعدة والدعم
          const SizedBox(height: 24),
          _buildSectionHeader('المساعدة والدعم', Icons.help),
          const SizedBox(height: 8),

          // المساعدة
          Card(
            child: ListTile(
              leading: const Icon(Icons.help_outline, color: Colors.teal),
              title: const Text('المساعدة'),
              subtitle: const Text('دليل الاستخدام والدعم الفني'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showHelpDialog(),
            ),
          ),

          // حالة التطبيق
          const SizedBox(height: 32),
          Center(
            child: Column(
              children: [
                const Icon(
                  Icons.check_circle,
                  size: 64,
                  color: Colors.green,
                ),
                const SizedBox(height: 16),
                Text(
                  'التطبيق يعمل بشكل مثالي!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'جميع الخدمات متاحة وتعمل بكفاءة',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنوان القسم
  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  // نافذة حوار العملة
  Future<void> _showCurrencyDialog() async {
    final currencies = SettingsService.getPredefinedCurrencies();

    final selected = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر العملة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: currencies.length,
            itemBuilder: (context, index) {
              final currency = currencies[index];
              final isSelected = currency['name'] == _currentCurrency;

              return ListTile(
                leading: Icon(
                  Icons.attach_money,
                  color: isSelected ? Colors.green : Colors.grey,
                ),
                title: Text(currency['name']!),
                subtitle: Text(currency['symbol']!),
                trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
                onTap: () => Navigator.pop(context, currency),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (selected != null) {
      await SettingsService.instance.setCurrency(selected['name']!);
      await SettingsService.instance.setCurrencySymbol(selected['symbol']!);
      setState(() {
        _currentCurrency = selected['name']!;
        _currentCurrencySymbol = selected['symbol']!;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تغيير العملة إلى ${selected['name']}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // نافذة حوار معلومات التطبيق
  void _showAppInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Text('معلومات التطبيق'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تطبيق Uptime Smart Assist',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('الإصدار: 2.0'),
              Text('تاريخ الإصدار: ديسمبر 2024'),
              SizedBox(height: 16),
              Text(
                'الميزات الجديدة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('• قاعدة بيانات موحدة'),
              Text('• تحسين الأداء'),
              Text('• واجهة محدثة'),
              Text('• إدارة شاملة للقطاعات'),
              Text('• نظام محاسبة متطور'),
              SizedBox(height: 16),
              Text(
                'جميع الخدمات تعمل بكفاءة عالية',
                style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // نافذة حوار النسخ الاحتياطي
  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.backup, color: Colors.purple),
            SizedBox(width: 8),
            Text('النسخ الاحتياطي'),
          ],
        ),
        content: const Text(
          'النسخ الاحتياطي يتم تلقائياً:\n\n'
          '• قاعدة البيانات محفوظة في:\n'
          '  .dart_tool/sqflite_common_ffi/databases/\n\n'
          '• يمكنك نسخ ملف قاعدة البيانات يدوياً\n'
          '• جميع البيانات محفوظة بأمان\n'
          '• التطبيق يحفظ البيانات تلقائياً',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // نافذة حوار المساعدة
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help, color: Colors.teal),
            SizedBox(width: 8),
            Text('المساعدة'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'دليل الاستخدام السريع:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 12),
              Text('1. إدارة القطاعات:'),
              Text('   • اذهب إلى قسم الألومنيوم'),
              Text('   • اختر نوع القطاع (مفصلي/سحاب)'),
              Text('   • أضف مجموعات وقطاعات جديدة'),
              SizedBox(height: 8),
              Text('2. المحاسبة:'),
              Text('   • إدارة العملاء والموردين'),
              Text('   • إنشاء الفواتير'),
              Text('   • متابعة الحسابات'),
              SizedBox(height: 8),
              Text('3. الخزينة:'),
              Text('   • تسجيل المعاملات المالية'),
              Text('   • متابعة الأرصدة'),
              SizedBox(height: 8),
              Text('4. التقطيع:'),
              Text('   • تحسين استخدام المواد'),
              Text('   • حساب الفاقد'),
              SizedBox(height: 12),
              Text(
                'للدعم الفني: اتصل بفريق التطوير',
                style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
