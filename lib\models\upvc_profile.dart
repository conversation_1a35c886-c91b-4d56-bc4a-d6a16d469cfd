// نماذج قطاعات uPVC

enum UpvcProfileType {
  hinge, // مفصلي
  sliding, // سحاب
}

enum UpvcProfileCategory {
  halaf, // حلق
  bar, // بار
  dalfa, // ضلفة
  marad, // مرد
  baketa, // باكتة
  soas, // سؤاس
  dalfaSilk, // ضلفة سلك
  olba, // علبة
  filta, // فلتة
  skineh, // سكينة (للسحاب فقط)
  anf, // الانف (للسحاب فقط)
}

extension UpvcProfileTypeExtension on UpvcProfileType {
  String get arabicName {
    switch (this) {
      case UpvcProfileType.hinge:
        return 'مفصلي';
      case UpvcProfileType.sliding:
        return 'سحاب';
    }
  }

  String get englishName {
    switch (this) {
      case UpvcProfileType.hinge:
        return 'hinge';
      case UpvcProfileType.sliding:
        return 'sliding';
    }
  }
}

extension UpvcProfileCategoryExtension on UpvcProfileCategory {
  String get arabicName {
    switch (this) {
      case UpvcProfileCategory.halaf:
        return 'حلق';
      case UpvcProfileCategory.bar:
        return 'بار';
      case UpvcProfileCategory.dalfa:
        return 'ضلفة';
      case UpvcProfileCategory.marad:
        return 'مرد';
      case UpvcProfileCategory.baketa:
        return 'باكتة';
      case UpvcProfileCategory.soas:
        return 'سؤاس';
      case UpvcProfileCategory.dalfaSilk:
        return 'ضلفة سلك';
      case UpvcProfileCategory.olba:
        return 'علبة';
      case UpvcProfileCategory.filta:
        return 'فلتة';
      case UpvcProfileCategory.skineh:
        return 'سكينة';
      case UpvcProfileCategory.anf:
        return 'الانف';
    }
  }

  String get englishName {
    switch (this) {
      case UpvcProfileCategory.halaf:
        return 'halaf';
      case UpvcProfileCategory.bar:
        return 'bar';
      case UpvcProfileCategory.dalfa:
        return 'dalfa';
      case UpvcProfileCategory.marad:
        return 'marad';
      case UpvcProfileCategory.baketa:
        return 'baketa';
      case UpvcProfileCategory.soas:
        return 'soas';
      case UpvcProfileCategory.dalfaSilk:
        return 'dalfa_silk';
      case UpvcProfileCategory.olba:
        return 'olba';
      case UpvcProfileCategory.filta:
        return 'filta';
      case UpvcProfileCategory.skineh:
        return 'skineh';
      case UpvcProfileCategory.anf:
        return 'anf';
    }
  }

  // الفئات المتاحة لكل نوع
  static List<UpvcProfileCategory> getHingeCategories() {
    return [
      UpvcProfileCategory.halaf,
      UpvcProfileCategory.bar,
      UpvcProfileCategory.dalfa,
      UpvcProfileCategory.marad,
      UpvcProfileCategory.baketa,
      UpvcProfileCategory.soas,
      UpvcProfileCategory.dalfaSilk,
      UpvcProfileCategory.olba,
      UpvcProfileCategory.filta,
    ];
  }

  static List<UpvcProfileCategory> getSlidingCategories() {
    return [
      UpvcProfileCategory.halaf,
      UpvcProfileCategory.bar,
      UpvcProfileCategory.dalfa,
      UpvcProfileCategory.marad,
      UpvcProfileCategory.baketa,
      UpvcProfileCategory.soas,
      UpvcProfileCategory.dalfaSilk,
      UpvcProfileCategory.olba,
      UpvcProfileCategory.filta,
      UpvcProfileCategory.skineh,
      UpvcProfileCategory.anf,
    ];
  }

  // الفئات التي تحتاج نوع الشفة وسمك الشفة
  bool get needsLipFields {
    switch (this) {
      case UpvcProfileCategory.halaf: // حلق المفصلي
        return true;
      case UpvcProfileCategory.dalfa: // ضلفة المفصلي والسحاب
        return true;
      case UpvcProfileCategory.bar: // بار - لا يحتاج
      case UpvcProfileCategory.marad: // مرد - لا يحتاج
      case UpvcProfileCategory.baketa: // باكتة - لا يحتاج
      case UpvcProfileCategory.soas: // سؤاس - لا يحتاج
      case UpvcProfileCategory.dalfaSilk: // ضلفة سلك - لا يحتاج
      case UpvcProfileCategory.olba: // علبة - لا يحتاج
      case UpvcProfileCategory.filta: // فلتة - لا يحتاج
      case UpvcProfileCategory.skineh: // سكينة - لا يحتاج
      case UpvcProfileCategory.anf: // الانف - لا يحتاج
        return false;
    }
  }

  // الفئات التي تحتاج حقل "بالباكتة"
  bool get needsBaketaField {
    switch (this) {
      case UpvcProfileCategory.dalfa: // ضلفة المفصلي فقط
        return true;
      default:
        return false;
    }
  }

  // الفئات التي تحتاج حقل "بالضلفة"
  bool get needsDalfaField {
    switch (this) {
      case UpvcProfileCategory.skineh: // سكينة السحاب فقط
        return true;
      default:
        return false;
    }
  }
}

class UpvcProfile {
  final int? id;
  final String name;
  final String code;
  final UpvcProfileType type;
  final UpvcProfileCategory category;
  final int? seriesId; // ربط بمجموعة القطاعات
  final double? width;
  final double? height;
  final double? thickness;
  final double? weight; // kg per meter
  final String color;
  final String description;
  final String? lipType; // نوع الشفة (للحلق والضلفة فقط)
  final double? lipThickness; // سمك الشفة (للحلق والضلفة فقط)
  final bool? withBaketa; // بالباكتة (للضلفة المفصلي فقط)
  final bool? withDalfa; // بالضلفة (للسكينة السحاب فقط)
  final String? imagePath;
  final double? pricePerMeter;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  UpvcProfile({
    this.id,
    required this.name,
    required this.code,
    required this.type,
    required this.category,
    this.seriesId,
    this.width,
    this.height,
    this.thickness,
    this.weight,
    this.color = '',
    this.description = '',
    this.lipType,
    this.lipThickness,
    this.withBaketa,
    this.withDalfa,
    this.imagePath,
    this.pricePerMeter,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'type': type.englishName,
      'category': category.englishName,
      'series_id': seriesId,
      'width': width,
      'height': height,
      'thickness': thickness,
      'weight': weight,
      'color': color,
      'description': description,
      'lip_type': lipType,
      'lip_thickness': lipThickness,
      'with_baketa': withBaketa != null ? (withBaketa! ? 1 : 0) : null,
      'with_dalfa': withDalfa != null ? (withDalfa! ? 1 : 0) : null,
      'image_path': imagePath,
      'price_per_meter': pricePerMeter,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcProfile.fromMap(Map<String, dynamic> map) {
    return UpvcProfile(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      code: map['code'] ?? '',
      type: UpvcProfileType.values.firstWhere(
        (e) => e.englishName == map['type'],
        orElse: () => UpvcProfileType.hinge,
      ),
      category: UpvcProfileCategory.values.firstWhere(
        (e) => e.englishName == map['category'],
        orElse: () => UpvcProfileCategory.halaf,
      ),
      seriesId: map['series_id']?.toInt(),
      width: map['width']?.toDouble(),
      height: map['height']?.toDouble(),
      thickness: map['thickness']?.toDouble(),
      weight: map['weight']?.toDouble(),
      color: map['color'] ?? '',
      description: map['description'] ?? '',
      lipType: map['lip_type'],
      lipThickness: map['lip_thickness']?.toDouble(),
      withBaketa: map['with_baketa'] != null ? (map['with_baketa'] == 1) : null,
      withDalfa: map['with_dalfa'] != null ? (map['with_dalfa'] == 1) : null,
      imagePath: map['image_path'],
      pricePerMeter: map['price_per_meter']?.toDouble(),
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  UpvcProfile copyWith({
    int? id,
    String? name,
    String? code,
    UpvcProfileType? type,
    UpvcProfileCategory? category,
    int? seriesId,
    double? width,
    double? height,
    double? thickness,
    double? weight,
    String? color,
    String? description,
    String? lipType,
    double? lipThickness,
    bool? withBaketa,
    bool? withDalfa,
    String? imagePath,
    double? pricePerMeter,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UpvcProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      type: type ?? this.type,
      category: category ?? this.category,
      seriesId: seriesId ?? this.seriesId,
      width: width ?? this.width,
      height: height ?? this.height,
      thickness: thickness ?? this.thickness,
      weight: weight ?? this.weight,
      color: color ?? this.color,
      description: description ?? this.description,
      lipType: lipType ?? this.lipType,
      lipThickness: lipThickness ?? this.lipThickness,
      withBaketa: withBaketa ?? this.withBaketa,
      withDalfa: withDalfa ?? this.withDalfa,
      imagePath: imagePath ?? this.imagePath,
      pricePerMeter: pricePerMeter ?? this.pricePerMeter,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
