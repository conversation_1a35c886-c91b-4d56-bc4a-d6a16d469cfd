class PanelRectangle {
  final String id;
  final String itemId;
  final double width;
  final double height;
  final double x;
  final double y;
  final bool isRotated;
  final bool isWaste;

  PanelRectangle({
    required this.id,
    required this.itemId,
    required this.width,
    required this.height,
    this.x = 0,
    this.y = 0,
    this.isRotated = false,
    this.isWaste = false,
  });

  double get area => width * height;

  PanelRectangle copyWith({
    String? id,
    String? itemId,
    double? width,
    double? height,
    double? x,
    double? y,
    bool? isRotated,
    bool? isWaste,
  }) {
    return PanelRectangle(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      width: width ?? this.width,
      height: height ?? this.height,
      x: x ?? this.x,
      y: y ?? this.y,
      isRotated: isRotated ?? this.isRotated,
      isWaste: isWaste ?? this.isWaste,
    );
  }

  @override
  String toString() {
    return 'PanelRectangle{id: $id, itemId: $itemId, width: $width, height: $height, x: $x, y: $y, isRotated: $isRotated}';
  }
}

class PanelMeasurement {
  int? id;
  final int orderItemId;
  final double pieceWidth;
  final double pieceHeight;
  final int quantity;
  final String type;
  final String number;
  final DateTime? createdAt;

  PanelMeasurement({
    this.id,
    required this.orderItemId,
    required this.pieceWidth,
    required this.pieceHeight,
    required this.quantity,
    required this.type,
    required this.number,
    this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_item_id': orderItemId,
      'piece_width': pieceWidth,
      'piece_height': pieceHeight,
      'quantity': quantity,
      'type': type,
      'number': number,
      'created_at': createdAt?.millisecondsSinceEpoch ?? DateTime.now().millisecondsSinceEpoch,
    };
  }

  factory PanelMeasurement.fromMap(Map<String, dynamic> map) {
    return PanelMeasurement(
      id: map['id'],
      orderItemId: map['order_item_id'],
      pieceWidth: map['piece_width'],
      pieceHeight: map['piece_height'],
      quantity: map['quantity'],
      type: map['type'],
      number: map['number'],
      createdAt: map['created_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'])
          : null,
    );
  }
}

class OptimizedPanel {
  final double panelWidth;
  final double panelHeight;
  final List<PanelRectangle> placedItems;
  final List<PanelRectangle> wasteAreas;
  final double efficiency;
  final double usedArea;
  final double wasteArea;

  OptimizedPanel({
    required this.panelWidth,
    required this.panelHeight,
    required this.placedItems,
    required this.wasteAreas,
    required this.efficiency,
    required this.usedArea,
    required this.wasteArea,
  });

  // For backward compatibility
  List<PanelRectangle> get rectangles => placedItems;
  double get totalUsedArea => usedArea;
}
