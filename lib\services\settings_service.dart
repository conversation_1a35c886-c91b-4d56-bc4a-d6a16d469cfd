import 'package:shared_preferences/shared_preferences.dart';

class SettingsService {
  static const String _currencyKey = 'app_currency';
  static const String _currencySymbolKey = 'app_currency_symbol';
  static const String _defaultCurrency = 'ريال';
  static const String _defaultCurrencySymbol = 'ر.س';

  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  
  SettingsService._();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Currency settings
  Future<void> setCurrency(String currency) async {
    await init();
    await _prefs!.setString(_currencyKey, currency);
  }

  Future<void> setCurrencySymbol(String symbol) async {
    await init();
    await _prefs!.setString(_currencySymbolKey, symbol);
  }

  Future<String> getCurrency() async {
    await init();
    return _prefs!.getString(_currencyKey) ?? _defaultCurrency;
  }

  Future<String> getCurrencySymbol() async {
    await init();
    return _prefs!.getString(_currencySymbolKey) ?? _defaultCurrencySymbol;
  }

  // Reset to defaults
  Future<void> resetCurrencySettings() async {
    await init();
    await _prefs!.remove(_currencyKey);
    await _prefs!.remove(_currencySymbolKey);
  }

  // Get predefined currencies
  static List<Map<String, String>> getPredefinedCurrencies() {
    return [
      {'name': 'ريال سعودي', 'symbol': 'ر.س'},
      {'name': 'درهم إماراتي', 'symbol': 'د.إ'},
      {'name': 'دينار كويتي', 'symbol': 'د.ك'},
      {'name': 'ريال قطري', 'symbol': 'ر.ق'},
      {'name': 'دينار بحريني', 'symbol': 'د.ب'},
      {'name': 'ريال عماني', 'symbol': 'ر.ع'},
      {'name': 'دولار أمريكي', 'symbol': '\$'},
      {'name': 'يورو', 'symbol': '€'},
      {'name': 'جنيه إسترليني', 'symbol': '£'},
      {'name': 'جنيه مصري', 'symbol': 'ج.م'},
      {'name': 'ليرة تركية', 'symbol': '₺'},
      {'name': 'دينار أردني', 'symbol': 'د.أ'},
      {'name': 'ليرة لبنانية', 'symbol': 'ل.ل'},
    ];
  }
}
