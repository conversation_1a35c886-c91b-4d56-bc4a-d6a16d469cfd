import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CostCalculationService extends StatefulWidget {
  const CostCalculationService({super.key});

  @override
  State<CostCalculationService> createState() => _CostCalculationServiceState();
}

class _CostCalculationServiceState extends State<CostCalculationService>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Kitchen Cost Controllers
  final _kitchenLengthController = TextEditingController();
  final _kitchenWidthController = TextEditingController();
  final _kitchenHeightController = TextEditingController();
  final _materialCostController = TextEditingController(text: '120');
  final _laborCostController = TextEditingController(text: '80');
  final _accessoriesCostController = TextEditingController(text: '50');
  
  // Aluminum Cost Controllers
  final _windowWidthController = TextEditingController();
  final _windowHeightController = TextEditingController();
  final _doorWidthController = TextEditingController();
  final _doorHeightController = TextEditingController();
  final _aluminumPriceController = TextEditingController(text: '45');
  final _glassPriceController = TextEditingController(text: '65');
  final _aluminumLaborController = TextEditingController(text: '60');
  
  // Results
  double _kitchenTotalCost = 0.0;
  double _aluminumTotalCost = 0.0;
  String _selectedKitchenStyle = 'كلاسيك';
  String _selectedAluminumType = 'عادي';
  
  final List<String> _kitchenStyles = ['كلاسيك', 'مودرن', 'تقليدي', 'أمريكي'];
  final List<String> _aluminumTypes = ['عادي', 'حراري', 'uPVC', 'خشبي'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    // Kitchen controllers
    _kitchenLengthController.dispose();
    _kitchenWidthController.dispose();
    _kitchenHeightController.dispose();
    _materialCostController.dispose();
    _laborCostController.dispose();
    _accessoriesCostController.dispose();
    // Aluminum controllers
    _windowWidthController.dispose();
    _windowHeightController.dispose();
    _doorWidthController.dispose();
    _doorHeightController.dispose();
    _aluminumPriceController.dispose();
    _glassPriceController.dispose();
    _aluminumLaborController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حساب التكلفة'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.kitchen),
              text: 'المطابخ',
            ),
            Tab(
              icon: Icon(Icons.window),
              text: 'الألومنيوم',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildKitchenCostCalculator(),
          _buildAluminumCostCalculator(),
        ],
      ),
    );
  }

  Widget _buildKitchenCostCalculator() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.kitchen,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'أبعاد المطبخ',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _kitchenLengthController,
                          label: 'الطول (متر)',
                          icon: Icons.straighten,
                          onChanged: (_) => _calculateKitchenCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          controller: _kitchenWidthController,
                          label: 'العرض (متر)',
                          icon: Icons.straighten,
                          onChanged: (_) => _calculateKitchenCost(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _kitchenHeightController,
                          label: 'الارتفاع (متر)',
                          icon: Icons.height,
                          onChanged: (_) => _calculateKitchenCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildDropdownField(
                          value: _selectedKitchenStyle,
                          items: _kitchenStyles,
                          label: 'نوع المطبخ',
                          icon: Icons.style,
                          onChanged: (value) {
                            setState(() {
                              _selectedKitchenStyle = value!;
                            });
                            _calculateKitchenCost();
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.attach_money,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تفاصيل التكلفة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _materialCostController,
                          label: 'سعر المواد (ريال/متر مربع)',
                          icon: Icons.build,
                          onChanged: (_) => _calculateKitchenCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          controller: _laborCostController,
                          label: 'سعر العمالة (ريال/متر مربع)',
                          icon: Icons.handyman,
                          onChanged: (_) => _calculateKitchenCost(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildNumberField(
                    controller: _accessoriesCostController,
                    label: 'سعر الاكسسوارات (ريال/متر مربع)',
                    icon: Icons.hardware,
                    onChanged: (_) => _calculateKitchenCost(),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          _buildResultCard(
            'النتيجة النهائية',
            _kitchenTotalCost,
            Icons.calculate,
            Colors.green,
            _getKitchenBreakdown(),
          ),
        ],
      ),
    );
  }

  Widget _buildAluminumCostCalculator() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.window,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'أبعاد الشبابيك والأبواب',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _windowWidthController,
                          label: 'عرض الشباك (متر)',
                          icon: Icons.width_normal,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          controller: _windowHeightController,
                          label: 'ارتفاع الشباك (متر)',
                          icon: Icons.height,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _doorWidthController,
                          label: 'عرض الباب (متر)',
                          icon: Icons.width_normal,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          controller: _doorHeightController,
                          label: 'ارتفاع الباب (متر)',
                          icon: Icons.height,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildDropdownField(
                    value: _selectedAluminumType,
                    items: _aluminumTypes,
                    label: 'نوع الألومنيوم',
                    icon: Icons.category,
                    onChanged: (value) {
                      setState(() {
                        _selectedAluminumType = value!;
                      });
                      _calculateAluminumCost();
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.price_change,
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الأسعار',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          controller: _aluminumPriceController,
                          label: 'سعر الألومنيوم (ريال/متر)',
                          icon: Icons.attach_money,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          controller: _glassPriceController,
                          label: 'سعر الزجاج (ريال/متر مربع)',
                          icon: Icons.attach_money,
                          onChanged: (_) => _calculateAluminumCost(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildNumberField(
                    controller: _aluminumLaborController,
                    label: 'سعر العمالة (ريال/متر مربع)',
                    icon: Icons.handyman,
                    onChanged: (_) => _calculateAluminumCost(),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          _buildResultCard(
            'النتيجة النهائية',
            _aluminumTotalCost,
            Icons.calculate,
            Colors.blue,
            _getAluminumBreakdown(),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Function(String) onChanged,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildDropdownField({
    required String value,
    required List<String> items,
    required String label,
    required IconData icon,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: items.map((item) {
        return DropdownMenuItem(
          value: item,
          child: Text(item),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildResultCard(
    String title,
    double totalCost,
    IconData icon,
    Color color,
    List<Map<String, dynamic>> breakdown,
  ) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'إجمالي التكلفة:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${totalCost.toStringAsFixed(2)} ريال',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ],
                ),
              ),
              
              if (breakdown.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'تفاصيل التكلفة:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...breakdown.map((item) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        item['label'],
                        style: const TextStyle(fontSize: 13),
                      ),
                      Text(
                        '${item['value'].toStringAsFixed(2)} ريال',
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _calculateKitchenCost() {
    final length = double.tryParse(_kitchenLengthController.text) ?? 0;
    final width = double.tryParse(_kitchenWidthController.text) ?? 0;
    final height = double.tryParse(_kitchenHeightController.text) ?? 0;
    final materialCost = double.tryParse(_materialCostController.text) ?? 0;
    final laborCost = double.tryParse(_laborCostController.text) ?? 0;
    final accessoriesCost = double.tryParse(_accessoriesCostController.text) ?? 0;

    if (length > 0 && width > 0 && height > 0) {
      final area = length * width;
      double multiplier = 1.0;
      
      switch (_selectedKitchenStyle) {
        case 'مودرن':
          multiplier = 1.3;
          break;
        case 'أمريكي':
          multiplier = 1.5;
          break;
        case 'تقليدي':
          multiplier = 0.8;
          break;
        default:
          multiplier = 1.0;
      }

      final materialTotal = area * materialCost * multiplier;
      final laborTotal = area * laborCost;
      final accessoriesTotal = area * accessoriesCost;
      
      setState(() {
        _kitchenTotalCost = materialTotal + laborTotal + accessoriesTotal;
      });
    }
  }

  void _calculateAluminumCost() {
    final windowWidth = double.tryParse(_windowWidthController.text) ?? 0;
    final windowHeight = double.tryParse(_windowHeightController.text) ?? 0;
    final doorWidth = double.tryParse(_doorWidthController.text) ?? 0;
    final doorHeight = double.tryParse(_doorHeightController.text) ?? 0;
    final aluminumPrice = double.tryParse(_aluminumPriceController.text) ?? 0;
    final glassPrice = double.tryParse(_glassPriceController.text) ?? 0;
    final laborPrice = double.tryParse(_aluminumLaborController.text) ?? 0;

    final windowArea = windowWidth * windowHeight;
    final doorArea = doorWidth * doorHeight;
    final totalArea = windowArea + doorArea;

    if (totalArea > 0) {
      double multiplier = 1.0;
      
      switch (_selectedAluminumType) {
        case 'حراري':
          multiplier = 1.4;
          break;
        case 'uPVC':
          multiplier = 1.2;
          break;
        case 'خشبي':
          multiplier = 1.6;
          break;
        default:
          multiplier = 1.0;
      }

      final perimeter = 2 * (windowWidth + windowHeight + doorWidth + doorHeight);
      final aluminumTotal = perimeter * aluminumPrice * multiplier;
      final glassTotal = totalArea * glassPrice;
      final laborTotal = totalArea * laborPrice;
      
      setState(() {
        _aluminumTotalCost = aluminumTotal + glassTotal + laborTotal;
      });
    }
  }

  List<Map<String, dynamic>> _getKitchenBreakdown() {
    final length = double.tryParse(_kitchenLengthController.text) ?? 0;
    final width = double.tryParse(_kitchenWidthController.text) ?? 0;
    final materialCost = double.tryParse(_materialCostController.text) ?? 0;
    final laborCost = double.tryParse(_laborCostController.text) ?? 0;
    final accessoriesCost = double.tryParse(_accessoriesCostController.text) ?? 0;
    
    if (length <= 0 || width <= 0) return [];
    
    final area = length * width;
    double multiplier = _selectedKitchenStyle == 'مودرن' ? 1.3 : 
                       _selectedKitchenStyle == 'أمريكي' ? 1.5 :
                       _selectedKitchenStyle == 'تقليدي' ? 0.8 : 1.0;
    
    return [
      {
        'label': 'المساحة',
        'value': area,
      },
      {
        'label': 'تكلفة المواد',
        'value': area * materialCost * multiplier,
      },
      {
        'label': 'تكلفة العمالة',
        'value': area * laborCost,
      },
      {
        'label': 'تكلفة الاكسسوارات',
        'value': area * accessoriesCost,
      },
    ];
  }

  List<Map<String, dynamic>> _getAluminumBreakdown() {
    final windowWidth = double.tryParse(_windowWidthController.text) ?? 0;
    final windowHeight = double.tryParse(_windowHeightController.text) ?? 0;
    final doorWidth = double.tryParse(_doorWidthController.text) ?? 0;
    final doorHeight = double.tryParse(_doorHeightController.text) ?? 0;
    final aluminumPrice = double.tryParse(_aluminumPriceController.text) ?? 0;
    final glassPrice = double.tryParse(_glassPriceController.text) ?? 0;
    final laborPrice = double.tryParse(_aluminumLaborController.text) ?? 0;

    final totalArea = (windowWidth * windowHeight) + (doorWidth * doorHeight);
    if (totalArea <= 0) return [];
    
    final perimeter = 2 * (windowWidth + windowHeight + doorWidth + doorHeight);
    double multiplier = _selectedAluminumType == 'حراري' ? 1.4 : 
                       _selectedAluminumType == 'uPVC' ? 1.2 :
                       _selectedAluminumType == 'خشبي' ? 1.6 : 1.0;
    
    return [
      {
        'label': 'المساحة الإجمالية',
        'value': totalArea,
      },
      {
        'label': 'تكلفة الألومنيوم',
        'value': perimeter * aluminumPrice * multiplier,
      },
      {
        'label': 'تكلفة الزجاج',
        'value': totalArea * glassPrice,
      },
      {
        'label': 'تكلفة العمالة',
        'value': totalArea * laborPrice,
      },
    ];
  }
}