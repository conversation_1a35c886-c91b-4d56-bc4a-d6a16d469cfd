import 'dart:io';
import 'package:path/path.dart';

/// سكريبت لإنشاء نسخة احتياطية من قاعدة البيانات الموحدة
Future<void> main() async {
  try {
    // مسار قاعدة البيانات الأصلية
    final sourceDb = File('.dart_tool/sqflite_common_ffi/databases/uptime_unified_database.db');
    
    if (!await sourceDb.exists()) {
      print('❌ قاعدة البيانات الموحدة غير موجودة');
      print('💡 قم بتشغيل: dart run test_unified_database.dart');
      return;
    }
    
    // إنشاء مجلد النسخ الاحتياطية
    final backupDir = Directory('database_backups');
    if (!await backupDir.exists()) {
      await backupDir.create();
    }
    
    // اسم النسخة الاحتياطية مع التاريخ والوقت
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
    final backupPath = join(backupDir.path, 'uptime_unified_database_$timestamp.db');
    
    // نسخ قاعدة البيانات
    await sourceDb.copy(backupPath);
    
    // معلومات النسخة الاحتياطية
    final backupFile = File(backupPath);
    final stat = await backupFile.stat();
    final sizeInKB = (stat.size / 1024).toStringAsFixed(1);
    
    print('✅ تم إنشاء نسخة احتياطية بنجاح!');
    print('📁 المسار: $backupPath');
    print('📏 الحجم: $sizeInKB KB');
    print('📅 التاريخ: ${stat.changed}');
    
    // عرض جميع النسخ الاحتياطية
    final backupFiles = await backupDir.list()
        .where((file) => file is File && file.path.endsWith('.db'))
        .cast<File>()
        .toList();
    
    if (backupFiles.length > 1) {
      print('\n📋 جميع النسخ الاحتياطية:');
      for (final file in backupFiles) {
        final fileName = basename(file.path);
        final fileStat = await file.stat();
        final fileSize = (fileStat.size / 1024).toStringAsFixed(1);
        print('   📄 $fileName ($fileSize KB) - ${fileStat.changed}');
      }
    }
    
  } catch (e) {
    print('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
  }
}
