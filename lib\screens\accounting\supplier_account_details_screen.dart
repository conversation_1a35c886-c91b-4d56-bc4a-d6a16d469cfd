// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../services/unified_invoice_service.dart';
import '../../services/currency_service.dart';
import '../../services/pdf_export_service.dart';
import '../../services/pdf_font_service.dart';
import '../../services/unified_treasury_service.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';

class SupplierAccountDetailsScreen extends StatefulWidget {
  final String supplierName;

  const SupplierAccountDetailsScreen({
    super.key,
    required this.supplierName,
  });

  @override
  State<SupplierAccountDetailsScreen> createState() => _SupplierAccountDetailsScreenState();
}

class _SupplierAccountDetailsScreenState extends State<SupplierAccountDetailsScreen> {
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  final UnifiedTreasuryService _treasuryDatabase = UnifiedTreasuryService();
  Map<String, dynamic>? _supplierAccount;
  // ignore: unused_field
  List<Map<String, dynamic>> _invoices = [];
  // ignore: unused_field
  List<Map<String, dynamic>> _payments = [];
  List<Map<String, dynamic>> _allTransactions = [];
  bool _isLoading = true;
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    _loadSupplierData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload data when returning to this screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSupplierData();
    });
  }

  Future<void> _loadSupplierData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load supplier account details
      final accountDetails = await _database.getSupplierAccountDetails(widget.supplierName);

      // Load supplier invoices
      final invoices = await _database.getInvoicesBySupplierName(widget.supplierName);

      // Load supplier payments
      final payments = await _database.getSupplierPayments(widget.supplierName);

      // Combine invoices and payments into one list
      final allTransactions = <Map<String, dynamic>>[];

      // Add invoices
      for (final invoice in invoices) {
        allTransactions.add({
          ...invoice,
          'transactionType': 'invoice',
        });
      }

      // Add payments
      for (final payment in payments) {
        allTransactions.add({
          ...payment,
          'transactionType': 'payment',
        });
      }

      // Sort by date (newest first)
      allTransactions.sort((a, b) {
        final dateA = DateTime.parse(a['date']);
        final dateB = DateTime.parse(b['date']);
        return dateB.compareTo(dateA);
      });

      setState(() {
        _supplierAccount = accountDetails;
        _invoices = invoices;
        _payments = payments;
        _allTransactions = allTransactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل بيانات المورد: $e')),
        );
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
      _filterInvoicesByDate();
    }
  }

  void _filterInvoicesByDate() {
    if (_dateRange == null) return;

    // Filter invoices by date range
    _loadSupplierData();
  }

  void _clearDateFilter() {
    setState(() {
      _dateRange = null;
    });
    _loadSupplierData();
  }

  Future<void> _exportToPDF() async {
    if (_supplierAccount == null) return;

    try {
      // Show loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(width: 16),
                Text('جاري إنشاء ملف PDF...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      final pdf = pw.Document();

      // Load Arabic fonts with better number support
      await PdfFontService.loadFonts();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: PdfFontService.createTheme(),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.red50,
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    pw.Text(
                      'كشف حساب مورد',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.red800,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'اسم المورد: ${widget.supplierName}',
                      style: pw.TextStyle(fontSize: 18),
                    ),
                    pw.Text(
                      'تاريخ التقرير: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}',
                      style: pw.TextStyle(fontSize: 14, color: PdfColors.grey600),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Account Summary
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'ملخص الحساب',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text('إجمالي المشتريات:'),
                        pw.Text('${NumberFormat('#,##0.00').format(_supplierAccount!['totalPurchases'])} ريال'),
                      ],
                    ),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text('إجمالي المرتجعات:'),
                        pw.Text('${NumberFormat('#,##0.00').format(_supplierAccount!['totalReturns'])} ريال'),
                      ],
                    ),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text('إجمالي الدفعات:'),
                        pw.Text('${NumberFormat('#,##0.00').format((_supplierAccount!['totalPayments'] as num).abs())} ريال'),
                      ],
                    ),
                    pw.Divider(),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'الرصيد النهائي:',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        pw.Text(
                          '${NumberFormat('#,##0.00').format(_supplierAccount!['balance'])} ريال',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Invoices Table
              pw.Text(
                'تفاصيل المعاملات',
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),

              pw.Table(
                border: pw.TableBorder.all(color: PdfColors.grey300),
                children: [
                  // Header
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(color: PdfColors.grey100),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('رقم الفاتورة', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('التاريخ', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('النوع', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('المبلغ', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                    ],
                  ),
                  // Data rows
                  ..._allTransactions.map((transaction) {
                    final transactionType = transaction['transactionType'] as String;

                    if (transactionType == 'invoice') {
                      return pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(transaction['invoiceNumber'] ?? ''),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              DateFormat('yyyy/MM/dd').format(DateTime.parse(transaction['date'])),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              transaction['type'] == 'purchase' ? 'مشتريات' : 'مرتجع مشتريات',
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              '${NumberFormat('#,##0.00').format(transaction['netAmount'])} ريال',
                            ),
                          ),
                        ],
                      );
                    } else {
                      // Payment transaction
                      final amount = (transaction['amount'] as num?)?.toDouble() ?? 0.0;
                      final isNegative = amount < 0;

                      return pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text('-'),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              DateFormat('yyyy/MM/dd').format(DateTime.parse(transaction['date'])),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              isNegative ? 'دفع للمورد' : 'خصم من الحساب',
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              '${NumberFormat('#,##0.00').format(amount.abs())} ريال',
                            ),
                          ),
                        ],
                      );
                    }
                  }),
                ],
              ),
            ],
          );
        },
      ),
    );

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      // Get file name
      final fileName = PdfExportService.getDefaultFileName('كشف_حساب', customerName: widget.supplierName);

      // Save file using the improved service
      if (mounted) {
        await PdfExportService.savePdfFile(
          context: context,
          pdfBytes: pdfBytes,
          fileName: fileName,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('حساب ${widget.supplierName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
            tooltip: 'تصفية بالتاريخ',
          ),
          if (_dateRange != null)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearDateFilter,
              tooltip: 'إزالة التصفية',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSupplierData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _supplierAccount == null
              ? const Center(
                  child: Text('لا توجد بيانات للمورد'),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date filter info
                      if (_dateRange != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.blue[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info, color: Colors.blue[600]),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'عرض المعاملات من ${DateFormat('yyyy/MM/dd').format(_dateRange!.start)} إلى ${DateFormat('yyyy/MM/dd').format(_dateRange!.end)}',
                                  style: TextStyle(color: Colors.blue[800]),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Account summary
                      _buildAccountSummary(),

                      const SizedBox(height: 16),

                      // Invoices list
                      _buildInvoicesList(),
                    ],
                  ),
                ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildAccountSummary() {
    final totalPurchases = (_supplierAccount!['totalPurchases'] as num?)?.toDouble() ?? 0.0;
    final totalReturns = (_supplierAccount!['totalReturns'] as num?)?.toDouble() ?? 0.0;
    final totalPayments = (_supplierAccount!['totalPayments'] as num?)?.toDouble() ?? 0.0;
    final totalTransactions = (_supplierAccount!['totalTransactions'] as num?)?.toInt() ?? 0;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Theme.of(context).colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'ملخص الحساب',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            Row(
              children: [
                Expanded(
                  child: FutureBuilder<String>(
                    future: CurrencyService.instance.formatAmount(totalPurchases),
                    builder: (context, snapshot) {
                      return _buildSummaryItem(
                        'إجمالي المشتريات',
                        snapshot.data ?? CurrencyService.instance.formatForPdf(totalPurchases),
                        Icons.shopping_cart,
                        Colors.blue,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: FutureBuilder<String>(
                    future: CurrencyService.instance.formatAmount(totalReturns),
                    builder: (context, snapshot) {
                      return _buildSummaryItem(
                        'إجمالي المرتجعات',
                        snapshot.data ?? CurrencyService.instance.formatForPdf(totalReturns),
                        Icons.keyboard_return,
                        Colors.orange,
                      );
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: FutureBuilder<String>(
                    future: CurrencyService.instance.formatAmount(totalPayments.abs()),
                    builder: (context, snapshot) {
                      return _buildSummaryItem(
                        'إجمالي الدفعات',
                        snapshot.data ?? CurrencyService.instance.formatForPdf(totalPayments.abs()),
                        Icons.payment,
                        Colors.purple,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryItem(
                    'عدد المعاملات',
                    '$totalTransactions',
                    Icons.receipt_long,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            FutureBuilder<String>(
              future: CurrencyService.instance.formatAmount((totalPurchases - totalReturns - totalPayments.abs()).abs()),
              builder: (context, snapshot) {
                final outstandingAmount = totalPurchases - totalReturns - totalPayments.abs();
                return _buildSummaryItem(
                  'المبلغ الباقي للمورد',
                  snapshot.data ?? CurrencyService.instance.formatForPdf(outstandingAmount.abs()),
                  Icons.account_balance_wallet,
                  outstandingAmount >= 0 ? Colors.red : Colors.green,
                  subtitle: outstandingAmount >= 0 ? 'مستحق الدفع' : 'مدفوع زيادة',
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color, {String? subtitle}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (subtitle != null)
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: color.withValues(alpha: 0.7),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showAddPaymentDialog(true),
                icon: const Icon(Icons.add_circle, color: Colors.white, size: 20),
                label: const Text(
                  'دفع للمورد',
                  style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showAddPaymentDialog(false),
                icon: const Icon(Icons.remove_circle, color: Colors.white, size: 20),
                label: const Text(
                  'خصم مبلغ',
                  style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _exportToPDF,
                icon: const Icon(Icons.picture_as_pdf, color: Colors.white, size: 20),
                label: const Text(
                  'تصدير PDF',
                  style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAddPaymentDialog(bool isPayment) async {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController notesController = TextEditingController();
    Treasury? selectedTreasury;
    List<Treasury> treasuries = [];

    // Load treasuries
    try {
      final treasuriesData = await _treasuryDatabase.getAllTreasuries();
      treasuries = treasuriesData.map((data) => Treasury.fromMap(data)).toList();
      if (treasuries.isNotEmpty) {
        selectedTreasury = treasuries.first;
      } else {
        // No treasuries found, show option to add one
        if (mounted) {
          final shouldAddTreasury = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('لا توجد خزائن'),
              content: const Text('يجب إضافة خزينة أولاً قبل إضافة الدفعات. هل تريد إضافة خزينة جديدة؟'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('إضافة خزينة'),
                ),
              ],
            ),
          );

          if (shouldAddTreasury == true) {
            await _showAddTreasuryDialog();
            // Reload treasuries after adding
            final treasuriesData2 = await _treasuryDatabase.getAllTreasuries();
            treasuries = treasuriesData2.map((data) => Treasury.fromMap(data)).toList();
            if (treasuries.isNotEmpty) {
              selectedTreasury = treasuries.first;
            } else {
              return; // User cancelled or failed to add treasury
            }
          } else {
            return; // User cancelled
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading treasuries: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الخزائن: $e')),
        );
      }
      return;
    }

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(
                    isPayment ? Icons.add_circle : Icons.remove_circle,
                    color: isPayment ? Colors.red : Colors.green,
                  ),
                  const SizedBox(width: 8),
                  Text(isPayment ? 'دفع مبلغ للمورد' : 'خصم مبلغ من الحساب'),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Treasury selection
                    if (treasuries.isNotEmpty)
                      DropdownButtonFormField<Treasury>(
                        value: selectedTreasury,
                        decoration: InputDecoration(
                          labelText: 'اختر الخزينة',
                          prefixIcon: const Icon(Icons.account_balance),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        items: treasuries.map((treasury) {
                          return DropdownMenuItem<Treasury>(
                            value: treasury,
                            child: Text(treasury.name),
                          );
                        }).toList(),
                        onChanged: (Treasury? value) {
                          setState(() {
                            selectedTreasury = value;
                          });
                        },
                      ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'المبلغ',
                        hintText: 'أدخل المبلغ',
                        prefixIcon: const Icon(Icons.attach_money),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: notesController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: 'ملاحظات',
                        hintText: 'أدخل ملاحظات (اختياري)',
                        prefixIcon: const Icon(Icons.note),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    final amount = double.tryParse(amountController.text);
                    if (amount != null && amount > 0 && selectedTreasury != null) {
                      await _addPayment(amount, isPayment, notesController.text, selectedTreasury!);
                      if (mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              isPayment
                                ? 'تم دفع المبلغ بنجاح'
                                : 'تم خصم المبلغ بنجاح'
                            ),
                            backgroundColor: isPayment ? Colors.red : Colors.green,
                          ),
                        );
                        _loadSupplierData(); // Refresh data
                      }
                    } else if (selectedTreasury == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى اختيار خزينة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال مبلغ صحيح'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isPayment ? Colors.red : Colors.green,
                  ),
                  child: Text(
                    isPayment ? 'دفع المبلغ' : 'خصم المبلغ',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _showAddTreasuryDialog() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController balanceController = TextEditingController();

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.account_balance, color: Colors.blue),
              SizedBox(width: 8),
              Text('إضافة خزينة جديدة'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'اسم الخزينة',
                  hintText: 'أدخل اسم الخزينة',
                  prefixIcon: const Icon(Icons.account_balance),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: balanceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الرصيد الافتتاحي',
                  hintText: 'أدخل الرصيد الافتتاحي (اختياري)',
                  prefixIcon: const Icon(Icons.attach_money),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  final balance = double.tryParse(balanceController.text) ?? 0.0;
                  final treasury = Treasury(
                    name: nameController.text.trim(),
                    previousBalance: balance,
                    currentBalance: 0.0,
                    date: DateTime.now(),
                  );

                  try {
                    await _treasuryDatabase.insertTreasury(treasury.toMap());
                    if (mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إضافة الخزينة بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('خطأ في إضافة الخزينة: $e')),
                      );
                    }
                  }
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال اسم الخزينة'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: const Text('إضافة', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _addPayment(double amount, bool isPayment, String notes, Treasury treasury) async {
    try {
      // Add payment to supplier payments table
      await _database.addSupplierPayment(
        widget.supplierName,
        isPayment ? -amount : amount, // Negative for payment to supplier
        notes,
      );

      // Create treasury transaction
      final transaction = TreasuryTransaction(
        treasuryId: treasury.id!,
        description: isPayment
          ? 'دفع للمورد: ${widget.supplierName}'
          : 'خصم من حساب المورد: ${widget.supplierName}',
        income: isPayment ? 0.0 : amount,
        expenses: isPayment ? amount : 0.0,
        notes: notes.isNotEmpty ? notes : 'معاملة دفع مورد',
        date: DateTime.now(),
      );

      await _treasuryDatabase.insertTreasuryTransaction(transaction.toMap());
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إضافة المدفوعة: $e')),
        );
      }
    }
  }

  Widget _buildInvoicesList() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Theme.of(context).colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'تفاصيل المعاملات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_allTransactions.length} معاملة',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (_allTransactions.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد معاملات',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _allTransactions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final transaction = _allTransactions[index];
                  return _buildTransactionItem(transaction);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final transactionType = transaction['transactionType'] as String;

    if (transactionType == 'invoice') {
      return _buildInvoiceItem(transaction);
    } else {
      return _buildPaymentItem(transaction);
    }
  }

  Widget _buildInvoiceItem(Map<String, dynamic> invoice) {
    final invoiceNumber = invoice['invoiceNumber'] ?? '';
    final date = DateTime.parse(invoice['date']);
    final type = invoice['type'] ?? '';
    final netAmount = (invoice['netAmount'] as num?)?.toDouble() ?? 0.0;
    final isReturn = type.contains('return');

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: isReturn ? Colors.orange.withValues(alpha: 0.1) : Colors.blue.withValues(alpha: 0.1),
        child: Icon(
          isReturn ? Icons.keyboard_return : Icons.shopping_cart,
          color: isReturn ? Colors.orange : Colors.blue,
          size: 20,
        ),
      ),
      title: Text(
        'فاتورة رقم: $invoiceNumber',
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            DateFormat('yyyy/MM/dd - HH:mm').format(date),
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          Text(
            isReturn ? 'مرتجع مشتريات' : 'فاتورة مشتريات',
            style: TextStyle(
              color: isReturn ? Colors.orange : Colors.blue,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${NumberFormat('#,##0.00').format(netAmount)} ريال',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isReturn ? Colors.orange : Colors.blue,
            ),
          ),
          Text(
            isReturn ? 'خصم' : 'إضافة',
            style: TextStyle(
              fontSize: 12,
              color: isReturn ? Colors.orange : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentItem(Map<String, dynamic> payment) {
    final date = DateTime.parse(payment['date']);
    final amount = (payment['amount'] as num?)?.toDouble() ?? 0.0;
    final notes = payment['notes'] as String? ?? '';
    final isNegative = amount < 0; // For suppliers: negative = payment to supplier

    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: isNegative ? Colors.red.withValues(alpha: 0.1) : Colors.green.withValues(alpha: 0.1),
        child: Icon(
          isNegative ? Icons.remove_circle : Icons.add_circle,
          color: isNegative ? Colors.red : Colors.green,
          size: 20,
        ),
      ),
      title: Text(
        isNegative ? 'دفع للمورد' : 'خصم من الحساب',
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            DateFormat('yyyy/MM/dd - HH:mm').format(date),
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (notes.isNotEmpty)
            Text(
              notes,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${NumberFormat('#,##0.00').format(amount.abs())} ريال',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isNegative ? Colors.red : Colors.green,
            ),
          ),
          Text(
            isNegative ? 'دفعة' : 'خصم',
            style: TextStyle(
              fontSize: 12,
              color: isNegative ? Colors.red : Colors.green,
            ),
          ),
        ],
      ),
    );
  }
}
