import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/aluminum_profile.dart';
import '../../models/profile_series.dart';
import '../../services/unified_aluminum_service.dart';

class ProfileManagementScreen extends StatefulWidget {
  final ProfileType profileType;
  final ProfileCategory profileCategory;
  final ProfileSeries? series;

  const ProfileManagementScreen({
    super.key,
    required this.profileType,
    required this.profileCategory,
    this.series,
  });

  @override
  State<ProfileManagementScreen> createState() => _ProfileManagementScreenState();
}

class _ProfileManagementScreenState extends State<ProfileManagementScreen> {
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();
  List<AluminumProfile> _profiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProfiles();
    _initializeSampleData();
  }

  Future<void> _initializeSampleData() async {
    // تم نقل البيانات إلى قاعدة البيانات الموحدة
  }

  Future<void> _loadProfiles() async {
    setState(() => _isLoading = true);
    try {
      List<AluminumProfile> profiles;
      if (widget.series != null) {
        final profilesData = await _aluminumService.getProfilesBySeries(widget.series!.id!);
        profiles = profilesData.map((data) => AluminumProfile.fromMap(data)).toList();
      } else {
        final profilesData = await _aluminumService.getAllProfiles();
        profiles = profilesData.map((data) => AluminumProfile.fromMap(data)).toList();
      }
      setState(() {
        _profiles = profiles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل القطاعات: $e')),
        );
      }
    }
  }

  Future<void> _showAddEditDialog({AluminumProfile? profile}) async {
    final result = await showDialog<AluminumProfile>(
      context: context,
      builder: (context) => _AddEditProfileDialog(
        profileType: widget.profileType,
        profileCategory: widget.profileCategory,
        series: widget.series,
        profile: profile,
      ),
    );

    if (result != null) {
      try {
        if (profile == null) {
          await _aluminumService.insertAluminumProfile(result);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppLocalizations.of(context)!.profileSaved)),
            );
          }
        } else {
          await _aluminumService.updateProfile(result);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppLocalizations.of(context)!.profileUpdated)),
            );
          }
        }
        _loadProfiles();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حفظ القطاع: $e')),
          );
        }
      }
    }
  }

  Future<void> _deleteProfile(AluminumProfile profile) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.deleteProfile),
        content: Text('هل أنت متأكد من حذف القطاع "${profile.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(AppLocalizations.of(context)!.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _aluminumService.deleteProfile(profile.id!);
        _loadProfiles();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.profileDeleted)),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف القطاع: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.series != null
              ? '${widget.series!.name} - ${widget.profileCategory.getLocalizedName(Localizations.localeOf(context).languageCode)}'
              : '${widget.profileType.getLocalizedName(Localizations.localeOf(context).languageCode)} - ${widget.profileCategory.getLocalizedName(Localizations.localeOf(context).languageCode)}',
        ),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _showAddEditDialog(),
            icon: const Icon(Icons.add),
            tooltip: localizations.addNewProfile,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _profiles.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          localizations.noProfilesFound,
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _showAddEditDialog(),
                          icon: const Icon(Icons.add),
                          label: Text(localizations.addNewProfile),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF607D8B),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildDataTable(localizations, isTablet),
      ),
    );
  }

  Widget _buildDataTable(AppLocalizations localizations, bool isTablet) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFF607D8B),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.table_chart, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  'قطاعات ${widget.profileCategory.arabicName}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => _showAddEditDialog(),
                  icon: const Icon(Icons.add, color: Colors.white),
                  tooltip: localizations.addNewProfile,
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  columnSpacing: isTablet ? 40 : 20,
                  horizontalMargin: 16,
                  headingRowColor: WidgetStateProperty.all(Colors.grey[100]),
                  columns: [
                    DataColumn(
                      label: Text(
                        localizations.profileName,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    DataColumn(
                      label: Text(
                        localizations.profileNumber,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    DataColumn(
                      label: Text(
                        localizations.profileThickness,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    DataColumn(
                      label: Text(
                        localizations.lipType,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    DataColumn(
                      label: Text(
                        localizations.lipThickness,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    DataColumn(
                      label: Text(
                        localizations.actions,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                  rows: _profiles.map((profile) {
                    return DataRow(
                      cells: [
                        DataCell(Text(profile.name)),
                        DataCell(Text(profile.code)),
                        DataCell(Text(profile.thickness?.toString() ?? '-')),
                        DataCell(Text(profile.lipType ?? '-')),
                        DataCell(Text(profile.lipThickness?.toString() ?? '-')),
                        DataCell(
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _showAddEditDialog(profile: profile),
                                icon: const Icon(Icons.edit, color: Colors.blue),
                                tooltip: localizations.editProfile,
                              ),
                              IconButton(
                                onPressed: () => _deleteProfile(profile),
                                icon: const Icon(Icons.delete, color: Colors.red),
                                tooltip: localizations.deleteProfile,
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _AddEditProfileDialog extends StatefulWidget {
  final ProfileType profileType;
  final ProfileCategory profileCategory;
  final ProfileSeries? series;
  final AluminumProfile? profile;

  const _AddEditProfileDialog({
    required this.profileType,
    required this.profileCategory,
    this.series,
    this.profile,
  });

  @override
  State<_AddEditProfileDialog> createState() => _AddEditProfileDialogState();
}

class _AddEditProfileDialogState extends State<_AddEditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  // ignore: unused_field
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  late TextEditingController _nameController;
  late TextEditingController _codeController;
  late TextEditingController _thicknessController;
  late TextEditingController _lipTypeController;
  late TextEditingController _lipThicknessController;
  late TextEditingController _widthController;
  late TextEditingController _heightController;
  late TextEditingController _weightController;
  late TextEditingController _colorController;
  late TextEditingController _descriptionController;

  bool get _isEditing => widget.profile != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (!_isEditing) {
      _generateCode();
    }
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.profile?.name ?? '');
    _codeController = TextEditingController(text: widget.profile?.code ?? '');
    _thicknessController = TextEditingController(text: widget.profile?.thickness?.toString() ?? '');
    _lipTypeController = TextEditingController(text: widget.profile?.lipType ?? '');
    _lipThicknessController = TextEditingController(text: widget.profile?.lipThickness?.toString() ?? '');
    _widthController = TextEditingController(text: widget.profile?.width?.toString() ?? '');
    _heightController = TextEditingController(text: widget.profile?.height?.toString() ?? '');
    _weightController = TextEditingController(text: widget.profile?.weight?.toString() ?? '');
    _colorController = TextEditingController(text: widget.profile?.color ?? 'أبيض');
    _descriptionController = TextEditingController(text: widget.profile?.description ?? '');
  }

  Future<void> _generateCode() async {
    try {
      final code = 'AL${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
      setState(() {
        _codeController.text = code;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _thicknessController.dispose();
    _lipTypeController.dispose();
    _lipThicknessController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _colorController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveProfile() {
    if (!_formKey.currentState!.validate()) return;

    final now = DateTime.now();
    final profile = AluminumProfile(
      id: widget.profile?.id,
      name: _nameController.text.trim(),
      code: _codeController.text.trim(),
      type: widget.profileType,
      category: widget.profileCategory,
      seriesId: widget.series?.id,
      thickness: double.tryParse(_thicknessController.text),
      lipType: _lipTypeController.text.trim().isEmpty ? null : _lipTypeController.text.trim(),
      lipThickness: double.tryParse(_lipThicknessController.text),
      width: double.tryParse(_widthController.text),
      height: double.tryParse(_heightController.text),
      weight: double.tryParse(_weightController.text),
      color: _colorController.text.trim(),
      description: _descriptionController.text.trim(),
      createdAt: widget.profile?.createdAt ?? now,
      updatedAt: now,
    );

    Navigator.pop(context, profile);
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return AlertDialog(
      title: Text(_isEditing ? localizations.editProfile : localizations.addNewProfile),
      content: SizedBox(
        width: isTablet ? 500 : double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: localizations.profileName,
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم القطاع مطلوب';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _codeController,
                        decoration: InputDecoration(
                          labelText: localizations.profileCode,
                          border: const OutlineInputBorder(),
                        ),
                        readOnly: _isEditing,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'كود القطاع مطلوب';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _thicknessController,
                        decoration: InputDecoration(
                          labelText: localizations.profileThickness,
                          border: const OutlineInputBorder(),
                          suffixText: 'مم',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _lipTypeController,
                        decoration: InputDecoration(
                          labelText: localizations.lipType,
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _lipThicknessController,
                        decoration: InputDecoration(
                          labelText: localizations.lipThickness,
                          border: const OutlineInputBorder(),
                          suffixText: 'مم',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _colorController,
                        decoration: InputDecoration(
                          labelText: localizations.profileColor,
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _widthController,
                        decoration: InputDecoration(
                          labelText: localizations.profileWidth,
                          border: const OutlineInputBorder(),
                          suffixText: 'مم',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _heightController,
                        decoration: InputDecoration(
                          labelText: localizations.profileHeight,
                          border: const OutlineInputBorder(),
                          suffixText: 'مم',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _weightController,
                  decoration: InputDecoration(
                    labelText: localizations.profileWeight,
                    border: const OutlineInputBorder(),
                    suffixText: 'كجم/متر',
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: localizations.profileDescription,
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(localizations.cancel),
        ),
        ElevatedButton(
          onPressed: _saveProfile,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF607D8B),
            foregroundColor: Colors.white,
          ),
          child: Text(_isEditing ? localizations.update : localizations.save),
        ),
      ],
    );
  }
}
