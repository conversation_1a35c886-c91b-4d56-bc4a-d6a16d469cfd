import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/aluminum_quotation.dart';
import '../../services/unified_aluminum_service.dart';

class AddEditAluminumQuotationScreen extends StatefulWidget {
  final AluminumQuotation? quotation;

  const AddEditAluminumQuotationScreen({super.key, this.quotation});

  @override
  State<AddEditAluminumQuotationScreen> createState() => _AddEditAluminumQuotationScreenState();
}

class _AddEditAluminumQuotationScreenState extends State<AddEditAluminumQuotationScreen> {
  final _formKey = GlobalKey<FormState>();
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  late TextEditingController _quotationNumberController;
  late TextEditingController _clientNameController;
  late TextEditingController _clientPhoneController;
  late TextEditingController _clientAddressController;
  late TextEditingController _notesController;

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool get _isEditing => widget.quotation != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (!_isEditing) {
      _generateQuotationNumber();
    }
  }

  void _initializeControllers() {
    _quotationNumberController = TextEditingController(
      text: widget.quotation?.quotationNumber ?? '',
    );
    _clientNameController = TextEditingController(
      text: widget.quotation?.clientName ?? '',
    );
    _clientPhoneController = TextEditingController(
      text: widget.quotation?.clientPhone ?? '',
    );
    _clientAddressController = TextEditingController(
      text: widget.quotation?.clientAddress ?? '',
    );
    _notesController = TextEditingController(
      text: widget.quotation?.notes ?? '',
    );

    if (widget.quotation != null) {
      _selectedDate = widget.quotation!.quotationDate;
    }
  }

  Future<void> _generateQuotationNumber() async {
    try {
      final number = await _aluminumService.generateQuotationNumber();
      setState(() {
        _quotationNumberController.text = number;
      });
    } catch (e) {
      // Handle error silently or show a message
    }
  }

  @override
  void dispose() {
    _quotationNumberController.dispose();
    _clientNameController.dispose();
    _clientPhoneController.dispose();
    _clientAddressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveQuotation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final now = DateTime.now();
      final quotation = AluminumQuotation(
        id: widget.quotation?.id,
        quotationNumber: _quotationNumberController.text.trim(),
        quotationDate: _selectedDate,
        clientName: _clientNameController.text.trim(),
        clientPhone: _clientPhoneController.text.trim(),
        clientAddress: _clientAddressController.text.trim(),
        notes: _notesController.text.trim(),
        createdAt: widget.quotation?.createdAt ?? now,
        updatedAt: now,
      );

      if (_isEditing) {
        await _aluminumService.updateQuotation(quotation);
      } else {
        await _aluminumService.insertQuotation(quotation);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'تم تحديث المقايسة بنجاح' : 'تم إضافة المقايسة بنجاح'),
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ المقايسة: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? localizations.editQuotation : localizations.addQuotation),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            )
          else
            IconButton(
              onPressed: _saveQuotation,
              icon: const Icon(Icons.save),
            ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildCard(
                title: 'بيانات المقايسة الأساسية',
                children: [
                  _buildTextField(
                    controller: _quotationNumberController,
                    label: localizations.quotationNumber,
                    icon: Icons.numbers,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'رقم المقايسة مطلوب';
                      }
                      return null;
                    },
                    readOnly: _isEditing,
                  ),
                  const SizedBox(height: 16),
                  _buildDateField(),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _clientNameController,
                    label: localizations.clientName,
                    icon: Icons.person,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'اسم العميل مطلوب';
                      }
                      return null;
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildCard(
                title: 'بيانات الاتصال',
                children: [
                  _buildTextField(
                    controller: _clientPhoneController,
                    label: localizations.clientPhone,
                    icon: Icons.phone,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _clientAddressController,
                    label: localizations.clientAddress,
                    icon: Icons.location_on,
                    maxLines: 2,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildCard(
                title: 'ملاحظات',
                children: [
                  _buildTextField(
                    controller: _notesController,
                    label: localizations.quotationNotes,
                    icon: Icons.note,
                    maxLines: 3,
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _isLoading ? null : _saveQuotation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF607D8B),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  _isEditing ? 'تحديث المقايسة' : 'إضافة المقايسة',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCard({required String title, required List<Widget> children}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF607D8B),
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool readOnly = false,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      readOnly: readOnly,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: readOnly ? Colors.grey[100] : Colors.white,
      ),
    );
  }

  Widget _buildDateField() {
    final localizations = AppLocalizations.of(context)!;

    return InkWell(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.quotationDate,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
