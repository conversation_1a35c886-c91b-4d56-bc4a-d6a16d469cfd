// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../models/invoice.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';
import '../../services/unified_invoice_service.dart';
import '../../services/unified_treasury_service.dart';
import '../../widgets/name_selector_dialog.dart';
import '../../services/currency_service.dart';

class AddEditInvoiceScreen extends StatefulWidget {
  final String invoiceType;
  final Invoice? invoice;

  const AddEditInvoiceScreen({
    super.key,
    required this.invoiceType,
    this.invoice,
  });

  @override
  State<AddEditInvoiceScreen> createState() => _AddEditInvoiceScreenState();
}

class _AddEditInvoiceScreenState extends State<AddEditInvoiceScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  final UnifiedTreasuryService _treasuryDatabase = UnifiedTreasuryService();

  // Controllers
  final TextEditingController _invoiceNumberController = TextEditingController();
  final TextEditingController _supplierCustomerController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _discountController = TextEditingController(text: '0');
  final TextEditingController _expensesController = TextEditingController(text: '0');

  DateTime _selectedDate = DateTime.now();
  List<InvoiceItem> _items = [];
  List<String> _availableNames = [];

  bool _isLoading = false;
  bool get _isEditing => widget.invoice != null;

  // Treasury variables
  List<Treasury> _treasuries = [];
  Treasury? _selectedTreasury;
  bool _linkToTreasury = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    // Load available names based on invoice type
    await _loadAvailableNames();
    // Load treasuries
    await _loadTreasuries();

    if (_isEditing) {
      final invoice = widget.invoice!;
      _invoiceNumberController.text = invoice.invoiceNumber;
      _supplierCustomerController.text = invoice.supplierOrCustomerName;
      _notesController.text = invoice.notes;
      _discountController.text = invoice.discount.toString();
      _expensesController.text = invoice.expenses.toString();
      _selectedDate = invoice.date;
      _items = List.from(invoice.items);
    } else {
      // Generate new invoice number
      try {
        final invoiceNumber = await _database.generateInvoiceNumber(widget.invoiceType);
        _invoiceNumberController.text = invoiceNumber;
      } catch (e) {
        // Fallback if generation fails
        _invoiceNumberController.text = 'INV${DateTime.now().millisecondsSinceEpoch}';
      }
    }
    setState(() {});
  }

  Future<void> _loadAvailableNames() async {
    try {
      List<String> names = [];

      switch (widget.invoiceType) {
        case 'sale':
        case 'sale_return':
          names = await _database.getCustomerNamesFromInvoices();
          debugPrint('Customer names loaded: $names'); // Debug
          break;
        case 'purchase':
        case 'purchase_return':
          names = await _database.getSupplierNamesFromInvoices();
          debugPrint('Supplier names loaded: $names'); // Debug
          break;
        default:
          names = await _database.getAllEntityNames();
          debugPrint('All entity names loaded: $names'); // Debug
      }

      setState(() {
        _availableNames = names;
      });
      debugPrint('Available names set: $_availableNames'); // Debug
    } catch (e) {
      debugPrint('Error loading available names: $e'); // Debug
      // Handle error silently, user can still type manually
      setState(() {
        _availableNames = [];
      });
    }
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _supplierCustomerController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    _expensesController.dispose();
    super.dispose();
  }

  String _getScreenTitle() {
    final action = _isEditing ? 'تعديل' : 'إضافة';
    switch (widget.invoiceType) {
      case 'purchase':
        return '$action فاتورة مشتريات';
      case 'sale':
        return '$action فاتورة مبيعات';
      case 'purchase_return':
        return '$action فاتورة مرتجع مشتريات';
      case 'sale_return':
        return '$action فاتورة مرتجع مبيعات';
      default:
        return '$action فاتورة';
    }
  }

  Future<void> _loadTreasuries() async {
    try {
      final treasuriesData = await _treasuryDatabase.getAllTreasuries();
      final treasuries = treasuriesData.map((data) => Treasury.fromMap(data)).toList();
      setState(() {
        _treasuries = treasuries;

        if (_isEditing && widget.invoice!.treasuryId != null) {
          // إذا كان في وضع التعديل وكانت الفاتورة مربوطة بخزينة
          _selectedTreasury = treasuries.isNotEmpty
            ? treasuries.firstWhere(
                (t) => t.id == widget.invoice!.treasuryId,
                orElse: () => treasuries.first,
              )
            : null;
          _linkToTreasury = widget.invoice!.treasuryId != null;
        } else {
          // إعدادات افتراضية للفواتير الجديدة
          if (treasuries.isNotEmpty) {
            _linkToTreasury = true; // تفعيل الربط بالخزينة افتراضياً
            _selectedTreasury = treasuries.first; // اختيار أول خزينة
          } else {
            _linkToTreasury = false;
            _selectedTreasury = null;
            // إذا لم توجد خزائن، اعرض شاشة إضافة خزينة
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showAddTreasuryDialog();
            });
          }
        }
      });
    } catch (e) {
      debugPrint('Error loading treasuries: $e');
      setState(() {
        _treasuries = [];
        _linkToTreasury = false;
        _selectedTreasury = null;
      });
    }
  }

  String _getEntityLabel() {
    switch (widget.invoiceType) {
      case 'purchase':
      case 'purchase_return':
        return 'اسم المورد';
      case 'sale':
      case 'sale_return':
        return 'اسم العميل';
      default:
        return 'اسم الطرف';
    }
  }

  double get _totalAmount {
    return _items.fold(0.0, (sum, item) => sum + item.total);
  }

  double get _discount {
    return double.tryParse(_discountController.text) ?? 0.0;
  }

  double get _expenses {
    return double.tryParse(_expensesController.text) ?? 0.0;
  }

  double get _netAmount {
    return _totalAmount - _discount + _expenses;
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(_getScreenTitle()),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          // Debug button to force add sample data
          if (widget.invoiceType == 'sale_return' || widget.invoiceType == 'purchase_return')
            IconButton(
              onPressed: () async {
                await _database.forceAddSampleData();
                await _loadAvailableNames();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إعادة تحميل البيانات النموذجية')),
                  );
                }
              },
              icon: const Icon(Icons.refresh),
              tooltip: 'إعادة تحميل البيانات',
            ),
          TextButton(
            onPressed: _isLoading ? null : _saveInvoice,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: Colors.white,
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE8F5E8),
            ],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInvoiceHeader(isTablet),
                            SizedBox(height: isTablet ? 20 : 16),
                            _buildItemsSection(isTablet),
                            SizedBox(height: isTablet ? 20 : 16),
                            _buildTotalsSection(isTablet),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildInvoiceHeader(bool isTablet) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بيانات الفاتورة',
              style: TextStyle(
                fontSize: isTablet ? 18 : 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: isTablet ? 16 : 12),

            // Invoice number and date
            if (isTablet)
              Row(
                children: [
                  Expanded(child: _buildInvoiceNumberField()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDateField()),
                ],
              )
            else
              Column(
                children: [
                  _buildInvoiceNumberField(),
                  const SizedBox(height: 12),
                  _buildDateField(),
                ],
              ),

            SizedBox(height: isTablet ? 16 : 12),

            // Supplier/Customer name
            _buildSupplierCustomerField(),

            SizedBox(height: isTablet ? 16 : 12),

            // Notes
            _buildNotesField(),

            SizedBox(height: isTablet ? 16 : 12),

            // Treasury selection
            _buildTreasurySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceNumberField() {
    return TextFormField(
      controller: _invoiceNumberController,
      decoration: InputDecoration(
        labelText: 'رقم الفاتورة',
        prefixIcon: const Icon(Icons.numbers),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال رقم الفاتورة';
        }
        return null;
      },
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: () => _selectDate(),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: 'التاريخ',
          prefixIcon: const Icon(Icons.calendar_today),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        child: Text(
          DateFormat('yyyy/MM/dd').format(_selectedDate),
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }

  Widget _buildSupplierCustomerField() {
    // For return invoices, show field with popup selector
    if (widget.invoiceType == 'sale_return' || widget.invoiceType == 'purchase_return') {
      return _ClickableTextField(
        controller: _supplierCustomerController,
        labelText: _getEntityLabel(),
        hintText: 'اختر ${_getEntityLabel()} أو اكتب اسم جديد',
        onTap: () => _showNameSelector(),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'يرجى إدخال ${_getEntityLabel()}';
          }
          return null;
        },
      );
    }

    // For regular invoices, show normal text field
    return TextFormField(
      controller: _supplierCustomerController,
      decoration: InputDecoration(
        labelText: _getEntityLabel(),
        prefixIcon: const Icon(Icons.person),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال ${_getEntityLabel()}';
        }
        return null;
      },
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: InputDecoration(
        labelText: 'ملاحظات (اختيارية)',
        prefixIcon: const Icon(Icons.note),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      maxLines: 2,
    );
  }

  Widget _buildTreasurySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Treasury link checkbox
        CheckboxListTile(
          title: const Text('ربط بالخزينة'),
          subtitle: const Text('ربط الفاتورة بخزينة لإنشاء معاملة مالية تلقائياً'),
          value: _linkToTreasury,
          onChanged: (value) {
            setState(() {
              _linkToTreasury = value ?? false;
              if (!_linkToTreasury) {
                _selectedTreasury = null;
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),

        // Treasury dropdown
        if (_linkToTreasury) ...[
          const SizedBox(height: 8),
          DropdownButtonFormField<Treasury>(
            value: _selectedTreasury,
            decoration: InputDecoration(
              labelText: 'اختر الخزينة',
              prefixIcon: const Icon(Icons.account_balance_wallet),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            items: _treasuries.map((treasury) {
              return DropdownMenuItem<Treasury>(
                value: treasury,
                child: Text(
                  '${treasury.name} (${NumberFormat('#,##0.00').format(treasury.finalBalance)})',
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
            onChanged: (Treasury? value) {
              setState(() {
                _selectedTreasury = value;
              });
            },
            validator: _linkToTreasury ? (value) {
              if (value == null) {
                return 'يرجى اختيار خزينة';
              }
              return null;
            } : null,
          ),
        ],
      ],
    );
  }

  Widget _buildItemsSection(bool isTablet) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'أصناف الفاتورة',
                  style: TextStyle(
                    fontSize: isTablet ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('إضافة صنف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: isTablet ? 16 : 12),

            if (_items.isEmpty)
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(isTablet ? 32 : 24),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.inventory_2_outlined,
                      size: isTablet ? 48 : 40,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: isTablet ? 12 : 8),
                    Text(
                      'لم يتم إضافة أي أصناف بعد',
                      style: TextStyle(
                        fontSize: isTablet ? 16 : 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: isTablet ? 8 : 6),
                    Text(
                      'اضغط على "إضافة صنف" لبدء إضافة الأصناف',
                      style: TextStyle(
                        fontSize: isTablet ? 12 : 10,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                separatorBuilder: (context, index) => SizedBox(height: isTablet ? 12 : 8),
                itemBuilder: (context, index) {
                  return _buildItemCard(_items[index], index, isTablet);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(InvoiceItem item, int index, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  item.itemName,
                  style: TextStyle(
                    fontSize: isTablet ? 14 : 12,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () => _editItem(index),
                    icon: const Icon(Icons.edit, size: 20),
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                  ),
                  IconButton(
                    onPressed: () => _removeItem(index),
                    icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
            ],
          ),

          SizedBox(height: isTablet ? 8 : 6),

          if (isTablet)
            Row(
              children: [
                Expanded(child: _buildItemDetail('الوحدة', item.unit)),
                Expanded(child: _buildItemDetail('الكمية', '${item.quantity}')),
                Expanded(child: _buildItemDetail('السعر', '${item.price} ريال')),
                if (item.discount > 0)
                  Expanded(child: _buildItemDetail('الخصم', '${item.discount} ريال')),
                Expanded(child: _buildItemDetail('الإجمالي', '${NumberFormat('#,##0.00').format(item.total)} ريال')),
              ],
            )
          else
            Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _buildItemDetail('الوحدة', item.unit)),
                    Expanded(child: _buildItemDetail('الكمية', '${item.quantity}')),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(child: _buildItemDetail('السعر', '${item.price} ريال')),
                    if (item.discount > 0)
                      Expanded(child: _buildItemDetail('الخصم', '${item.discount} ريال')),
                  ],
                ),
                SizedBox(height: 4),
                _buildItemDetail('الإجمالي', '${NumberFormat('#,##0.00').format(item.total)} ريال'),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildItemDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsSection(bool isTablet) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجماليات الفاتورة',
              style: TextStyle(
                fontSize: isTablet ? 18 : 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),

            SizedBox(height: isTablet ? 16 : 12),

            // Discount and Expenses
            if (isTablet)
              Row(
                children: [
                  Expanded(child: _buildDiscountField()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildExpensesField()),
                ],
              )
            else
              Column(
                children: [
                  _buildDiscountField(),
                  const SizedBox(height: 12),
                  _buildExpensesField(),
                ],
              ),

            SizedBox(height: isTablet ? 16 : 12),

            // Totals Display
            Container(
              padding: EdgeInsets.all(isTablet ? 16 : 12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildTotalRow('إجمالي الفاتورة:', _totalAmount),
                  if (_discount > 0)
                    _buildTotalRow('الخصم:', -_discount, color: Colors.red),
                  if (_expenses > 0)
                    _buildTotalRow('المصاريف:', _expenses, color: Colors.orange),
                  Divider(height: isTablet ? 16 : 12),
                  _buildTotalRow(
                    'صافي الفاتورة:',
                    _netAmount,
                    isTotal: true,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountField() {
    return TextFormField(
      controller: _discountController,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
      decoration: InputDecoration(
        labelText: 'الخصم (ريال)',
        prefixIcon: const Icon(Icons.discount),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      onChanged: (_) => setState(() {}),
    );
  }

  Widget _buildExpensesField() {
    return TextFormField(
      controller: _expensesController,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
      decoration: InputDecoration(
        labelText: 'المصاريف (ريال)',
        prefixIcon: const Icon(Icons.add_circle),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      onChanged: (_) => setState(() {}),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
          FutureBuilder<String>(
            future: CurrencyService.instance.formatAmount(amount),
            builder: (context, snapshot) {
              return Text(
                snapshot.data ?? CurrencyService.instance.formatForPdf(amount),
                style: TextStyle(
                  fontSize: isTotal ? 16 : 14,
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  color: color,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _addItem() {
    _showItemDialog();
  }

  void _editItem(int index) {
    _showItemDialog(item: _items[index], index: index);
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _showItemDialog({InvoiceItem? item, int? index}) {
    showDialog(
      context: context,
      builder: (context) => _ItemDialog(
        item: item,
        onSave: (newItem) {
          setState(() {
            if (index != null) {
              _items[index] = newItem;
            } else {
              _items.add(newItem);
            }
          });
        },
      ),
    );
  }

  Future<void> _showNameSelector() async {
    if (_availableNames.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد أسماء متاحة. اضغط على زر التحديث أولاً.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final selectedName = await showDialog<String>(
      context: context,
      builder: (context) => NameSelectorDialog(
        names: _availableNames,
        title: 'اختر ${_getEntityLabel()}',
        searchHint: 'ابحث عن ${_getEntityLabel()}...',
        prefixIcon: Icons.person,
      ),
    );

    debugPrint('Dialog returned with: $selectedName'); // Debug

    if (selectedName != null && selectedName.isNotEmpty) {
      debugPrint('Setting selected name: $selectedName'); // Debug
      setState(() {
        _supplierCustomerController.text = selectedName;
      });

      // Unfocus the field to prevent reopening the dialog
      if (mounted) {
        FocusScope.of(context).unfocus();
      }
      debugPrint('Name set and focus removed'); // Debug
    } else {
      debugPrint('No name selected or dialog cancelled'); // Debug
    }
  }

  Future<void> _linkInvoiceToTreasury(Invoice invoice, Treasury treasury) async {
    try {
      debugPrint('Linking invoice ${invoice.id} to treasury ${treasury.id}');

      // التحقق من صحة البيانات
      if (invoice.id == null) {
        throw Exception('معرف الفاتورة مطلوب');
      }

      if (treasury.id == null) {
        throw Exception('معرف الخزينة مطلوب');
      }

      // إنشاء معاملة مالية في الخزينة
      double income = 0.0;
      double expenses = 0.0;
      String description = '';

      // تحديد نوع المعاملة حسب نوع الفاتورة
      switch (invoice.type) {
        case 'sale':
          income = invoice.netAmount;
          description = 'دخل من فاتورة مبيعات رقم ${invoice.invoiceNumber}';
          break;
        case 'purchase':
          expenses = invoice.netAmount;
          description = 'مصروف فاتورة مشتريات رقم ${invoice.invoiceNumber}';
          break;
        case 'sale_return':
          expenses = invoice.netAmount;
          description = 'مصروف مرتجع مبيعات رقم ${invoice.invoiceNumber}';
          break;
        case 'purchase_return':
          income = invoice.netAmount;
          description = 'دخل من مرتجع مشتريات رقم ${invoice.invoiceNumber}';
          break;
        default:
          throw Exception('نوع فاتورة غير مدعوم: ${invoice.type}');
      }

      debugPrint('Creating treasury transaction: income=$income, expenses=$expenses');

      final transaction = TreasuryTransaction(
        treasuryId: treasury.id!,
        description: description,
        income: income,
        expenses: expenses,
        notes: 'تم إنشاؤها تلقائياً من الفاتورة - ${invoice.supplierOrCustomerName}',
        date: invoice.date,
        invoiceId: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
      );

      final transactionId = await _treasuryDatabase.insertTreasuryTransaction(transaction.toMap());
      debugPrint('Treasury transaction created with ID: $transactionId');

      // تحديث الفاتورة بمعرف المعاملة
      await _database.markInvoiceAsPaid(invoice.id!, transactionId);
      debugPrint('Invoice marked as paid');

    } catch (e) {
      debugPrint('Error linking invoice to treasury: $e');
      // إعادة رفع الخطأ ليتم التعامل معه في دالة الحفظ
      rethrow;
    }
  }

  Future<void> _showAddTreasuryDialog() async {
    if (!mounted) return;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // منع إغلاق الحوار بالضغط خارجه
      builder: (context) => _AddTreasuryDialog(),
    );

    if (result == true) {
      // إعادة تحميل الخزائن بعد إضافة خزينة جديدة
      await _loadTreasuries();
    }
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة صنف واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final invoice = Invoice(
        id: widget.invoice?.id,
        invoiceNumber: _invoiceNumberController.text,
        date: _selectedDate,
        supplierOrCustomerName: _supplierCustomerController.text,
        type: widget.invoiceType,
        items: _items,
        totalAmount: _totalAmount,
        discount: _discount,
        expenses: _expenses,
        netAmount: _netAmount,
        notes: _notesController.text,
        treasuryId: _linkToTreasury ? _selectedTreasury?.id : null,
        isPaid: false, // سيتم تحديثها عند الربط بالخزينة
        treasuryTransactionId: null,
      );

      int? invoiceId;
      if (_isEditing) {
        await _database.updateInvoice(invoice.id!, invoice.toMap());
        invoiceId = invoice.id;
      } else {
        invoiceId = await _database.insertInvoice(invoice.toMap());
      }

      // ربط الفاتورة بالخزينة إذا تم اختيار ذلك
      if (_linkToTreasury && _selectedTreasury != null && invoiceId != null) {
        // إنشاء نسخة من الفاتورة مع المعرف الصحيح
        final invoiceWithId = invoice.copyWith(id: invoiceId);
        await _linkInvoiceToTreasury(invoiceWithId, _selectedTreasury!);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'تم تحديث الفاتورة بنجاح' : 'تم حفظ الفاتورة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _ItemDialog extends StatefulWidget {
  final InvoiceItem? item;
  final Function(InvoiceItem) onSave;

  const _ItemDialog({required this.onSave, this.item});

  @override
  State<_ItemDialog> createState() => _ItemDialogState();
}

class _ItemDialogState extends State<_ItemDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _unitController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  final TextEditingController _discountController = TextEditingController(text: '0');

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _nameController.text = widget.item!.itemName;
      _unitController.text = widget.item!.unit;
      _quantityController.text = widget.item!.quantity.toString();
      _priceController.text = widget.item!.price.toString();
      _discountController.text = widget.item!.discount.toString();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _unitController.dispose();
    _quantityController.dispose();
    _priceController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  double get _quantity => double.tryParse(_quantityController.text) ?? 0.0;
  double get _price => double.tryParse(_priceController.text) ?? 0.0;
  double get _discount => double.tryParse(_discountController.text) ?? 0.0;
  double get _total => (_quantity * _price) - _discount;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return AlertDialog(
      title: Text(widget.item == null ? 'إضافة صنف' : 'تعديل صنف'),
      content: SizedBox(
        width: isTablet ? 500 : double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الصنف',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم الصنف';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 12),

                TextFormField(
                  controller: _unitController,
                  decoration: const InputDecoration(
                    labelText: 'الوحدة',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الوحدة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _quantityController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        decoration: const InputDecoration(
                          labelText: 'الكمية',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الكمية';
                          }
                          if (double.tryParse(value) == null || double.parse(value) <= 0) {
                            return 'يرجى إدخال كمية صحيحة';
                          }
                          return null;
                        },
                        onChanged: (_) => setState(() {}),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: TextFormField(
                        controller: _priceController,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                        decoration: const InputDecoration(
                          labelText: 'السعر',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال السعر';
                          }
                          if (double.tryParse(value) == null || double.parse(value) < 0) {
                            return 'يرجى إدخال سعر صحيح';
                          }
                          return null;
                        },
                        onChanged: (_) => setState(() {}),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                TextFormField(
                  controller: _discountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
                  decoration: InputDecoration(
                    labelText: 'الخصم (${CurrencyService.instance.getCurrentCurrencySymbol()})',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (_) => setState(() {}),
                ),

                const SizedBox(height: 16),

                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      FutureBuilder<String>(
                        future: CurrencyService.instance.formatAmount(_total),
                        builder: (context, snapshot) {
                          return Text(
                            'الإجمالي: ${snapshot.data ?? CurrencyService.instance.formatForPdf(_total)}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final item = InvoiceItem(
                itemName: _nameController.text,
                unit: _unitController.text,
                quantity: _quantity,
                price: _price,
                discount: _discount,
                total: _total,
              );
              widget.onSave(item);
              Navigator.of(context).pop();
            }
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}

class _ClickableTextField extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final VoidCallback onTap;
  final String? Function(String?)? validator;

  const _ClickableTextField({
    required this.controller,
    required this.labelText,
    required this.hintText,
    required this.onTap,
    this.validator,
  });

  @override
  State<_ClickableTextField> createState() => _ClickableTextFieldState();
}

class _ClickableTextFieldState extends State<_ClickableTextField> {
  final FocusNode _focusNode = FocusNode();
  bool _isTyping = false;
  bool _justSelected = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus && !_isTyping && !_justSelected) {
      // Show popup when field gets focus (but not when typing or just selected)
      Future.delayed(const Duration(milliseconds: 100), () {
        if (_focusNode.hasFocus && !_isTyping && !_justSelected) {
          widget.onTap();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      decoration: InputDecoration(
        labelText: widget.labelText,
        hintText: widget.hintText,
        prefixIcon: const Icon(Icons.person),
        suffixIcon: IconButton(
          onPressed: widget.onTap,
          icon: const Icon(Icons.arrow_drop_down),
          tooltip: 'اختر من القائمة',
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      validator: widget.validator,
      onChanged: (value) {
        _isTyping = true;
        // Reset typing flag after a delay
        Future.delayed(const Duration(milliseconds: 500), () {
          _isTyping = false;
        });
      },
      onTap: () {
        if (!_isTyping && !_justSelected) {
          _justSelected = true;
          widget.onTap();
          // Reset the flag after a delay
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted) {
              setState(() {
                _justSelected = false;
              });
            }
          });
        }
      },
    );
  }
}

class _AddTreasuryDialog extends StatefulWidget {
  @override
  State<_AddTreasuryDialog> createState() => _AddTreasuryDialogState();
}

class _AddTreasuryDialogState extends State<_AddTreasuryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController(text: '0');
  final _treasuryDatabase = UnifiedTreasuryService();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  Future<void> _saveTreasury() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final treasury = Treasury(
        name: _nameController.text.trim(),
        previousBalance: 0.0,
        currentBalance: double.tryParse(_balanceController.text) ?? 0.0,
        date: DateTime.now(),
      );

      await _treasuryDatabase.insertTreasury(treasury.toMap());

      if (mounted) {
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى نجاح الإضافة
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الخزينة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة خزينة جديدة'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'يجب إضافة خزينة واحدة على الأقل لربط الفواتير بها',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الخزينة',
                  prefixIcon: Icon(Icons.account_balance_wallet),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الخزينة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _balanceController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
                ],
                decoration: const InputDecoration(
                  labelText: 'الرصيد الابتدائي',
                  prefixIcon: Icon(Icons.monetization_on),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الرصيد الابتدائي';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () {
            Navigator.of(context).pop(false);
          },
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveTreasury,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إضافة'),
        ),
      ],
    );
  }
}