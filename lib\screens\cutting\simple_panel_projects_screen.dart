import 'package:flutter/material.dart';
import '../../models/panel_cutting_project.dart';
import 'add_edit_panel_project_screen.dart';

class SimplePanelProjectsScreen extends StatefulWidget {
  const SimplePanelProjectsScreen({super.key});

  @override
  State<SimplePanelProjectsScreen> createState() => _SimplePanelProjectsScreenState();
}

class _SimplePanelProjectsScreenState extends State<SimplePanelProjectsScreen> {
  List<PanelCuttingProject> _projects = [];

  @override
  void initState() {
    super.initState();
    _loadSampleProjects();
  }
  void _loadSampleProjects() {
    // إضافة مشاريع تجريبية
    _projects = [
      PanelCuttingProject(
        id: '1',
        projectName: 'P001',
        customerName: 'أحمد محمد',
        panelWidth: 2440,
        panelHeight: 1220,
        orderItems: [],
        notes: 'مشروع تقطيع ألواح خشبية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      PanelCuttingProject(
        id: '2',
        projectName: 'P002',
        customerName: 'فاطمة أحمد',
        panelWidth: 2440,
        panelHeight: 1220,
        orderItems: [],
        notes: 'ألواح فيبر للمطبخ',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مشاريع تقطيع الألواح',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: Column(
        children: [
          // إحصائيات سريعة
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.orange.shade50,
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المشاريع',
                    _projects.length.toString(),
                    Icons.folder,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'مشاريع اليوم',                    _projects.where((p) =>
                      p.createdAt.day == DateTime.now().day &&
                      p.createdAt.month == DateTime.now().month &&
                      p.createdAt.year == DateTime.now().year
                    ).length.toString(),
                    Icons.today,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ),

          // قائمة المشاريع
          Expanded(
            child: _projects.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.dashboard, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد مشاريع ألواح',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'اضغط على زر الإضافة لبدء مشروع جديد',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _projects.length,
                  itemBuilder: (context, index) => _buildProjectCard(_projects[index]),
                ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addNewProject,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('مشروع جديد'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProjectCard(PanelCuttingProject project) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _viewProject(project),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [                  Text(
                    project.projectName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'نشط',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.person, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    project.customerName,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
              const SizedBox(height: 8),              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ الإنشاء: ${project.createdAt.day}/${project.createdAt.month}/${project.createdAt.year}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.straighten, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    'أبعاد اللوح: ${project.panelWidth.toInt()} × ${project.panelHeight.toInt()} مم',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
              if (project.notes?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.note, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Expanded(                        child: Text(
                          project.notes ?? '',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _editProject(project),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('تعديل'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _deleteProject(project),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('حذف'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addNewProject() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddEditPanelProjectScreen(),
      ),
    ).then((_) {
      // تحديث القائمة عند العودة
      setState(() {});
    });
  }

  void _editProject(PanelCuttingProject project) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditPanelProjectScreen(project: project),
      ),
    ).then((_) {
      // تحديث القائمة عند العودة
      setState(() {});
    });
  }

  void _viewProject(PanelCuttingProject project) {
    // TODO: إضافة شاشة عرض المشروع
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض مشروع: ${project.projectName}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _deleteProject(PanelCuttingProject project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مشروع ${project.projectName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _projects.remove(project);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المشروع بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
