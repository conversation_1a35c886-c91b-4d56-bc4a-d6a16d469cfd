import 'package:flutter/material.dart';

class NameSelectorDialog extends StatefulWidget {
  final List<String> names;
  final String title;
  final String searchHint;
  final IconData? prefixIcon;

  const NameSelectorDialog({
    super.key,
    required this.names,
    required this.title,
    required this.searchHint,
    this.prefixIcon,
  });

  @override
  State<NameSelectorDialog> createState() => _NameSelectorDialogState();
}

class _NameSelectorDialogState extends State<NameSelectorDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredNames = [];

  @override
  void initState() {
    super.initState();
    _filteredNames = widget.names;
    _searchController.addListener(_filterNames);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterNames() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredNames = widget.names;
      } else {
        _filteredNames = widget.names
            .where((name) => name.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: isTablet ? 500 : double.infinity,
        height: isTablet ? 600 : MediaQuery.of(context).size.height * 0.8,
        padding: EdgeInsets.all(isTablet ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  widget.prefixIcon ?? Icons.person,
                  color: Theme.of(context).colorScheme.primary,
                  size: isTablet ? 28 : 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: isTablet ? 20 : 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  tooltip: 'إغلاق',
                ),
              ],
            ),

            SizedBox(height: isTablet ? 20 : 16),

            // Search field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: widget.searchHint,
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),

            SizedBox(height: isTablet ? 20 : 16),

            // Names list
            Expanded(
              child: _filteredNames.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: isTablet ? 64 : 48,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: isTablet ? 16 : 12),
                          Text(
                            'لا توجد نتائج',
                            style: TextStyle(
                              fontSize: isTablet ? 18 : 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: isTablet ? 8 : 6),
                          Text(
                            'جرب البحث بكلمات أخرى',
                            style: TextStyle(
                              fontSize: isTablet ? 14 : 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.separated(
                      itemCount: _filteredNames.length,
                      separatorBuilder: (context, index) => Divider(
                        height: 1,
                        color: Colors.grey[200],
                      ),
                      itemBuilder: (context, index) {
                        final name = _filteredNames[index];
                        return _NameItem(
                          name: name,
                          prefixIcon: widget.prefixIcon,
                          onTap: () {
                            debugPrint('Name item tapped: $name'); // Debug
                            Navigator.of(context).pop(name);
                            debugPrint('Navigator.pop called with: $name'); // Debug
                          },
                        );
                      },
                    ),
            ),

            SizedBox(height: isTablet ? 20 : 16),

            // Bottom buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        vertical: isTablet ? 16 : 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'إلغاء',
                      style: TextStyle(fontSize: isTablet ? 16 : 14),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _searchController.text.isNotEmpty
                        ? () => Navigator.of(context).pop(_searchController.text)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: isTablet ? 16 : 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'إضافة جديد',
                      style: TextStyle(fontSize: isTablet ? 16 : 14),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _NameItem extends StatefulWidget {
  final String name;
  final IconData? prefixIcon;
  final VoidCallback onTap;

  const _NameItem({
    required this.name,
    this.prefixIcon,
    required this.onTap,
  });

  @override
  State<_NameItem> createState() => _NameItemState();
}

class _NameItemState extends State<_NameItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            debugPrint('InkWell onTap called for: ${widget.name}'); // Debug
            widget.onTap();
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: _isHovered ? Colors.grey[100] : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 0.5,
              ),
            ),
          child: Row(
            children: [
              Icon(
                widget.prefixIcon ?? Icons.person,
                size: 20,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    ),
    );
  }
}
