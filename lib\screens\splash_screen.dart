import 'dart:async';
import 'package:flutter/material.dart';
import '../services/unified_invoice_service.dart';
import '../main.dart' as main_app;
import 'license_activation_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  Timer? _navigationTimer;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutBack,
      ),
    );

    _animationController.forward();

    // Initialize sample data
    _initializeSampleData();

    // تأخير للانتقال إلى الشاشة المناسبة
    _navigationTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        _navigateToNextScreen();
      }
    });
  }
  Future<void> _initializeSampleData() async {
    try {
      final database = UnifiedInvoiceService.instance;
      await database.addSampleData();
    } catch (e) {
      // Ignore errors during sample data initialization
      debugPrint('Error initializing sample data: $e');
    }
  }

  void _navigateToNextScreen() {
    // الحصول على نتيجة فحص الترخيص
    final licenseResult = main_app.getLicenseCheckResult();

    if (licenseResult != null &&
        licenseResult['success'] == true &&
        licenseResult['is_current_device'] == true) {
      // السيريال متطابق، الانتقال إلى الشاشة الرئيسية
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      // السيريال غير متطابق أو فشل فحص الترخيص، عرض شاشة الترخيص
      final currentSerial = licenseResult?['current_device_serial'] ?? 'غير متوفر';
      final databaseSerial = licenseResult?['serial'];
      final message = licenseResult?['message'] ?? 'يرجى تفعيل ترخيص التطبيق';

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => LicenseActivationScreen(
            currentDeviceSerial: currentSerial,
            databaseSerial: databaseSerial,
            message: message,
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _navigationTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF2E7D32),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF1B5E20),
            ],
          ),
        ),
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.handyman,
                          size: 60,
                          color: Color(0xFF2E7D32),
                        ),
                      ),
                      const SizedBox(height: 40),
                      const Text(
                        'المساعد الذكي',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1.2,
                        ),
                      ),
                      const SizedBox(height: 10),
                      const Text(
                        'لخدمات المطابخ والألومنيوم',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white70,
                        ),
                      ),
                      const SizedBox(height: 40),
                      const SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 3,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
