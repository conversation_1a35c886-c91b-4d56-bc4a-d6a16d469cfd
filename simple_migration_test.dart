import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'dart:io';

/// اختبار مبسط لنقل البيانات من قواعد البيانات المنفصلة إلى قاعدة البيانات الموحدة
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🚀 بدء اختبار نقل البيانات المبسط...\n');

  try {
    // الحصول على مسار قواعد البيانات
    final databasesPath = await getDatabasesPath();
    print('📁 مسار قواعد البيانات: $databasesPath');
    
    // فتح قاعدة البيانات الموحدة
    final unifiedDbPath = join(databasesPath, 'uptime_unified_database_v4.db');
    final unifiedDb = await openDatabase(unifiedDbPath);
    
    print('✅ تم فتح قاعدة البيانات الموحدة');
    
    // عرض حالة قاعدة البيانات الموحدة قبل النقل
    await _showDatabaseStatus(unifiedDb, 'قبل النقل');
    
    // نقل البيانات من قواعد البيانات المنفصلة
    final migrationResults = <String, Map<String, dynamic>>{};
    
    // نقل بيانات التقطيع والمشاريع
    migrationResults['cutting'] = await _migrateCuttingData(databasesPath, unifiedDb);
    
    // نقل بيانات الفواتير
    migrationResults['invoices'] = await _migrateInvoiceData(databasesPath, unifiedDb);
    
    // نقل بيانات الخزينة
    migrationResults['treasury'] = await _migrateTreasuryData(databasesPath, unifiedDb);
    
    // نقل بيانات الألومنيوم
    migrationResults['aluminum'] = await _migrateAluminumData(databasesPath, unifiedDb);
    
    // عرض نتائج النقل
    print('\n📋 نتائج عملية النقل:');
    migrationResults.forEach((section, result) {
      if (result['success']) {
        print('   ✅ $section: نجح');
        result.forEach((key, value) {
          if (key != 'success' && key != 'error') {
            print('      📊 $key: $value');
          }
        });
      } else {
        print('   ❌ $section: فشل - ${result['error']}');
      }
    });
    
    // عرض حالة قاعدة البيانات الموحدة بعد النقل
    await _showDatabaseStatus(unifiedDb, 'بعد النقل');
    
    // إغلاق قاعدة البيانات
    await unifiedDb.close();
    
    print('\n✅ تم اكتمال عملية النقل بنجاح!');
    
  } catch (e) {
    print('❌ خطأ في عملية النقل: $e');
  }
}

/// عرض حالة قاعدة البيانات
Future<void> _showDatabaseStatus(Database db, String title) async {
  print('\n📊 حالة قاعدة البيانات $title:');
  
  // الحصول على قائمة الجداول
  final tables = await db.rawQuery(
    "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
  );
  
  final tableNames = tables.map((table) => table['name'] as String).toList();
  
  // حساب عدد السجلات في كل جدول
  final tableCounts = <String, int>{};
  for (final tableName in tableNames) {
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
    tableCounts[tableName] = result.first['count'] as int;
  }
  
  print('   📊 عدد الجداول: ${tableNames.length}');
  print('   📈 إجمالي السجلات: ${tableCounts.values.fold(0, (sum, count) => sum + count)}');
  
  // تجميع الجداول حسب الأقسام
  final sections = {
    '📋 الفواتير والعملاء': ['customers', 'suppliers', 'invoices', 'invoice_items'],
    '🔧 الألومنيوم': ['profile_series', 'aluminum_profiles', 'aluminum_quotations', 'aluminum_quotation_items', 'hinge_designs', 'sliding_designs'],
    '🔩 uPVC': ['upvc_profile_series', 'upvc_profiles', 'upvc_quotations', 'upvc_quotation_items', 'upvc_hinge_designs', 'upvc_sliding_designs'],
    '✂️ التقطيع والمشاريع': ['cutting_projects', 'order_items', 'cutting_measurements'],
    '💰 الخزينة': ['treasuries', 'treasury_transactions'],
    '📋 إدارة المهام': ['task_categories', 'tasks'],
  };
  
  for (final section in sections.entries) {
    final sectionTables = section.value.where((table) => tableCounts.containsKey(table)).toList();
    if (sectionTables.isNotEmpty) {
      print('      ${section.key}:');
      for (final tableName in sectionTables) {
        final count = tableCounts[tableName] ?? 0;
        print('         📄 $tableName: $count سجل');
      }
    }
  }
}

/// نقل بيانات التقطيع والمشاريع
Future<Map<String, dynamic>> _migrateCuttingData(String databasesPath, Database unifiedDb) async {
  print('📋 نقل بيانات التقطيع والمشاريع...');
  
  try {
    final oldDbPath = join(databasesPath, 'uptime_smart_assist.db');
    final oldDbFile = File(oldDbPath);
    
    if (!await oldDbFile.exists()) {
      return {'success': false, 'error': 'قاعدة البيانات الأصلية غير موجودة'};
    }
    
    final oldDb = await openDatabase(oldDbPath);
    
    int projectsCount = 0;
    int itemsCount = 0;
    int measurementsCount = 0;
    
    // نقل مشاريع التقطيع
    final projects = await oldDb.query('cutting_projects');
    for (final project in projects) {
      await unifiedDb.insert('cutting_projects', project, conflictAlgorithm: ConflictAlgorithm.replace);
      projectsCount++;
      
      // نقل عناصر المشروع
      final items = await oldDb.query('order_items', 
        where: 'project_id = ?', 
        whereArgs: [project['id']]
      );
      for (final item in items) {
        await unifiedDb.insert('order_items', item, conflictAlgorithm: ConflictAlgorithm.replace);
        itemsCount++;
        
        // نقل قياسات التقطيع
        final measurements = await oldDb.query('cutting_measurements',
          where: 'order_item_id = ?',
          whereArgs: [item['id']]
        );
        for (final measurement in measurements) {
          await unifiedDb.insert('cutting_measurements', measurement, conflictAlgorithm: ConflictAlgorithm.replace);
          measurementsCount++;
        }
      }
    }
    
    await oldDb.close();
    
    print('   ✅ تم نقل بيانات التقطيع: $projectsCount مشروع، $itemsCount عنصر، $measurementsCount قياس');
    
    return {
      'success': true,
      'projects': projectsCount,
      'items': itemsCount,
      'measurements': measurementsCount,
    };
    
  } catch (e) {
    print('   ❌ خطأ في نقل بيانات التقطيع: $e');
    return {'success': false, 'error': e.toString()};
  }
}

/// نقل بيانات الفواتير والعملاء
Future<Map<String, dynamic>> _migrateInvoiceData(String databasesPath, Database unifiedDb) async {
  print('📋 نقل بيانات الفواتير والعملاء...');
  
  try {
    final oldDbPath = join(databasesPath, 'invoices_database_v2.db');
    final oldDbFile = File(oldDbPath);
    
    if (!await oldDbFile.exists()) {
      return {'success': false, 'error': 'قاعدة بيانات الفواتير غير موجودة'};
    }
    
    final oldDb = await openDatabase(oldDbPath);
    
    int customersCount = 0;
    int suppliersCount = 0;
    int invoicesCount = 0;
    int itemsCount = 0;
    
    // نقل العملاء
    final customers = await oldDb.query('customers');
    for (final customer in customers) {
      await unifiedDb.insert('customers', customer, conflictAlgorithm: ConflictAlgorithm.replace);
      customersCount++;
    }
    
    // نقل الموردين
    final suppliers = await oldDb.query('suppliers');
    for (final supplier in suppliers) {
      await unifiedDb.insert('suppliers', supplier, conflictAlgorithm: ConflictAlgorithm.replace);
      suppliersCount++;
    }
    
    // نقل الفواتير
    final invoices = await oldDb.query('invoices');
    for (final invoice in invoices) {
      // إضافة الأعمدة المفقودة
      final invoiceData = Map<String, dynamic>.from(invoice);
      invoiceData['created_at'] = DateTime.now().millisecondsSinceEpoch;
      invoiceData['updated_at'] = DateTime.now().millisecondsSinceEpoch;

      await unifiedDb.insert('invoices', invoiceData, conflictAlgorithm: ConflictAlgorithm.replace);
      invoicesCount++;

      // نقل عناصر الفاتورة
      final items = await oldDb.query('invoice_items',
        where: 'invoiceId = ?',
        whereArgs: [invoice['id']]
      );
      for (final item in items) {
        await unifiedDb.insert('invoice_items', item, conflictAlgorithm: ConflictAlgorithm.replace);
        itemsCount++;
      }
    }
    
    await oldDb.close();
    
    print('   ✅ تم نقل بيانات الفواتير: $customersCount عميل، $suppliersCount مورد، $invoicesCount فاتورة، $itemsCount عنصر');
    
    return {
      'success': true,
      'customers': customersCount,
      'suppliers': suppliersCount,
      'invoices': invoicesCount,
      'items': itemsCount,
    };
    
  } catch (e) {
    print('   ❌ خطأ في نقل بيانات الفواتير: $e');
    return {'success': false, 'error': e.toString()};
  }
}

/// نقل بيانات الخزينة
Future<Map<String, dynamic>> _migrateTreasuryData(String databasesPath, Database unifiedDb) async {
  print('💰 نقل بيانات الخزينة...');
  
  try {
    final oldDbPath = join(databasesPath, 'uptime_treasury_v2.db');
    final oldDbFile = File(oldDbPath);
    
    if (!await oldDbFile.exists()) {
      return {'success': false, 'error': 'قاعدة بيانات الخزينة غير موجودة'};
    }
    
    final oldDb = await openDatabase(oldDbPath);
    
    int treasuriesCount = 0;
    int transactionsCount = 0;
    
    // نقل الخزائن
    final treasuries = await oldDb.query('treasuries');
    for (final treasury in treasuries) {
      await unifiedDb.insert('treasuries', treasury, conflictAlgorithm: ConflictAlgorithm.replace);
      treasuriesCount++;
      
      // نقل معاملات الخزينة
      final transactions = await oldDb.query('treasury_transactions',
        where: 'treasury_id = ?',
        whereArgs: [treasury['id']]
      );
      for (final transaction in transactions) {
        await unifiedDb.insert('treasury_transactions', transaction, conflictAlgorithm: ConflictAlgorithm.replace);
        transactionsCount++;
      }
    }
    
    await oldDb.close();
    
    print('   ✅ تم نقل بيانات الخزينة: $treasuriesCount خزينة، $transactionsCount معاملة');
    
    return {
      'success': true,
      'treasuries': treasuriesCount,
      'transactions': transactionsCount,
    };
    
  } catch (e) {
    print('   ❌ خطأ في نقل بيانات الخزينة: $e');
    return {'success': false, 'error': e.toString()};
  }
}

/// نقل بيانات الألومنيوم
Future<Map<String, dynamic>> _migrateAluminumData(String databasesPath, Database unifiedDb) async {
  print('🔧 نقل بيانات الألومنيوم...');
  
  try {
    int profilesCount = 0;
    int quotationsCount = 0;
    int quotationItemsCount = 0;
    int hingeDesignsCount = 0;
    int slidingDesignsCount = 0;
    
    // نقل قطاعات الألومنيوم
    final profilesDbPath = join(databasesPath, 'aluminum_profiles.db');
    final profilesDbFile = File(profilesDbPath);
    
    if (await profilesDbFile.exists()) {
      final profilesDb = await openDatabase(profilesDbPath);
      final profiles = await profilesDb.query('aluminum_profiles');
      for (final profile in profiles) {
        await unifiedDb.insert('aluminum_profiles', profile, conflictAlgorithm: ConflictAlgorithm.replace);
        profilesCount++;
      }
      await profilesDb.close();
    }
    
    // نقل عروض أسعار الألومنيوم
    final quotationsDbPath = join(databasesPath, 'aluminum_quotations.db');
    final quotationsDbFile = File(quotationsDbPath);
    
    if (await quotationsDbFile.exists()) {
      final quotationsDb = await openDatabase(quotationsDbPath);
      
      final quotations = await quotationsDb.query('aluminum_quotations');
      for (final quotation in quotations) {
        await unifiedDb.insert('aluminum_quotations', quotation, conflictAlgorithm: ConflictAlgorithm.replace);
        quotationsCount++;
        
        // نقل عناصر عرض السعر
        final items = await quotationsDb.query('aluminum_quotation_items',
          where: 'quotation_id = ?',
          whereArgs: [quotation['id']]
        );
        for (final item in items) {
          await unifiedDb.insert('aluminum_quotation_items', item, conflictAlgorithm: ConflictAlgorithm.replace);
          quotationItemsCount++;
        }
      }
      await quotationsDb.close();
    }
    
    // نقل تصاميم الألومنيوم
    final designsDbPath = join(databasesPath, 'aluminum_designs.db');
    final designsDbFile = File(designsDbPath);
    
    if (await designsDbFile.exists()) {
      final designsDb = await openDatabase(designsDbPath);
      
      final hingeDesigns = await designsDb.query('hinge_designs');
      for (final design in hingeDesigns) {
        await unifiedDb.insert('hinge_designs', design, conflictAlgorithm: ConflictAlgorithm.replace);
        hingeDesignsCount++;
      }
      
      final slidingDesigns = await designsDb.query('sliding_designs');
      for (final design in slidingDesigns) {
        await unifiedDb.insert('sliding_designs', design, conflictAlgorithm: ConflictAlgorithm.replace);
        slidingDesignsCount++;
      }
      
      await designsDb.close();
    }
    
    print('   ✅ تم نقل بيانات الألومنيوم: $profilesCount قطاع، $quotationsCount عرض سعر، $hingeDesignsCount تصميم مفصلي، $slidingDesignsCount تصميم سحاب');
    
    return {
      'success': true,
      'profiles': profilesCount,
      'quotations': quotationsCount,
      'quotation_items': quotationItemsCount,
      'hinge_designs': hingeDesignsCount,
      'sliding_designs': slidingDesignsCount,
    };
    
  } catch (e) {
    print('   ❌ خطأ في نقل بيانات الألومنيوم: $e');
    return {'success': false, 'error': e.toString()};
  }
}
