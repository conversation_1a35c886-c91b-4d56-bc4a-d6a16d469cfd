import 'lib/services/mysql_service.dart';

/// اختبار إنشاء جميع الجداول (الفواتير + الألومنيوم)
void main() async {
  print('🏗️ اختبار إنشاء جميع الجداول في قاعدة البيانات السحابية\n');

  try {
    final mysql = MySQLService.instance;
    
    // الخطوة 1: اختبار الاتصال
    print('🔌 الخطوة 1: اختبار الاتصال بقاعدة البيانات...');
    final connected = await mysql.testConnection();
    
    if (!connected) {
      print('❌ فشل الاتصال بقاعدة البيانات');
      print('   تأكد من إعدادات الاتصال في MySQLService');
      return;
    }
    print('✅ تم الاتصال بنجاح\n');

    // الخطوة 2: إنشاء الجداول
    print('🏗️ الخطوة 2: إنشاء جميع الجداول...');
    final success = await mysql.createTables();
    
    if (success) {
      print('✅ تم إنشاء جميع الجداول بنجاح!\n');
      
      // الخطوة 3: التحقق من الجداول المنشأة
      print('🔍 الخطوة 3: التحقق من الجداول المنشأة...');
      await verifyCreatedTables(mysql);
      
    } else {
      print('❌ فشل في إنشاء الجداول');
    }

  } catch (e) {
    print('❌ خطأ في اختبار إنشاء الجداول: $e');
  } finally {
    // قطع الاتصال
    await MySQLService.instance.disconnect();
    print('\n🔌 تم قطع الاتصال بقاعدة البيانات');
  }

  print('\n🎉 انتهى اختبار إنشاء الجداول');
}

/// التحقق من الجداول المنشأة
Future<void> verifyCreatedTables(MySQLService mysql) async {
  try {
    // قائمة الجداول المتوقعة
    final expectedTables = [
      // جداول الفواتير
      'customers',
      'invoices',
      'invoice_items',
      'payments',
      'subscriptions',

      // جداول الألومنيوم
      'profile_series',
      'aluminum_profiles',
      'aluminum_profile_series',
      'aluminum_quotations',
      'aluminum_quotation_items',
      'hinge_designs',
      'sliding_designs',

      // جداول التقطيع والمشاريع
      'cutting_projects',
      'order_items',
      'cutting_measurements',
      'panel_measurements',

      // جداول الخزينة
      'treasuries',
      'treasury_transactions',

      // جداول إدارة المهام
      'task_categories',
      'tasks',

      // جداول الموردين
      'suppliers',

      // جداول مقايسات UPVC
      'upvc_quotations',
      'upvc_quotation_items',
    ];

    // الحصول على قائمة الجداول الموجودة
    final result = await mysql.select('SHOW TABLES');
    final existingTables = result.map((row) => row.values.first.toString()).toList();
    
    print('   📊 الجداول الموجودة في قاعدة البيانات:');
    
    // فحص كل جدول متوقع
    final foundTables = <String>[];
    final missingTables = <String>[];
    
    for (final table in expectedTables) {
      if (existingTables.contains(table)) {
        foundTables.add(table);
        print('   ✅ $table');
      } else {
        missingTables.add(table);
        print('   ❌ $table (مفقود)');
      }
    }
    
    // عرض الملخص
    print('\n   📈 ملخص النتائج:');
    print('   - الجداول الموجودة: ${foundTables.length}');
    print('   - الجداول المفقودة: ${missingTables.length}');
    print('   - المجموع المتوقع: ${expectedTables.length}');
    
    if (missingTables.isEmpty) {
      print('   🎉 تم إنشاء جميع الجداول المطلوبة بنجاح!');
    } else {
      print('   ⚠️ بعض الجداول مفقودة: ${missingTables.join(', ')}');
    }

    // فحص تفصيلي لجداول الألومنيوم
    print('\n   🔍 فحص تفصيلي لجداول الألومنيوم:');
    await checkAluminumTablesStructure(mysql);

  } catch (e) {
    print('   ❌ خطأ في التحقق من الجداول: $e');
  }
}

/// فحص هيكل جداول الألومنيوم
Future<void> checkAluminumTablesStructure(MySQLService mysql) async {
  try {
    final allTables = [
      // جداول الألومنيوم
      'profile_series',
      'aluminum_profiles',
      'aluminum_profile_series',
      'aluminum_quotations',
      'aluminum_quotation_items',
      'hinge_designs',
      'sliding_designs',

      // جداول التقطيع والمشاريع
      'cutting_projects',
      'order_items',
      'cutting_measurements',
      'panel_measurements',

      // جداول الخزينة
      'treasuries',
      'treasury_transactions',

      // جداول إدارة المهام
      'task_categories',
      'tasks',

      // جداول الموردين
      'suppliers',

      // جداول مقايسات UPVC
      'upvc_quotations',
      'upvc_quotation_items',
    ];

    for (final table in allTables) {
      try {
        // فحص هيكل الجدول
        final structure = await mysql.select('DESCRIBE $table');
        print('   📋 $table (${structure.length} عمود):');
        
        // عرض أهم الأعمدة
        final importantColumns = structure.take(5).map((row) => row['Field']).toList();
        print('      الأعمدة: ${importantColumns.join(', ')}${structure.length > 5 ? '...' : ''}');
        
        // فحص عدد السجلات
        final count = await mysql.select('SELECT COUNT(*) as count FROM $table');
        final recordCount = count.first['count'];
        print('      السجلات: $recordCount');
        
      } catch (e) {
        print('   ❌ خطأ في فحص $table: $e');
      }
    }

    // فحص العلاقات الخارجية
    print('\n   🔗 فحص العلاقات الخارجية:');
    await checkForeignKeys(mysql);

  } catch (e) {
    print('   ❌ خطأ في فحص هيكل جداول الألومنيوم: $e');
  }
}

/// فحص العلاقات الخارجية
Future<void> checkForeignKeys(MySQLService mysql) async {
  try {
    // فحص العلاقات في جدول aluminum_profiles
    final profileConstraints = await mysql.select('''
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'aluminum_profiles' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    ''');

    if (profileConstraints.isNotEmpty) {
      print('   ✅ aluminum_profiles له ${profileConstraints.length} علاقة خارجية');
      for (final constraint in profileConstraints) {
        print('      - ${constraint['COLUMN_NAME']} → ${constraint['REFERENCED_TABLE_NAME']}.${constraint['REFERENCED_COLUMN_NAME']}');
      }
    } else {
      print('   ⚠️ aluminum_profiles ليس له علاقات خارجية');
    }

    // فحص العلاقات في جدول aluminum_profile_series
    final linkConstraints = await mysql.select('''
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'aluminum_profile_series' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    ''');

    if (linkConstraints.isNotEmpty) {
      print('   ✅ aluminum_profile_series له ${linkConstraints.length} علاقة خارجية');
      for (final constraint in linkConstraints) {
        print('      - ${constraint['COLUMN_NAME']} → ${constraint['REFERENCED_TABLE_NAME']}.${constraint['REFERENCED_COLUMN_NAME']}');
      }
    } else {
      print('   ⚠️ aluminum_profile_series ليس له علاقات خارجية');
    }

  } catch (e) {
    print('   ❌ خطأ في فحص العلاقات الخارجية: $e');
  }
}

/// اختبار سريع لإدراج بيانات تجريبية
Future<void> testSampleDataInsertion(MySQLService mysql) async {
  print('\n🧪 اختبار إدراج بيانات تجريبية...');
  
  try {
    // إدراج مجموعة قطاعات تجريبية
    await mysql.execute('''
      INSERT IGNORE INTO profile_series 
      (name, code, type, description, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', [
      'اختبار سوناتا 45',
      'TEST45',
      'hinge',
      'مجموعة اختبار',
      1,
      DateTime.now().millisecondsSinceEpoch,
      DateTime.now().millisecondsSinceEpoch,
    ]);

    // التحقق من الإدراج
    final inserted = await mysql.select('SELECT * FROM profile_series WHERE code = ?', ['TEST45']);
    
    if (inserted.isNotEmpty) {
      print('✅ تم إدراج بيانات تجريبية بنجاح');
      print('   📋 المجموعة: ${inserted.first['name']} (${inserted.first['code']})');
      
      // حذف البيانات التجريبية
      await mysql.execute('DELETE FROM profile_series WHERE code = ?', ['TEST45']);
      print('✅ تم حذف البيانات التجريبية');
    } else {
      print('❌ فشل إدراج البيانات التجريبية');
    }

  } catch (e) {
    print('❌ خطأ في اختبار إدراج البيانات: $e');
  }
}

/// عرض إحصائيات سريعة
Future<void> showQuickStats(MySQLService mysql) async {
  print('\n📊 إحصائيات سريعة:');
  
  try {
    // إحصائيات الجداول
    final tableStats = await mysql.select('''
      SELECT 
        TABLE_NAME,
        TABLE_ROWS,
        DATA_LENGTH,
        INDEX_LENGTH
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME IN (
        'profile_series', 'aluminum_profiles', 'aluminum_profile_series',
        'aluminum_quotations', 'aluminum_quotation_items',
        'hinge_designs', 'sliding_designs'
      )
      ORDER BY TABLE_NAME
    ''');

    if (tableStats.isNotEmpty) {
      print('   📋 إحصائيات جداول الألومنيوم:');
      for (final stat in tableStats) {
        final tableName = stat['TABLE_NAME'];
        final rows = stat['TABLE_ROWS'] ?? 0;
        final dataSize = ((stat['DATA_LENGTH'] ?? 0) / 1024).round();
        print('   - $tableName: $rows سجل، ${dataSize}KB');
      }
    }

  } catch (e) {
    print('   ❌ خطأ في عرض الإحصائيات: $e');
  }
}
