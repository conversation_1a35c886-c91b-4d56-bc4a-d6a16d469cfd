import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/aluminum_quotation.dart';
import '../../services/unified_aluminum_service.dart';
import 'add_edit_aluminum_quotation_screen.dart';
import 'aluminum_quotation_details_screen.dart';
import 'aluminum_settings_screen.dart';

class AluminumQuotationsScreen extends StatefulWidget {
  const AluminumQuotationsScreen({super.key});

  @override
  State<AluminumQuotationsScreen> createState() => _AluminumQuotationsScreenState();
}

class _AluminumQuotationsScreenState extends State<AluminumQuotationsScreen> {
  final UnifiedAluminumService _aluminumService = UnifiedAluminumService();
  final TextEditingController _searchController = TextEditingController();
  List<AluminumQuotation> _quotations = [];
  List<AluminumQuotation> _filteredQuotations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadQuotations();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadQuotations() async {
    setState(() => _isLoading = true);
    try {
      final quotationsData = await _aluminumService.getAllQuotations();
      final quotations = quotationsData.map((data) => AluminumQuotation.fromMap(data)).toList();
      setState(() {
        _quotations = quotations;
        _filteredQuotations = quotations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المقايسات: $e')),
        );
      }
    }
  }

  void _filterQuotations(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredQuotations = _quotations;
      } else {
        _filteredQuotations = _quotations.where((quotation) {
          return quotation.quotationNumber.toLowerCase().contains(query.toLowerCase()) ||
                 quotation.clientName.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  Future<void> _deleteQuotation(AluminumQuotation quotation) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المقايسة رقم ${quotation.quotationNumber}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _aluminumService.deleteQuotation(quotation.id!);
        _loadQuotations();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المقايسة بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف المقايسة: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.aluminumQuotations),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AluminumSettingsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.settings),
            tooltip: localizations.aluminumSettings,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Column(
          children: [
            // Search Bar
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                onChanged: _filterQuotations,
                decoration: InputDecoration(
                  hintText: 'البحث في المقايسات...',
                  prefixIcon: const Icon(Icons.search),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),

            // Quotations List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredQuotations.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.description_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد مقايسات',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredQuotations.length,
                          itemBuilder: (context, index) {
                            final quotation = _filteredQuotations[index];
                            return _buildQuotationCard(quotation, isTablet);
                          },
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditAluminumQuotationScreen(),
            ),
          );
          if (result == true) {
            _loadQuotations();
          }
        },
        icon: const Icon(Icons.add),
        label: Text(localizations.addQuotation),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildQuotationCard(AluminumQuotation quotation, bool isTablet) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AluminumQuotationDetailsScreen(quotation: quotation),
            ),
          );
          if (result == true) {
            _loadQuotations();
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: const Color(0xFF607D8B),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      quotation.quotationNumber,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Spacer(),
                  PopupMenuButton<String>(
                    onSelected: (value) async {
                      if (value == 'edit') {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AddEditAluminumQuotationScreen(quotation: quotation),
                          ),
                        );
                        if (result == true) {
                          _loadQuotations();
                        }
                      } else if (value == 'delete') {
                        _deleteQuotation(quotation);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                      const PopupMenuItem(value: 'delete', child: Text('حذف')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                quotation.clientName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    '${quotation.quotationDate.day}/${quotation.quotationDate.month}/${quotation.quotationDate.year}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
              if (quotation.clientPhone.isNotEmpty) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Text(
                      quotation.clientPhone,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
