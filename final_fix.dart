import 'dart:io';

void main() async {
  print('الإصلاح النهائي للأخطاء...');
  
  // إصلاح أخطاء $1 المتبقية
  await fixRemainingDollarErrors();
  
  // إزالة الاستيرادات غير الضرورية
  await removeUnnecessaryImports();
  
  // إصلاح أخطاء أخرى
  await fixMiscErrors();
  
  print('تم الانتهاء من الإصلاح النهائي!');
}

Future<void> fixRemainingDollarErrors() async {
  print('إصلاح أخطاء \$1 المتبقية...');
  
  final filesToFix = [
    'lib/screens/accounting/customers_screen.dart',
    'lib/screens/accounting/suppliers_screen.dart',
    'lib/screens/accounting/reports_screen.dart',
    'lib/screens/cutting/panel_cutting_optimization_screen.dart',
    'lib/screens/cutting/panel_cutting_projects_screen.dart',
    'lib/screens/cutting/panel_cutting_screen.dart',
    'lib/screens/cutting/panel_optimization_screen.dart',
    'lib/screens/cutting/panel_project_view_screen.dart',
    'lib/screens/cutting/simple_panel_projects_screen.dart',
    'lib/screens/mysql_test_screen_simple.dart',
    'lib/screens/treasury/treasury_screen.dart',
    'lib/screens/treasury/treasury_summary_screen.dart',
  ];

  for (String filePath in filesToFix) {
    try {
      final file = File(filePath);
      if (!await file.exists()) continue;

      String content = await file.readAsString();
      
      // إصلاح جميع حالات $1
      content = content.replaceAll('\$1', '0.3');
      
      await file.writeAsString(content);
      print('تم إصلاح \$1 في: $filePath');
    } catch (e) {
      print('خطأ في إصلاح \$1 في $filePath: $e');
    }
  }
}

Future<void> removeUnnecessaryImports() async {
  print('إزالة الاستيرادات غير الضرورية...');
  
  final filesToFix = [
    'lib/screens/accounting/accounting_invoices_screen.dart',
    'lib/screens/accounting/add_edit_invoice_screen.dart',
    'lib/screens/accounting/customer_account_details_screen.dart',
    'lib/screens/accounting/supplier_account_details_screen.dart',
    'lib/screens/aluminum/aluminum_settings_screen.dart',
    'lib/screens/cutting/add_edit_panel_project_screen.dart',
    'lib/screens/database_config_screen.dart',
    'lib/screens/splash_screen.dart',
    'lib/services/pdf_export_service.dart',
    'lib/widgets/name_selector_dialog.dart',
    'lib/widgets/searchable_dropdown.dart',
  ];

  for (String filePath in filesToFix) {
    try {
      final file = File(filePath);
      if (!await file.exists()) continue;

      String content = await file.readAsString();
      
      // إزالة الاستيراد غير الضروري
      content = content.replaceAll(
        "import 'package:flutter/foundation.dart';\n",
        ''
      );
      
      await file.writeAsString(content);
      print('تم إزالة الاستيراد غير الضروري من: $filePath');
    } catch (e) {
      print('خطأ في إزالة الاستيراد من $filePath: $e');
    }
  }
}

Future<void> fixMiscErrors() async {
  print('إصلاح أخطاء متنوعة...');
  
  // إصلاح use_super_parameters
  await fixSuperParameters();
  
  // إصلاح المتغيرات غير المستخدمة
  await fixUnusedVariables();
}

Future<void> fixSuperParameters() async {
  final file = File('lib/screens/mysql_test_screen_simple.dart');
  if (!await file.exists()) return;

  try {
    String content = await file.readAsString();
    
    // إصلاح super parameters
    content = content.replaceAll(
      'const MySQLTestScreen({Key? key}) : super(key: key);',
      'const MySQLTestScreen({super.key});'
    );
    
    await file.writeAsString(content);
    print('تم إصلاح super parameters');
  } catch (e) {
    print('خطأ في إصلاح super parameters: $e');
  }
}

Future<void> fixUnusedVariables() async {
  // إصلاح المتغيرات غير المستخدمة في ملفات مختلفة
  final fixes = {
    'lib/screens/aluminum/profile_categories_screen.dart': [
      'final localizations = AppLocalizations.of(context)!;'
    ],
    'lib/screens/aluminum/profile_series_screen.dart': [
      'final localizations = AppLocalizations.of(context)!;'
    ],
    'test_unified_services_simple.dart': [
      'final db = await UnifiedDatabaseService.instance.database;'
    ],
  };

  for (String filePath in fixes.keys) {
    try {
      final file = File(filePath);
      if (!await file.exists()) continue;

      String content = await file.readAsString();
      
      for (String unusedLine in fixes[filePath]!) {
        // إضافة تعليق ignore
        content = content.replaceAll(
          unusedLine,
          '// ignore: unused_local_variable\n    $unusedLine'
        );
      }
      
      await file.writeAsString(content);
      print('تم إصلاح المتغيرات غير المستخدمة في: $filePath');
    } catch (e) {
      print('خطأ في إصلاح المتغيرات في $filePath: $e');
    }
  }
}
