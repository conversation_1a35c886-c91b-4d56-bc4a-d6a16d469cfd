import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/cutting_project.dart';
import '../../models/order_item.dart';
import '../../services/unified_cutting_service.dart';
import 'add_edit_project_screen.dart';
import 'add_order_item_dialog.dart';
import 'cutting_sticks_screen.dart';
import 'panel_cutting_screen.dart';

class ProjectViewScreen extends StatefulWidget {
  final CuttingProject project;

  const ProjectViewScreen({super.key, required this.project});

  @override
  State<ProjectViewScreen> createState() => _ProjectViewScreenState();
}

class _ProjectViewScreenState extends State<ProjectViewScreen> {
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();
  late CuttingProject _project;
  List<OrderItem> _orderItems = [];
  bool _isLoadingItems = false;
  OrderItem? _selectedItem;

  // Controllers for editable fields
  final TextEditingController _requiredBoardsController = TextEditingController();
  final TextEditingController _unitPriceController = TextEditingController();
  final TextEditingController _discountPercentController = TextEditingController();

  // Controllers for calculated fields
  final TextEditingController _totalAmountController = TextEditingController();
  final TextEditingController _discountAmountController = TextEditingController();
  final TextEditingController _finalAmountController = TextEditingController();

  // Controller for item name field
  final TextEditingController _itemNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _project = widget.project;
    _loadOrderItems();
  }

  Future<void> _loadOrderItems() async {
    if (_project.id == null) return;

    setState(() {
      _isLoadingItems = true;
    });

    try {
      final itemsData = await _databaseHelper.getOrderItemsByProject(_project.id!);
      final items = itemsData.map((data) => OrderItem.fromMap(data)).toList();

      // If there was a selected item, try to find it in the new list to maintain selection
      OrderItem? newSelectedItem;
      if (_selectedItem != null) {
        try {
          newSelectedItem = items.firstWhere(
            (item) => item.id == _selectedItem!.id,
          );
        } catch (e) {
          // Item not found, clear selection
          newSelectedItem = null;
        }
      }

      setState(() {
        _orderItems = items;
        _selectedItem = newSelectedItem;

        // Update controllers if there's a selected item
        if (newSelectedItem != null) {
          _requiredBoardsController.text = newSelectedItem.requiredBoards.toString();
          _unitPriceController.text = newSelectedItem.unitPrice.toStringAsFixed(2);
          _discountPercentController.text = newSelectedItem.discountPercent.toStringAsFixed(1);

          // Update calculated field controllers
          _totalAmountController.text = newSelectedItem.totalAmount.toStringAsFixed(2);
          _discountAmountController.text = newSelectedItem.discountAmount.toStringAsFixed(2);
          _finalAmountController.text = newSelectedItem.finalAmount.toStringAsFixed(2);

          // Update item name controller
          final localizations = AppLocalizations.of(context)!;
          _itemNameController.text = '${newSelectedItem.itemName} (${newSelectedItem.itemType == 'sticks' ? localizations.sticks : localizations.boards})';
        }
      });
    } catch (e) {
      // Handle error silently or show message
    } finally {
      setState(() {
        _isLoadingItems = false;
      });
    }
  }

  Future<void> _addNewOrderItem() async {
    if (_project.id == null) return;

    final result = await showDialog<OrderItem>(
      context: context,
      builder: (context) => AddOrderItemDialog(projectId: _project.id!),
    );

    if (result != null) {
      // Reload items to show the new one
      await _loadOrderItems();
    }
  }

  Future<void> _deleteOrderItem(OrderItem item) async {
    final localizations = AppLocalizations.of(context)!;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.confirmDelete),
        content: Text('هل أنت متأكد من حذف البند "${item.itemName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );

    if (confirmed == true && item.id != null) {
      try {
        await _databaseHelper.deleteOrderItem(item.id!);
        await _loadOrderItems(); // Reload the list
        _showSuccessSnackBar('تم حذف البند بنجاح');
      } catch (e) {
        _showErrorSnackBar('${localizations.error}: $e');
      }
    }
  }

  @override
  void dispose() {
    _requiredBoardsController.dispose();
    _unitPriceController.dispose();
    _discountPercentController.dispose();
    _totalAmountController.dispose();
    _discountAmountController.dispose();
    _finalAmountController.dispose();
    _itemNameController.dispose();
    super.dispose();
  }

  void _selectItem(OrderItem item) {
    setState(() {
      _selectedItem = item;
      // Update controllers with selected item values
      _requiredBoardsController.text = item.requiredBoards.toString();
      _unitPriceController.text = item.unitPrice.toStringAsFixed(2);
      _discountPercentController.text = item.discountPercent.toStringAsFixed(1);

      // Update calculated field controllers
      _totalAmountController.text = item.totalAmount.toStringAsFixed(2);
      _discountAmountController.text = item.discountAmount.toStringAsFixed(2);
      _finalAmountController.text = item.finalAmount.toStringAsFixed(2);

      // Update item name controller
      final localizations = AppLocalizations.of(context)!;
      _itemNameController.text = '${item.itemName} (${item.itemType == 'sticks' ? localizations.sticks : localizations.boards})';
    });
  }

  OrderItem _calculateTotals(OrderItem item, {
    int? requiredBoards,
    double? unitPrice,
    double? discountPercent,
  }) {
    final boards = requiredBoards ?? item.requiredBoards;
    final price = unitPrice ?? item.unitPrice;
    final discount = discountPercent ?? item.discountPercent;

    final totalAmount = boards.toDouble() * price;
    final discountAmount = totalAmount * (discount / 100);
    final finalAmount = totalAmount - discountAmount;

    // Update calculated field controllers immediately
    _totalAmountController.text = totalAmount.toStringAsFixed(2);
    _discountAmountController.text = discountAmount.toStringAsFixed(2);
    _finalAmountController.text = finalAmount.toStringAsFixed(2);

    return item.copyWith(
      requiredBoards: boards,
      unitPrice: price,
      discountPercent: discount,
      totalAmount: totalAmount,
      discountAmount: discountAmount,
      finalAmount: finalAmount,
    );
  }

  Future<void> _updateItem(OrderItem updatedItem) async {
    try {
      await _databaseHelper.updateOrderItem(updatedItem.id!, updatedItem.toMap());
      await _loadOrderItems();

      // Find the updated item from the reloaded list to ensure we have the correct reference
      final updatedItemFromDb = _orderItems.firstWhere(
        (item) => item.id == updatedItem.id,
        orElse: () => updatedItem,
      );

      setState(() {
        _selectedItem = updatedItemFromDb;
        // Update controllers with the fresh data from database
        _requiredBoardsController.text = updatedItemFromDb.requiredBoards.toString();
        _unitPriceController.text = updatedItemFromDb.unitPrice.toStringAsFixed(2);
        _discountPercentController.text = updatedItemFromDb.discountPercent.toStringAsFixed(1);

        // Update calculated field controllers
        _totalAmountController.text = updatedItemFromDb.totalAmount.toStringAsFixed(2);
        _discountAmountController.text = updatedItemFromDb.discountAmount.toStringAsFixed(2);
        _finalAmountController.text = updatedItemFromDb.finalAmount.toStringAsFixed(2);

        // Update item name controller
        final localizations = AppLocalizations.of(context)!;
        _itemNameController.text = '${updatedItemFromDb.itemName} (${updatedItemFromDb.itemType == 'sticks' ? localizations.sticks : localizations.boards})';
      });
    } catch (e) {
      if (mounted) {
        final localizations = AppLocalizations.of(context)!;
        _showErrorSnackBar('${localizations.error}: $e');
      }
    }
  }

  Widget _buildItemDetailsView() {
    if (_selectedItem == null) return const SizedBox();

    final localizations = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Action Buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                // Header Row
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: _selectedItem!.itemType == 'sticks'
                          ? Colors.brown[100]
                          : Colors.orange[100],
                      child: Icon(
                        _selectedItem!.itemType == 'sticks'
                            ? Icons.straighten
                            : Icons.view_module,
                        color: _selectedItem!.itemType == 'sticks'
                            ? Colors.brown[600]
                            : Colors.orange[600],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.itemDetails,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${localizations.itemNumber}: ${_selectedItem!.itemNumber}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Action Buttons Row
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectedItem != null ? () {
                          // Navigate to cutting screen based on item type
                          if (_selectedItem!.itemType == 'sticks') {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => CuttingSticksScreen(
                                  orderItem: _selectedItem!,
                                ),
                              ),
                            );
                          } else {
                            // Navigate to panel cutting screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PanelCuttingScreen(
                                  orderItem: _selectedItem!,
                                ),
                              ),
                            );
                          }
                        } : null,
                        icon: const Icon(Icons.straighten, size: 16),
                        label: Text(
                          localizations.enterMeasurements,
                          style: const TextStyle(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          if (_selectedItem != null) {
                            await _updateItem(_selectedItem!);
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('تم حفظ البيانات بنجاح'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.save, size: 16),
                        label: Text(
                          localizations.savePrice,
                          style: const TextStyle(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Item Details Form
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Item Name with Type
                  _buildDetailField(
                    label: localizations.itemName,
                    controller: _itemNameController,
                    readOnly: true,
                  ),

                  const SizedBox(height: 16),

                  // Required Boards
                  _buildDetailField(
                    label: localizations.requiredBoards,
                    controller: _requiredBoardsController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      if (_selectedItem != null) {
                        final boards = int.tryParse(value) ?? 0;
                        final updatedItem = _calculateTotals(_selectedItem!, requiredBoards: boards);
                        setState(() {
                          _selectedItem = updatedItem;
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Unit Price
                  _buildDetailField(
                    label: localizations.unitPrice,
                    controller: _unitPriceController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      if (_selectedItem != null) {
                        final price = double.tryParse(value) ?? 0.0;
                        final updatedItem = _calculateTotals(_selectedItem!, unitPrice: price);
                        setState(() {
                          _selectedItem = updatedItem;
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Total Amount (calculated)
                  _buildDetailField(
                    label: localizations.totalAmount,
                    controller: _totalAmountController,
                    readOnly: true,
                    backgroundColor: Colors.grey[50],
                  ),

                  const SizedBox(height: 16),

                  // Discount
                  Row(
                    children: [
                      Expanded(
                        child: _buildDetailField(
                          label: localizations.discountPercent,
                          controller: _discountPercentController,
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            if (_selectedItem != null) {
                              final discount = double.tryParse(value) ?? 0.0;
                              final updatedItem = _calculateTotals(_selectedItem!, discountPercent: discount);
                              setState(() {
                                _selectedItem = updatedItem;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildDetailField(
                          label: localizations.discountAmount,
                          controller: _discountAmountController,
                          readOnly: true,
                          backgroundColor: Colors.grey[50],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Final Amount
                  _buildDetailField(
                    label: localizations.finalAmount,
                    controller: _finalAmountController,
                    readOnly: true,
                    backgroundColor: Colors.green[50],
                    textColor: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),


                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailField({
    required String label,
    String? value,
    TextEditingController? controller,
    bool readOnly = false,
    TextInputType? keyboardType,
    Function(String)? onChanged,
    Color? backgroundColor,
    Color? textColor,
    FontWeight? fontWeight,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          initialValue: controller == null ? value : null,
          readOnly: readOnly,
          keyboardType: keyboardType,
          onChanged: onChanged,
          style: TextStyle(
            color: textColor,
            fontWeight: fontWeight,
          ),
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            filled: backgroundColor != null,
            fillColor: backgroundColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _deleteProject() async {
    final localizations = AppLocalizations.of(context)!;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.confirmDelete),
        content: Text(localizations.deleteProjectMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );

    if (confirmed == true && _project.id != null) {
      try {
        await _databaseHelper.deleteCuttingProject(_project.id!);
        _showSuccessSnackBar(localizations.projectDeleted);
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate deletion
        }
      } catch (e) {
        _showErrorSnackBar('${localizations.error}: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.projectView),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final navigator = Navigator.of(context);
              final result = await navigator.push<bool>(
                MaterialPageRoute(
                  builder: (context) => AddEditProjectScreen(project: _project),
                ),
              );
              if (result == true && mounted) {
                // Reload project data if needed
                navigator.pop(true); // Return to projects list
              }
            },
            tooltip: localizations.editProject,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteProject,
            tooltip: localizations.deleteProject,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Project Header Card - Compact
            Container(
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.folder_open,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Row(
                      children: [
                        // Project Number
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            _project.projectNumber,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Customer Name
                        Expanded(
                          flex: 2,
                          child: Text(
                            _project.customerName,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Phone Number
                        Text(
                          _project.phone,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Colors.green[700],
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Main content area with responsive layout
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // تحديد ما إذا كانت الشاشة صغيرة (موبايل) أم كبيرة (ويندوز/متصفح)
                  bool isMobile = constraints.maxWidth < 800;

                  if (isMobile) {
                    // تصميم عمودي للموبايل
                    return _buildMobileLayout(localizations);
                  } else {
                    // تصميم أفقي للشاشات الكبيرة
                    return _buildDesktopLayout(localizations);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // تصميم للموبايل - عمودي
  Widget _buildMobileLayout(AppLocalizations localizations) {
    return Column(
      children: [
        // قائمة العناصر
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                style: BorderStyle.solid,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header with create new order button
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        localizations.orderItems,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _addNewOrderItem,
                          icon: const Icon(Icons.add, size: 18),
                          label: Text(localizations.createNewOrder),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            textStyle: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Items list
                Expanded(
                  child: _isLoadingItems
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : _orderItems.isEmpty
                          ? Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.shopping_cart_outlined,
                                    size: 48,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    localizations.noItemsYet,
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    localizations.addFirstItem,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey[500],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          : ScrollConfiguration(
                              behavior: ScrollConfiguration.of(context).copyWith(
                                scrollbars: true,
                              ),
                              child: ListView.builder(
                                padding: const EdgeInsets.all(8),
                                itemCount: _orderItems.length,
                                itemBuilder: (context, index) {
                                final item = _orderItems[index];
                                final isSelected = _selectedItem?.id == item.id;

                                return Card(
                                  margin: const EdgeInsets.symmetric(vertical: 2),
                                  color: isSelected ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1) : null,
                                  child: ListTile(
                                    dense: true,
                                    onTap: () => _selectItem(item),
                                    leading: CircleAvatar(
                                      radius: 16,
                                      backgroundColor: item.itemType == 'sticks'
                                          ? Colors.brown[100]
                                          : Colors.orange[100],
                                      child: Icon(
                                        item.itemType == 'sticks'
                                            ? Icons.straighten
                                            : Icons.view_module,
                                        color: item.itemType == 'sticks'
                                            ? Colors.brown[600]
                                            : Colors.orange[600],
                                        size: 16,
                                      ),
                                    ),
                                    title: Text(
                                      item.itemName,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                        color: isSelected ? Theme.of(context).colorScheme.primary : null,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${localizations.itemNumber}: ${item.itemNumber}',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 11,
                                          ),
                                        ),
                                        Text(
                                          item.itemType == 'sticks'
                                              ? localizations.sticks
                                              : localizations.boards,
                                          style: TextStyle(
                                            color: item.itemType == 'sticks'
                                                ? Colors.brown[600]
                                                : Colors.orange[600],
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    trailing: IconButton(
                                      icon: const Icon(
                                        Icons.delete_outline,
                                        color: Colors.red,
                                        size: 18,
                                      ),
                                      onPressed: () => _deleteOrderItem(item),
                                    ),
                                  ),
                                );
                              },
                              ),
                            ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // تفاصيل العنصر المحدد
        if (_selectedItem != null)
          Expanded(
            flex: 1,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey[300]!,
                  style: BorderStyle.solid,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: _buildItemDetailsView(),
            ),
          ),
      ],
    );
  }

  // تصميم للشاشات الكبيرة - أفقي
  Widget _buildDesktopLayout(AppLocalizations localizations) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      textDirection: Directionality.of(context) == TextDirection.rtl
          ? TextDirection.rtl
          : TextDirection.ltr,
      children: [
        // Sidebar for order items (positioned based on language direction)
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                style: BorderStyle.solid,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header with create new order button
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        localizations.orderItems,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _addNewOrderItem,
                          icon: const Icon(Icons.add),
                          label: Text(localizations.createNewOrder),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Items list
                Expanded(
                  child: _isLoadingItems
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : _orderItems.isEmpty
                          ? Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.shopping_cart_outlined,
                                    size: 48,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    localizations.noItemsYet,
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    localizations.addFirstItem,
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Colors.grey[500],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          : ScrollConfiguration(
                              behavior: ScrollConfiguration.of(context).copyWith(
                                scrollbars: true,
                              ),
                              child: ListView.builder(
                                padding: const EdgeInsets.all(8),
                                itemCount: _orderItems.length,
                                itemBuilder: (context, index) {
                                final item = _orderItems[index];
                                final isSelected = _selectedItem?.id == item.id;

                                return Card(
                                  margin: const EdgeInsets.symmetric(vertical: 4),
                                  color: isSelected ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1) : null,
                                  child: ListTile(
                                    onTap: () => _selectItem(item),
                                    leading: CircleAvatar(
                                      backgroundColor: item.itemType == 'sticks'
                                          ? Colors.brown[100]
                                          : Colors.orange[100],
                                      child: Icon(
                                        item.itemType == 'sticks'
                                            ? Icons.straighten
                                            : Icons.view_module,
                                        color: item.itemType == 'sticks'
                                            ? Colors.brown[600]
                                            : Colors.orange[600],
                                        size: 20,
                                      ),
                                    ),
                                    title: Text(
                                      item.itemName,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        color: isSelected ? Theme.of(context).colorScheme.primary : null,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${localizations.itemNumber}: ${item.itemNumber}',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          item.itemType == 'sticks'
                                              ? localizations.sticks
                                              : localizations.boards,
                                          style: TextStyle(
                                            color: item.itemType == 'sticks'
                                                ? Colors.brown[600]
                                                : Colors.orange[600],
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    trailing: IconButton(
                                      icon: const Icon(
                                        Icons.delete_outline,
                                        color: Colors.red,
                                        size: 20,
                                      ),
                                      onPressed: () => _deleteOrderItem(item),
                                    ),
                                  ),
                                );
                              },
                              ),
                            ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Main content area - Item Details
        Expanded(
          flex: 2,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                style: BorderStyle.solid,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _selectedItem == null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.touch_app,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          localizations.selectItem,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          localizations.clickItemToView,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : _buildItemDetailsView(),
          ),
        ),
      ],
    );
  }

}
