class Invoice {
  final int? id;
  final String invoiceNumber;
  final DateTime date;
  final String supplierOrCustomerName;
  final String type; // 'purchase', 'sale', 'purchase_return', 'sale_return'
  final List<InvoiceItem> items;
  final double totalAmount;
  final double discount;
  final double expenses;
  final double netAmount;
  final String notes;
  final int? treasuryId; // ربط الفاتورة بالخزينة
  final bool isPaid; // حالة الدفع
  final int? treasuryTransactionId; // معرف معاملة الخزينة المرتبطة

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.date,
    required this.supplierOrCustomerName,
    required this.type,
    required this.items,
    required this.totalAmount,
    this.discount = 0.0,
    this.expenses = 0.0,
    required this.netAmount,
    this.notes = '',
    this.treasuryId,
    this.isPaid = false,
    this.treasuryTransactionId,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'date': date.toIso8601String(),
      'supplierOrCustomerName': supplierOrCustomerName,
      'type': type,
      'items': items.map((item) => item.toMap()).toList(),
      'totalAmount': totalAmount,
      'discount': discount,
      'expenses': expenses,
      'netAmount': netAmount,
      'notes': notes,
      'treasuryId': treasuryId,
      'isPaid': isPaid ? 1 : 0,
      'treasuryTransactionId': treasuryTransactionId,
    };
  }

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id']?.toInt(),
      invoiceNumber: map['invoiceNumber'] ?? '',
      date: DateTime.parse(map['date']),
      supplierOrCustomerName: map['supplierOrCustomerName'] ?? '',
      type: map['type'] ?? '',
      items: map['items'] != null
        ? List<InvoiceItem>.from(
            (map['items'] as List).map((item) => InvoiceItem.fromMap(item)),
          )
        : [],
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      discount: map['discount']?.toDouble() ?? 0.0,
      expenses: map['expenses']?.toDouble() ?? 0.0,
      netAmount: map['netAmount']?.toDouble() ?? 0.0,
      notes: map['notes'] ?? '',
      treasuryId: map['treasuryId']?.toInt(),
      isPaid: (map['isPaid'] as int?) == 1,
      treasuryTransactionId: map['treasuryTransactionId']?.toInt(),
    );
  }

  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    DateTime? date,
    String? supplierOrCustomerName,
    String? type,
    List<InvoiceItem>? items,
    double? totalAmount,
    double? discount,
    double? expenses,
    double? netAmount,
    String? notes,
    int? treasuryId,
    bool? isPaid,
    int? treasuryTransactionId,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      date: date ?? this.date,
      supplierOrCustomerName: supplierOrCustomerName ?? this.supplierOrCustomerName,
      type: type ?? this.type,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      discount: discount ?? this.discount,
      expenses: expenses ?? this.expenses,
      netAmount: netAmount ?? this.netAmount,
      notes: notes ?? this.notes,
      treasuryId: treasuryId ?? this.treasuryId,
      isPaid: isPaid ?? this.isPaid,
      treasuryTransactionId: treasuryTransactionId ?? this.treasuryTransactionId,
    );
  }
}

class InvoiceItem {
  final int? id;
  final String itemName;
  final String unit;
  final double quantity;
  final double price;
  final double discount;
  final double total;

  InvoiceItem({
    this.id,
    required this.itemName,
    required this.unit,
    required this.quantity,
    required this.price,
    this.discount = 0.0,
    required this.total,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemName': itemName,
      'unit': unit,
      'quantity': quantity,
      'price': price,
      'discount': discount,
      'total': total,
    };
  }

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id']?.toInt(),
      itemName: map['itemName'] ?? '',
      unit: map['unit'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      price: map['price']?.toDouble() ?? 0.0,
      discount: map['discount']?.toDouble() ?? 0.0,
      total: map['total']?.toDouble() ?? 0.0,
    );
  }

  InvoiceItem copyWith({
    int? id,
    String? itemName,
    String? unit,
    double? quantity,
    double? price,
    double? discount,
    double? total,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      itemName: itemName ?? this.itemName,
      unit: unit ?? this.unit,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
      discount: discount ?? this.discount,
      total: total ?? this.total,
    );
  }
}

class Customer {
  final int? id;
  final String name;
  final String phone;
  final String address;
  final String email;
  final double balance;
  final String notes;

  Customer({
    this.id,
    required this.name,
    this.phone = '',
    this.address = '',
    this.email = '',
    this.balance = 0.0,
    this.notes = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'email': email,
      'balance': balance,
      'notes': notes,
    };
  }

  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      email: map['email'] ?? '',
      balance: map['balance']?.toDouble() ?? 0.0,
      notes: map['notes'] ?? '',
    );
  }

  Customer copyWith({
    int? id,
    String? name,
    String? phone,
    String? address,
    String? email,
    double? balance,
    String? notes,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      email: email ?? this.email,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
    );
  }
}

class Supplier {
  final int? id;
  final String name;
  final String phone;
  final String address;
  final String email;
  final String company;
  final double balance;
  final String notes;

  Supplier({
    this.id,
    required this.name,
    required this.phone,
    this.address = '',
    this.email = '',
    this.company = '',
    this.balance = 0.0,
    this.notes = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'address': address,
      'email': email,
      'company': company,
      'balance': balance,
      'notes': notes,
    };
  }  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      email: map['email'] ?? '',
      company: map['company'] ?? '',
      balance: map['balance']?.toDouble() ?? 0.0,
      notes: map['notes'] ?? '',
    );
  }

  Supplier copyWith({
    int? id,
    String? name,
    String? phone,
    String? address,
    String? email,
    String? company,
    double? balance,
    String? notes,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      email: email ?? this.email,
      company: company ?? this.company,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
    );
  }
}
