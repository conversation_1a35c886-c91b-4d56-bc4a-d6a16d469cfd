import 'package:intl/intl.dart';
import 'settings_service.dart';

class CurrencyService {
  static CurrencyService? _instance;
  static CurrencyService get instance => _instance ??= CurrencyService._();
  
  CurrencyService._();

  String? _cachedCurrency;
  String? _cachedSymbol;

  // Format amount with currency
  Future<String> formatAmount(double amount, {bool showSymbol = true}) async {
    // ignore: unused_local_variable
    final currency = await _getCurrency();
    final symbol = await _getCurrencySymbol();
    
    final formatter = NumberFormat("#,##0.00");
    final formattedAmount = formatter.format(amount);
    
    if (showSymbol) {
      return '$formattedAmount $symbol';
    } else {
      return formattedAmount;
    }
  }

  // Format amount with custom currency
  String formatAmountWithCurrency(double amount, String currency, {bool showSymbol = true}) {
    final formatter = NumberFormat("#,##0.00");
    final formattedAmount = formatter.format(amount);
    
    if (showSymbol) {
      return '$formattedAmount $currency';
    } else {
      return formattedAmount;
    }
  }

  // Get current currency (cached)
  Future<String> _getCurrency() async {
    _cachedCurrency ??= await SettingsService.instance.getCurrency();
    return _cachedCurrency!;
  }

  // Get current currency symbol (cached)
  Future<String> _getCurrencySymbol() async {
    _cachedSymbol ??= await SettingsService.instance.getCurrencySymbol();
    return _cachedSymbol!;
  }

  // Clear cache when currency changes
  void clearCache() {
    _cachedCurrency = null;
    _cachedSymbol = null;
  }

  // Get current currency without async
  String getCurrentCurrency() {
    return _cachedCurrency ?? 'ريال';
  }

  // Get current currency symbol without async
  String getCurrentCurrencySymbol() {
    return _cachedSymbol ?? 'ر.س';
  }

  // Initialize cache
  Future<void> initCache() async {
    _cachedCurrency = await SettingsService.instance.getCurrency();
    _cachedSymbol = await SettingsService.instance.getCurrencySymbol();
  }

  // Format for PDF (using cached values)
  String formatForPdf(double amount) {
    final formatter = NumberFormat("#,##0.00");
    final formattedAmount = formatter.format(amount);
    return '$formattedAmount ${getCurrentCurrencySymbol()}';
  }

  // Format without symbol
  String formatAmountOnly(double amount) {
    final formatter = NumberFormat("#,##0.00");
    return formatter.format(amount);
  }
}
