import 'dart:io';

/// أداة تحديث تلقائية لتحديث التطبيق لاستخدام الخدمات الموحدة
Future<void> main() async {
  print('🔄 بدء تحديث التطبيق لاستخدام الخدمات الموحدة...\n');

  try {
    // البحث عن جميع ملفات Dart في المشروع
    final dartFiles = await findDartFiles();
    print('📁 تم العثور على ${dartFiles.length} ملف Dart');

    int updatedFiles = 0;
    int totalReplacements = 0;

    // تحديث كل ملف
    for (final file in dartFiles) {
      final result = await updateFile(file);
      if (result['updated']) {
        updatedFiles++;
        totalReplacements += result['replacements'] as int;
        print('   ✅ تم تحديث: ${file.path} (${result['replacements']} تغيير)');
      }
    }

    print('\n📊 ملخص التحديث:');
    print('   📁 إجمالي الملفات: ${dartFiles.length}');
    print('   ✅ الملفات المحدثة: $updatedFiles');
    print('   🔄 إجمالي التغييرات: $totalReplacements');

    if (updatedFiles > 0) {
      print('\n🎉 تم تحديث التطبيق بنجاح لاستخدام الخدمات الموحدة!');
      print('📋 يُنصح بـ:');
      print('   1. اختبار التطبيق للتأكد من عمل جميع الوظائف');
      print('   2. تشغيل flutter clean && flutter pub get');
      print('   3. إعادة تشغيل التطبيق');
    } else {
      print('\n✅ لا توجد ملفات تحتاج تحديث - التطبيق محدث بالفعل!');
    }

  } catch (e) {
    print('❌ خطأ في تحديث التطبيق: $e');
  }
}

/// البحث عن جميع ملفات Dart في المشروع
Future<List<File>> findDartFiles() async {
  final libDir = Directory('lib');
  if (!await libDir.exists()) {
    throw Exception('مجلد lib غير موجود');
  }

  final dartFiles = <File>[];
  
  await for (final entity in libDir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      // تجاهل ملفات الخدمات الموحدة الجديدة
      if (!entity.path.contains('unified_') && 
          !entity.path.contains('database_migration_service.dart')) {
        dartFiles.add(entity);
      }
    }
  }

  return dartFiles;
}

/// تحديث ملف واحد
Future<Map<String, dynamic>> updateFile(File file) async {
  try {
    String content = await file.readAsString();
    final originalContent = content;
    int replacements = 0;

    // قائمة التحديثات المطلوبة
    final updates = [
      // تحديث استيرادات الفواتير
      {
        'old': "import '../services/invoice_database.dart';",
        'new': "import '../services/unified_invoice_service.dart';",
      },
      {
        'old': 'import "../services/invoice_database.dart";',
        'new': 'import "../services/unified_invoice_service.dart";',
      },
      
      // تحديث استيرادات الخزينة
      {
        'old': "import '../services/database_helper_treasury.dart';",
        'new': "import '../services/unified_treasury_service.dart';",
      },
      {
        'old': 'import "../services/database_helper_treasury.dart";',
        'new': 'import "../services/unified_treasury_service.dart";',
      },
      
      // تحديث استيرادات التقطيع والمهام
      {
        'old': "import '../services/database_helper.dart';",
        'new': "import '../services/unified_cutting_service.dart';",
      },
      {
        'old': 'import "../services/database_helper.dart";',
        'new': 'import "../services/unified_cutting_service.dart";',
      },
      
      // تحديث استيرادات الألومنيوم
      {
        'old': "import '../services/aluminum_profile_service.dart';",
        'new': "import '../services/unified_aluminum_service.dart';",
      },
      {
        'old': 'import "../services/aluminum_profile_service.dart";',
        'new': 'import "../services/unified_aluminum_service.dart";',
      },
      {
        'old': "import '../services/aluminum_quotation_service.dart';",
        'new': "import '../services/unified_aluminum_service.dart';",
      },
      {
        'old': 'import "../services/aluminum_quotation_service.dart";',
        'new': 'import "../services/unified_aluminum_service.dart";',
      },
      {
        'old': "import '../services/aluminum_design_service.dart';",
        'new': "import '../services/unified_aluminum_service.dart';",
      },
      {
        'old': 'import "../services/aluminum_design_service.dart";',
        'new': 'import "../services/unified_aluminum_service.dart";',
      },
      
      // تحديث استيرادات uPVC
      {
        'old': "import '../services/upvc_profile_service.dart';",
        'new': "import '../services/unified_upvc_service.dart';",
      },
      {
        'old': 'import "../services/upvc_profile_service.dart";',
        'new': 'import "../services/unified_upvc_service.dart";',
      },
      {
        'old': "import '../services/upvc_quotation_service.dart';",
        'new': "import '../services/unified_upvc_service.dart';",
      },
      {
        'old': 'import "../services/upvc_quotation_service.dart";',
        'new': 'import "../services/unified_upvc_service.dart";',
      },
      {
        'old': "import '../services/upvc_design_service.dart';",
        'new': "import '../services/unified_upvc_service.dart';",
      },
      {
        'old': 'import "../services/upvc_design_service.dart";',
        'new': 'import "../services/unified_upvc_service.dart";',
      },
      
      // تحديث إنشاء كائنات الخدمات
      {
        'old': 'InvoiceDatabase.instance',
        'new': 'UnifiedInvoiceService()',
      },
      {
        'old': 'DatabaseHelperTreasury()',
        'new': 'UnifiedTreasuryService()',
      },
      {
        'old': 'DatabaseHelper()',
        'new': 'UnifiedCuttingService()',
      },
      {
        'old': 'AluminumProfileService()',
        'new': 'UnifiedAluminumService()',
      },
      {
        'old': 'AluminumQuotationService()',
        'new': 'UnifiedAluminumService()',
      },
      {
        'old': 'AluminumDesignService()',
        'new': 'UnifiedAluminumService()',
      },
      {
        'old': 'UpvcProfileService()',
        'new': 'UnifiedUpvcService()',
      },
      {
        'old': 'UpvcQuotationService()',
        'new': 'UnifiedUpvcService()',
      },
      {
        'old': 'UpvcDesignService()',
        'new': 'UnifiedUpvcService()',
      },
      
      // تحديث أنواع المتغيرات
      {
        'old': 'InvoiceDatabase ',
        'new': 'UnifiedInvoiceService ',
      },
      {
        'old': 'DatabaseHelperTreasury ',
        'new': 'UnifiedTreasuryService ',
      },
      {
        'old': 'DatabaseHelper ',
        'new': 'UnifiedCuttingService ',
      },
      {
        'old': 'AluminumProfileService ',
        'new': 'UnifiedAluminumService ',
      },
      {
        'old': 'AluminumQuotationService ',
        'new': 'UnifiedAluminumService ',
      },
      {
        'old': 'AluminumDesignService ',
        'new': 'UnifiedAluminumService ',
      },
      {
        'old': 'UpvcProfileService ',
        'new': 'UnifiedUpvcService ',
      },
      {
        'old': 'UpvcQuotationService ',
        'new': 'UnifiedUpvcService ',
      },
      {
        'old': 'UpvcDesignService ',
        'new': 'UnifiedUpvcService ',
      },
    ];

    // تطبيق التحديثات
    for (final update in updates) {
      final oldPattern = update['old']!;
      final newPattern = update['new']!;
      
      if (content.contains(oldPattern)) {
        content = content.replaceAll(oldPattern, newPattern);
        replacements++;
      }
    }

    // إزالة الاستيرادات المكررة
    content = removeDuplicateImports(content);

    // كتابة الملف المحدث إذا تم تغييره
    if (content != originalContent) {
      await file.writeAsString(content);
      return {'updated': true, 'replacements': replacements};
    }

    return {'updated': false, 'replacements': 0};

  } catch (e) {
    print('   ❌ خطأ في تحديث ${file.path}: $e');
    return {'updated': false, 'replacements': 0};
  }
}

/// إزالة الاستيرادات المكررة
String removeDuplicateImports(String content) {
  final lines = content.split('\n');
  final imports = <String>{};
  final newLines = <String>[];
  
  for (final line in lines) {
    if (line.trim().startsWith('import ') && line.trim().endsWith(';')) {
      if (!imports.contains(line.trim())) {
        imports.add(line.trim());
        newLines.add(line);
      }
    } else {
      newLines.add(line);
    }
  }
  
  return newLines.join('\n');
}

/// إنشاء نسخة احتياطية من الملفات
Future<void> createBackup() async {
  final backupDir = Directory('backup_${DateTime.now().millisecondsSinceEpoch}');
  await backupDir.create();
  
  final libDir = Directory('lib');
  await copyDirectory(libDir, Directory('${backupDir.path}/lib'));
  
  print('📦 تم إنشاء نسخة احتياطية في: ${backupDir.path}');
}

/// نسخ مجلد
Future<void> copyDirectory(Directory source, Directory destination) async {
  await destination.create(recursive: true);
  
  await for (final entity in source.list()) {
    if (entity is Directory) {
      await copyDirectory(
        entity,
        Directory('${destination.path}/${entity.path.split('/').last}')
      );
    } else if (entity is File) {
      await entity.copy('${destination.path}/${entity.path.split('/').last}');
    }
  }
}
