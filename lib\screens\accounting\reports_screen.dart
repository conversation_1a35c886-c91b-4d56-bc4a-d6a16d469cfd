
import 'package:flutter/material.dart';
import '../../services/unified_invoice_service.dart';
import '../../services/currency_service.dart';
import 'package:intl/intl.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  bool _isLoading = true;

  // إحصائيات
  double _totalSales = 0.0;
  double _totalPurchases = 0.0;
  int _salesCount = 0;
  int _purchasesCount = 0;
  int _customersCount = 0;
  int _suppliersCount = 0;

  // فترة التقرير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('Loading report data...'); // Debug

      // إحصائيات المبيعات والمشتريات
      final stats = await _database.getSalesStatistics();
      final purchaseStats = await _database.getPurchaseStatistics();
      debugPrint('Sales stats: $stats'); // Debug
      debugPrint('Purchase stats: $purchaseStats'); // Debug

      // عدد العملاء والموردين
      final customers = await _database.getAllCustomers();
      final suppliers = await _database.getAllSuppliers();
      debugPrint('Customers count: ${customers.length}'); // Debug
      debugPrint('Suppliers count: ${suppliers.length}'); // Debug

      // فواتير المبيعات في الفترة المحددة
      final salesInvoices = await _database.getInvoicesByDateRange(
        _startDate, _endDate, 'sale'
      );

      // فواتير المشتريات في الفترة المحددة
      final purchaseInvoices = await _database.getInvoicesByDateRange(
        _startDate, _endDate, 'purchase'
      );
      debugPrint('Sales invoices in range: ${salesInvoices.length}'); // Debug
      debugPrint('Purchase invoices in range: ${purchaseInvoices.length}'); // Debug

      setState(() {
        _totalSales = stats['totalAmount'] ?? 0.0;
        _totalPurchases = purchaseStats['totalAmount'] ?? 0.0;
        _salesCount = salesInvoices.length;
        _purchasesCount = purchaseInvoices.length;
        _customersCount = customers.length;
        _suppliersCount = suppliers.length;
        _isLoading = false;
      });

      debugPrint('Final values - Sales: $_totalSales, Purchases: $_totalPurchases'); // Debug
    } catch (e) {
      debugPrint('Error loading report data: $e'); // Debug
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل التقارير: $e')),
        );
      }
    }
  }

  Future<void> _addSampleData() async {
    try {
      await _database.forceAddSampleData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة البيانات التجريبية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
      // Reload data after adding sample data
      _loadReportData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة البيانات التجريبية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير والإحصائيات'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
            },
            tooltip: 'الصفحة الرئيسية',
          ),
          IconButton(
            icon: const Icon(Icons.add_circle),
            onPressed: _addSampleData,
            tooltip: 'إضافة بيانات تجريبية',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // فترة التقرير
                  _buildDateRangeCard(),
                  const SizedBox(height: 16),

                  // إحصائيات سريعة
                  _buildQuickStatsSection(),
                  const SizedBox(height: 16),

                  // إحصائيات المبيعات والمشتريات
                  _buildSalesPurchaseSection(),
                  const SizedBox(height: 16),

                  // إحصائيات العملاء والموردين
                  _buildCustomerSupplierSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildDateRangeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.date_range,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'فترة التقرير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateButton(
                    'من تاريخ',
                    _startDate,
                    (date) => setState(() => _startDate = date),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateButton(
                    'إلى تاريخ',
                    _endDate,
                    (date) => setState(() => _endDate = date),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _loadReportData,
                icon: const Icon(Icons.search),
                label: const Text('تحديث التقرير'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateButton(String label, DateTime date, Function(DateTime) onChanged) {
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );
        if (pickedDate != null) {
          onChanged(pickedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatsSection() {
    final profit = _totalSales - _totalPurchases;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نظرة سريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: FutureBuilder<String>(
                future: CurrencyService.instance.formatAmount(_totalSales),
                builder: (context, snapshot) {
                  return _buildStatCard(
                    'إجمالي المبيعات',
                    snapshot.data ?? CurrencyService.instance.formatForPdf(_totalSales),
                    Icons.trending_up,
                    Colors.green,
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: FutureBuilder<String>(
                future: CurrencyService.instance.formatAmount(_totalPurchases),
                builder: (context, snapshot) {
                  return _buildStatCard(
                    'إجمالي المشتريات',
                    snapshot.data ?? CurrencyService.instance.formatForPdf(_totalPurchases),
                    Icons.trending_down,
                    Colors.orange,
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        FutureBuilder<String>(
          future: CurrencyService.instance.formatAmount(profit),
          builder: (context, snapshot) {
            return _buildStatCard(
              'صافي الربح',
              snapshot.data ?? CurrencyService.instance.formatForPdf(profit),
              profit >= 0 ? Icons.attach_money : Icons.money_off,
              profit >= 0 ? Colors.green : Colors.red,
            );
          },
        ),
      ],
    );
  }

  Widget _buildSalesPurchaseSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات الفواتير',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'فواتير المبيعات',
                '$_salesCount فاتورة',
                Icons.receipt_long,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'فواتير المشتريات',
                '$_purchasesCount فاتورة',
                Icons.shopping_cart,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomerSupplierSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات الشركاء',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'عدد العملاء',
                '$_customersCount عميل',
                Icons.people,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'عدد الموردين',
                '$_suppliersCount مورد',
                Icons.business,
                Colors.indigo,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
