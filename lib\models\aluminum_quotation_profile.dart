import 'aluminum_profile.dart';

class AluminumQuotationProfile {
  final int? id;
  final int quotationItemId;
  final int profileId;
  final ProfileCategory category;
  final String profileName;
  final String profileCode;
  final String? imagePath;
  final double quantity; // الكمية المطلوبة من هذا القطاع
  final String notes;
  final DateTime createdAt;

  AluminumQuotationProfile({
    this.id,
    required this.quotationItemId,
    required this.profileId,
    required this.category,
    required this.profileName,
    required this.profileCode,
    this.imagePath,
    this.quantity = 1.0,
    this.notes = '',
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quotation_item_id': quotationItemId,
      'profile_id': profileId,
      'category': category.key,
      'profile_name': profileName,
      'profile_code': profileCode,
      'image_path': imagePath,
      'quantity': quantity,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory AluminumQuotationProfile.fromMap(Map<String, dynamic> map) {
    return AluminumQuotationProfile(
      id: map['id']?.toInt(),
      quotationItemId: map['quotation_item_id']?.toInt() ?? 0,
      profileId: map['profile_id']?.toInt() ?? 0,
      category: ProfileCategory.values.firstWhere(
        (c) => c.key == map['category'],
        orElse: () => ProfileCategory.halaf,
      ),
      profileName: map['profile_name'] ?? '',
      profileCode: map['profile_code'] ?? '',
      imagePath: map['image_path'],
      quantity: (map['quantity'] ?? 1.0).toDouble(),
      notes: map['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  AluminumQuotationProfile copyWith({
    int? id,
    int? quotationItemId,
    int? profileId,
    ProfileCategory? category,
    String? profileName,
    String? profileCode,
    String? imagePath,
    double? quantity,
    String? notes,
    DateTime? createdAt,
  }) {
    return AluminumQuotationProfile(
      id: id ?? this.id,
      quotationItemId: quotationItemId ?? this.quotationItemId,
      profileId: profileId ?? this.profileId,
      category: category ?? this.category,
      profileName: profileName ?? this.profileName,
      profileCode: profileCode ?? this.profileCode,
      imagePath: imagePath ?? this.imagePath,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'AluminumQuotationProfile{id: $id, category: $category, profileName: $profileName, profileCode: $profileCode}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AluminumQuotationProfile &&
        other.id == id &&
        other.quotationItemId == quotationItemId &&
        other.profileId == profileId &&
        other.category == category;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        quotationItemId.hashCode ^
        profileId.hashCode ^
        category.hashCode;
  }
}
