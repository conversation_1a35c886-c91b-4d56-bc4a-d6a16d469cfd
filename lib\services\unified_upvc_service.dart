import 'package:sqflite/sqflite.dart';
import '../models/upvc_profile.dart';
import '../models/upvc_quotation.dart';
import '../models/profile_series.dart';
import 'unified_database_service.dart';

/// خدمة uPVC الموحدة - تستخدم قاعدة البيانات الموحدة بدلاً من الخدمات المنفصلة
class UnifiedUpvcService {
  static final UnifiedUpvcService _instance = UnifiedUpvcService._internal();
  factory UnifiedUpvcService() => _instance;
  UnifiedUpvcService._internal();

  final UnifiedDatabaseService _unifiedDb = UnifiedDatabaseService();

  /// الحصول على قاعدة البيانات
  Future<Database> get database async => await _unifiedDb.database;

  // ==================== مجموعات قطاعات uPVC ====================

  /// إدراج مجموعة قطاعات uPVC جديدة
  Future<int> insertUpvcProfileSeries(ProfileSeries series) async {
    final db = await database;
    final seriesMap = series.toMap();
    seriesMap['type'] = 'upvc'; // تحديد النوع كـ uPVC
    seriesMap['created_at'] = DateTime.now().millisecondsSinceEpoch;
    seriesMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('upvc_profile_series', seriesMap);
  }

  /// الحصول على جميع مجموعات قطاعات uPVC
  Future<List<ProfileSeries>> getAllUpvcProfileSeries() async {
    final db = await database;
    final result = await db.query(
      'upvc_profile_series',
      orderBy: 'name ASC',
    );
    return result.map((series) => ProfileSeries.fromMap(series)).toList();
  }

  /// الحصول على مجموعة قطاعات uPVC بالمعرف
  Future<ProfileSeries?> getUpvcProfileSeriesById(int id) async {
    final db = await database;
    final result = await db.query(
      'upvc_profile_series',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? ProfileSeries.fromMap(result.first) : null;
  }

  // ==================== قطاعات uPVC ====================

  /// إدراج قطاع uPVC جديد
  Future<int> insertUpvcProfile(UpvcProfile profile) async {
    return await _unifiedDb.insertUpvcProfile(profile.toMap());
  }

  /// الحصول على جميع قطاعات uPVC
  Future<List<UpvcProfile>> getAllProfiles() async {
    final profiles = await _unifiedDb.getAllUpvcProfiles();
    return profiles.map((profile) => UpvcProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاع uPVC بالمعرف
  Future<UpvcProfile?> getProfileById(int id) async {
    final db = await database;
    final result = await db.query(
      'upvc_profiles',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? UpvcProfile.fromMap(result.first) : null;
  }

  /// تحديث قطاع uPVC
  Future<int> updateProfile(UpvcProfile profile) async {
    final db = await database;
    final profileMap = profile.toMap();
    profileMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      'upvc_profiles',
      profileMap,
      where: 'id = ?',
      whereArgs: [profile.id],
    );
  }

  /// حذف قطاع uPVC
  Future<int> deleteProfile(int id) async {
    final db = await database;
    return await db.delete(
      'upvc_profiles',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// البحث في قطاعات uPVC
  Future<List<UpvcProfile>> searchProfiles(String query) async {
    final db = await database;
    final result = await db.query(
      'upvc_profiles',
      where: 'name LIKE ? OR code LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return result.map((profile) => UpvcProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب النوع
  Future<List<UpvcProfile>> getProfilesByType(String type) async {
    final db = await database;
    final result = await db.query(
      'upvc_profiles',
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'name ASC',
    );
    return result.map((profile) => UpvcProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب الفئة
  Future<List<UpvcProfile>> getProfilesByCategory(String category) async {
    final db = await database;
    final result = await db.query(
      'upvc_profiles',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'name ASC',
    );
    return result.map((profile) => UpvcProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب المجموعة
  Future<List<UpvcProfile>> getProfilesBySeries(int seriesId) async {
    final db = await database;
    final result = await db.query(
      'upvc_profiles',
      where: 'series_id = ?',
      whereArgs: [seriesId],
      orderBy: 'name ASC',
    );
    return result.map((profile) => UpvcProfile.fromMap(profile)).toList();
  }

  // ==================== عروض أسعار uPVC ====================

  /// إدراج عرض سعر uPVC جديد
  Future<int> insertQuotation(UpvcQuotation quotation) async {
    final db = await database;
    final quotationMap = quotation.toMap();
    quotationMap['created_at'] = DateTime.now().millisecondsSinceEpoch;
    quotationMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('upvc_quotations', quotationMap);
  }

  /// الحصول على جميع عروض أسعار uPVC
  Future<List<UpvcQuotation>> getAllQuotations() async {
    final db = await database;
    final result = await db.query(
      'upvc_quotations',
      orderBy: 'quotation_date DESC',
    );
    return result.map((quotation) => UpvcQuotation.fromMap(quotation)).toList();
  }

  /// الحصول على عرض سعر بالمعرف
  Future<UpvcQuotation?> getQuotationById(int id) async {
    final db = await database;
    final result = await db.query(
      'upvc_quotations',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? UpvcQuotation.fromMap(result.first) : null;
  }

  /// تحديث عرض سعر
  Future<int> updateQuotation(UpvcQuotation quotation) async {
    final db = await database;
    final quotationMap = quotation.toMap();
    quotationMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      'upvc_quotations',
      quotationMap,
      where: 'id = ?',
      whereArgs: [quotation.id],
    );
  }

  /// حذف عرض سعر
  Future<int> deleteQuotation(int id) async {
    final db = await database;
    return await db.delete(
      'upvc_quotations',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== عناصر عروض الأسعار ====================

  /// إدراج عنصر عرض سعر جديد
  Future<int> insertQuotationItem(Map<String, dynamic> item) async {
    final db = await database;
    item['created_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('upvc_quotation_items', item);
  }

  /// الحصول على عناصر عرض سعر
  Future<List<Map<String, dynamic>>> getQuotationItems(int quotationId) async {
    final db = await database;
    return await db.query(
      'upvc_quotation_items',
      where: 'quotation_id = ?',
      whereArgs: [quotationId],
      orderBy: 'created_at ASC',
    );
  }

  /// تحديث عنصر عرض سعر
  Future<int> updateQuotationItem(int id, Map<String, dynamic> item) async {
    final db = await database;
    return await db.update(
      'upvc_quotation_items',
      item,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف عنصر عرض سعر
  Future<int> deleteQuotationItem(int id) async {
    final db = await database;
    return await db.delete(
      'upvc_quotation_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== التصاميم ====================

  /// إدراج تصميم مفصلي uPVC جديد
  Future<int> insertUpvcHingeDesign(Map<String, dynamic> design) async {
    final db = await database;
    design['created_at'] = DateTime.now().millisecondsSinceEpoch;
    design['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('upvc_hinge_designs', design);
  }

  /// إدراج تصميم سحاب uPVC جديد
  Future<int> insertUpvcSlidingDesign(Map<String, dynamic> design) async {
    final db = await database;
    design['created_at'] = DateTime.now().millisecondsSinceEpoch;
    design['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('upvc_sliding_designs', design);
  }

  /// الحصول على تصاميم المفصلي uPVC
  Future<List<Map<String, dynamic>>> getUpvcHingeDesigns() async {
    final db = await database;
    return await db.query(
      'upvc_hinge_designs',
      orderBy: 'created_at DESC',
    );
  }

  /// الحصول على تصاميم السحاب uPVC
  Future<List<Map<String, dynamic>>> getUpvcSlidingDesigns() async {
    final db = await database;
    return await db.query(
      'upvc_sliding_designs',
      orderBy: 'created_at DESC',
    );
  }

  // ==================== الإحصائيات ====================

  /// الحصول على إحصائيات uPVC
  Future<Map<String, dynamic>> getUpvcStatistics() async {
    final db = await database;
    
    // عدد القطاعات
    final profilesResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_profiles');
    final profilesCount = profilesResult.first['count'] as int;
    
    // عدد عروض الأسعار
    final quotationsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_quotations');
    final quotationsCount = quotationsResult.first['count'] as int;
    
    // عدد التصاميم
    final hingeDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_hinge_designs');
    final hingeDesignsCount = hingeDesignsResult.first['count'] as int;
    
    final slidingDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM upvc_sliding_designs');
    final slidingDesignsCount = slidingDesignsResult.first['count'] as int;
    
    return {
      'profiles_count': profilesCount,
      'quotations_count': quotationsCount,
      'hinge_designs_count': hingeDesignsCount,
      'sliding_designs_count': slidingDesignsCount,
      'total_designs': hingeDesignsCount + slidingDesignsCount,
    };
  }

  /// البحث في عروض الأسعار
  Future<List<UpvcQuotation>> searchQuotations(String query) async {
    final db = await database;
    final result = await db.query(
      'upvc_quotations',
      where: 'quotation_number LIKE ? OR client_name LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'quotation_date DESC',
    );
    return result.map((quotation) => UpvcQuotation.fromMap(quotation)).toList();
  }
}
