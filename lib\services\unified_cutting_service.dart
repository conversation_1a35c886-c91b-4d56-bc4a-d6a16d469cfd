import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../models/cutting_project.dart';
import '../models/task.dart';
import '../models/task_category.dart';
import 'unified_database_service.dart';

/// خدمة التقطيع والمهام الموحدة - تستخدم قاعدة البيانات الموحدة بدلاً من DatabaseHelper
class UnifiedCuttingService {
  static final UnifiedCuttingService _instance = UnifiedCuttingService._internal();
  factory UnifiedCuttingService() => _instance;
  UnifiedCuttingService._internal();

  final UnifiedDatabaseService _unifiedDb = UnifiedDatabaseService();

  /// الحصول على قاعدة البيانات
  Future<Database> get database async => await _unifiedDb.database;

  // ==================== مشاريع التقطيع ====================

  /// إدراج مشروع تقطيع جديد
  Future<int> insertCuttingProject(Map<String, dynamic> project) async {
    // التأكد من وجود created_at و updated_at
    final now = DateTime.now().millisecondsSinceEpoch;
    project['created_at'] ??= now;
    project['updated_at'] ??= now;

    return await _unifiedDb.insertCuttingProject(project);
  }

  /// الحصول على جميع مشاريع التقطيع
  Future<List<Map<String, dynamic>>> getAllCuttingProjects() async {
    return await _unifiedDb.getAllCuttingProjects();
  }

  /// الحصول على مشروع تقطيع بالمعرف
  Future<Map<String, dynamic>?> getCuttingProjectById(int id) async {
    final db = await database;
    final result = await db.query(
      'cutting_projects',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// تحديث مشروع تقطيع
  Future<int> updateCuttingProject(CuttingProject project) async {
    final db = await database;
    final projectMap = project.toMap();
    projectMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      'cutting_projects',
      projectMap,
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }

  /// حذف مشروع تقطيع
  Future<int> deleteCuttingProject(int id) async {
    final db = await database;
    return await db.delete(
      'cutting_projects',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// توليد رقم مشروع جديد
  Future<String> generateNextProjectNumber() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM cutting_projects');
    final count = result.first['count'] as int;
    return 'P${(count + 1).toString().padLeft(4, '0')}';
  }

  // ==================== عناصر الطلبات ====================

  /// إدراج عنصر طلب جديد
  Future<int> insertOrderItem(Map<String, dynamic> item) async {
    // التأكد من وجود created_at
    item['created_at'] ??= DateTime.now().millisecondsSinceEpoch;

    return await _unifiedDb.insertOrderItem(item);
  }

  /// الحصول على عناصر مشروع
  Future<List<Map<String, dynamic>>> getOrderItems(int projectId) async {
    final db = await database;
    return await db.query(
      'order_items',
      where: 'project_id = ?',
      whereArgs: [projectId],
      orderBy: 'created_at ASC',
    );
  }

  /// الحصول على عناصر مشروع (اسم بديل)
  Future<List<Map<String, dynamic>>> getOrderItemsByProject(int projectId) async {
    return await getOrderItems(projectId);
  }

  /// تحديث عنصر طلب
  Future<int> updateOrderItem(int id, Map<String, dynamic> item) async {
    final db = await database;
    return await db.update(
      'order_items',
      item,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف عنصر طلب
  Future<int> deleteOrderItem(int id) async {
    final db = await database;
    return await db.delete(
      'order_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// توليد رقم عنصر جديد
  Future<String> generateNextItemNumber(int projectId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM order_items WHERE project_id = ?',
      [projectId]
    );
    final count = result.first['count'] as int;
    return (count + 1).toString().padLeft(3, '0');
  }

  // ==================== قياسات التقطيع ====================

  /// إدراج قياس تقطيع جديد
  Future<int> insertCuttingMeasurement(Map<String, dynamic> measurement) async {
    final db = await database;
    measurement['created_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('cutting_measurements', measurement);
  }

  /// الحصول على قياسات عنصر طلب
  Future<List<Map<String, dynamic>>> getCuttingMeasurements(int orderItemId) async {
    final db = await database;
    return await db.query(
      'cutting_measurements',
      where: 'order_item_id = ?',
      whereArgs: [orderItemId],
      orderBy: 'created_at ASC',
    );
  }

  /// تحديث قياس تقطيع
  Future<int> updateCuttingMeasurement(int id, Map<String, dynamic> measurement) async {
    final db = await database;
    return await db.update(
      'cutting_measurements',
      measurement,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف قياس تقطيع
  Future<int> deleteCuttingMeasurement(int id) async {
    final db = await database;
    return await db.delete(
      'cutting_measurements',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== قياسات الألواح ====================

  /// إدراج قياس لوح جديد
  Future<int> insertPanelMeasurement(dynamic measurement) async {
    final db = await database;
    Map<String, dynamic> measurementMap;

    if (measurement is Map<String, dynamic>) {
      measurementMap = measurement;
    } else {
      measurementMap = measurement.toMap();
    }

    measurementMap['created_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('panel_measurements', measurementMap);
  }

  /// الحصول على قياسات الألواح لعنصر طلب
  Future<List<Map<String, dynamic>>> getPanelMeasurements(int orderItemId) async {
    final db = await database;
    return await db.query(
      'panel_measurements',
      where: 'order_item_id = ?',
      whereArgs: [orderItemId],
      orderBy: 'created_at ASC',
    );
  }

  /// تحديث قياس لوح
  Future<int> updatePanelMeasurement(int id, dynamic measurement) async {
    final db = await database;
    Map<String, dynamic> measurementMap;

    if (measurement is Map<String, dynamic>) {
      measurementMap = measurement;
    } else {
      measurementMap = measurement.toMap();
    }

    return await db.update(
      'panel_measurements',
      measurementMap,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف قياس لوح
  Future<int> deletePanelMeasurement(int id) async {
    final db = await database;
    return await db.delete(
      'panel_measurements',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // ==================== إدارة المهام ====================

  /// إدراج فئة مهام جديدة
  Future<int> insertTaskCategory(TaskCategory category) async {
    return await _unifiedDb.insertTaskCategory(category.toMap());
  }

  /// الحصول على جميع فئات المهام
  Future<List<TaskCategory>> getAllTaskCategories() async {
    final categories = await _unifiedDb.getAllTaskCategories();
    return categories.map((category) => TaskCategory.fromMap(category)).toList();
  }

  /// إدراج مهمة جديدة
  Future<int> insertTask(Task task) async {
    return await _unifiedDb.insertTask(task.toMap());
  }

  /// الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    final tasks = await _unifiedDb.getAllTasks();
    return tasks.map((task) => Task.fromMap(task)).toList();
  }

  /// الحصول على مهمة بالمعرف
  Future<Task?> getTaskById(int id) async {
    final db = await database;
    final result = await db.query(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? Task.fromMap(result.first) : null;
  }

  /// تحديث مهمة
  Future<int> updateTask(Task task) async {
    final db = await database;
    final taskMap = task.toMap();
    taskMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      'tasks',
      taskMap,
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  /// حذف مهمة
  Future<int> deleteTask(int id) async {
    final db = await database;
    return await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على مهام حسب الفئة
  Future<List<Task>> getTasksByCategory(int categoryId) async {
    final db = await database;
    final result = await db.query(
      'tasks',
      where: 'category_id = ?',
      whereArgs: [categoryId],
      orderBy: 'created_at DESC',
    );
    return result.map((task) => Task.fromMap(task)).toList();
  }

  /// الحصول على مهام حسب الحالة
  Future<List<Task>> getTasksByStatus(int status) async {
    final db = await database;
    final result = await db.query(
      'tasks',
      where: 'status = ?',
      whereArgs: [status],
      orderBy: 'created_at DESC',
    );
    return result.map((task) => Task.fromMap(task)).toList();
  }

  /// البحث في المهام
  Future<List<Task>> searchTasks(String query) async {
    final db = await database;
    final result = await db.query(
      'tasks',
      where: 'title LIKE ? OR description LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return result.map((task) => Task.fromMap(task)).toList();
  }

  /// البحث في مشاريع التقطيع
  Future<List<Map<String, dynamic>>> searchCuttingProjects(String query) async {
    final db = await database;
    return await db.query(
      'cutting_projects',
      where: 'project_number LIKE ? OR customer_name LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
  }

  /// الحصول على إحصائيات المشاريع
  Future<Map<String, dynamic>> getProjectsStatistics() async {
    final db = await database;
    
    // عدد المشاريع
    final projectsResult = await db.rawQuery('SELECT COUNT(*) as count FROM cutting_projects');
    final projectsCount = projectsResult.first['count'] as int;
    
    // عدد العناصر
    final itemsResult = await db.rawQuery('SELECT COUNT(*) as count FROM order_items');
    final itemsCount = itemsResult.first['count'] as int;
    
    // عدد القياسات
    final measurementsResult = await db.rawQuery('SELECT COUNT(*) as count FROM cutting_measurements');
    final measurementsCount = measurementsResult.first['count'] as int;
    
    return {
      'projects_count': projectsCount,
      'items_count': itemsCount,
      'measurements_count': measurementsCount,
    };
  }

  /// الحصول على إحصائيات المهام
  Future<Map<String, dynamic>> getTasksStatistics() async {
    final db = await database;
    
    // عدد المهام حسب الحالة
    final statusResult = await db.rawQuery('''
      SELECT 
        status,
        COUNT(*) as count
      FROM tasks
      GROUP BY status
    ''');
    
    final statusCounts = <int, int>{};
    for (final row in statusResult) {
      statusCounts[row['status'] as int] = row['count'] as int;
    }
    
    // عدد المهام حسب الأولوية
    final priorityResult = await db.rawQuery('''
      SELECT 
        priority,
        COUNT(*) as count
      FROM tasks
      GROUP BY priority
    ''');
    
    final priorityCounts = <int, int>{};
    for (final row in priorityResult) {
      priorityCounts[row['priority'] as int] = row['count'] as int;
    }
    
    return {
      'status_counts': statusCounts,
      'priority_counts': priorityCounts,
      'total_tasks': statusCounts.values.fold(0, (sum, count) => sum + count),
    };
  }
}
