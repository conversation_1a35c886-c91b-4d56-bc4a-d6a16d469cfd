
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../../l10n/app_localizations.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';
import '../../services/unified_treasury_service.dart' as unified;
import '../../providers/database_provider.dart';
import 'treasury_transaction_form.dart';
import '../../services/currency_service.dart';

class TreasurySummaryScreen extends StatefulWidget {
  const TreasurySummaryScreen({super.key});

  @override
  State<TreasurySummaryScreen> createState() => _TreasurySummaryScreenState();
}

class _TreasurySummaryScreenState extends State<TreasurySummaryScreen> with SingleTickerProviderStateMixin {
  late unified.UnifiedTreasuryService _databaseHelper;
  List<Treasury> _treasuries = [];
  bool _isLoading = true;
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _totalBalance = 0.0;
  Map<String, double> _treasuryBalances = {};

  // New properties for enhanced reporting
  List<Map<String, dynamic>> _monthlyReports = [];
  List<Map<String, dynamic>> _topIncomeCategories = [];
  List<Map<String, dynamic>> _topExpenseCategories = [];
  DateTimeRange _dateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 180)),
    end: DateTime.now(),
  );
  late TabController _tabController;

  // Report generation state
  bool _generatingReport = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
    @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _databaseHelper = DatabaseProvider.of(context);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final treasuriesData = await _databaseHelper.getAllTreasuries();
      final treasuries = treasuriesData.map((data) => Treasury.fromMap(data)).toList();
      double totalIncome = 0.0;
      double totalExpenses = 0.0;
      double totalBalance = 0.0;
      Map<String, double> treasuryBalances = {};

      for (final treasury in treasuries) {
        final stats = await _databaseHelper.getTreasuryStatistics(treasury.id!);
        totalIncome += stats['total_income'] as double? ?? 0.0;
        totalExpenses += stats['total_expenses'] as double? ?? 0.0;
        totalBalance += treasury.currentBalance;
        treasuryBalances[treasury.name] = treasury.currentBalance;
      }
        // Load additional data for enhanced reporting
      final monthlyReports = <Map<String, dynamic>>[];
      final topIncomeCategories = <Map<String, dynamic>>[];
      final topExpenseCategories = <Map<String, dynamic>>[];

      setState(() {
        _treasuries = treasuries;
        _totalIncome = totalIncome;
        _totalExpenses = totalExpenses;
        _totalBalance = totalBalance;
        _treasuryBalances = treasuryBalances;
        _monthlyReports = monthlyReports;
        _topIncomeCategories = topIncomeCategories;
        _topExpenseCategories = topExpenseCategories;
        _isLoading = false;
      });    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading data: $e');
    }
  }

  Future<void> _selectDateRange() async {
    final newDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: _dateRange,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      setState(() {
        _dateRange = newDateRange;
      });
      _loadData();
    }
  }

  // Export report to clipboard (simplified for now)
  Future<void> _exportReport() async {
    setState(() {
      _generatingReport = true;
    });

    try {
      final currencyFormat = NumberFormat("#,##0.00");
      final dateFormat = DateFormat('yyyy-MM-dd');

      final buffer = StringBuffer();

      // Report header
      buffer.writeln('TREASURY SUMMARY REPORT');
      buffer.writeln('======================');
      buffer.writeln('Date Range: ${dateFormat.format(_dateRange.start)} to ${dateFormat.format(_dateRange.end)}');
      buffer.writeln('Generated: ${dateFormat.format(DateTime.now())}');
      buffer.writeln();

      // Overall totals
      buffer.writeln('OVERALL SUMMARY');
      buffer.writeln('--------------');
      buffer.writeln('Total Income: ${currencyFormat.format(_totalIncome)}');
      buffer.writeln('Total Expenses: ${currencyFormat.format(_totalExpenses)}');
      buffer.writeln('Total Balance: ${currencyFormat.format(_totalBalance)}');
      buffer.writeln();

      // Treasury breakdown
      buffer.writeln('TREASURY BREAKDOWN');
      buffer.writeln('------------------');
      for (final entry in _treasuryBalances.entries) {
        buffer.writeln('${entry.key}: ${currencyFormat.format(entry.value)}');
      }
      buffer.writeln();

      // Monthly breakdown
      buffer.writeln('MONTHLY BREAKDOWN');
      buffer.writeln('-----------------');
      for (final report in _monthlyReports) {
        final date = DateTime.parse(report['date'] as String);
        buffer.writeln('${DateFormat('MMMM yyyy').format(date)}: '
            'Income: ${currencyFormat.format(report['income'] as double)}, '
            'Expenses: ${currencyFormat.format(report['expenses'] as double)}, '
            'Balance: ${currencyFormat.format(report['balance'] as double)}');
      }
      buffer.writeln();

      // Top income categories
      buffer.writeln('TOP INCOME SOURCES');
      buffer.writeln('-----------------');
      for (final category in _topIncomeCategories) {
        buffer.writeln('${category['description']}: ${currencyFormat.format(category['total_income'])}');
      }
      buffer.writeln();

      // Top expense categories
      buffer.writeln('TOP EXPENSE CATEGORIES');
      buffer.writeln('---------------------');
      for (final category in _topExpenseCategories) {
        buffer.writeln('${category['description']}: ${currencyFormat.format(category['total_expenses'])}');
      }

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: buffer.toString()));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Report copied to clipboard'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error generating report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _generatingReport = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.treasurySummary),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          // Date range selector
          IconButton(
            icon: const Icon(Icons.date_range),
            tooltip: localizations.selectDateRange,
            onPressed: _selectDateRange,
          ),
          // Export report
          IconButton(
            icon: _generatingReport
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2)
                  )
                : const Icon(Icons.ios_share),
            tooltip: localizations.exportReport,
            onPressed: _generatingReport ? null : _exportReport,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(text: localizations.overview),
            Tab(text: localizations.trends),
            Tab(text: localizations.categories),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // Overview tab
                RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSummaryCards(localizations, theme),
                          const SizedBox(height: 24),
                          _buildIncomeExpensesChart(localizations, theme),
                          const SizedBox(height: 24),
                          _buildTreasuriesList(localizations, theme),
                        ],
                      ),
                    ),
                  ),
                ),
                // Trends tab
                RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDateRangeIndicator(localizations, theme),
                          const SizedBox(height: 16),
                          _buildMonthlyTrendsChart(localizations, theme),
                          const SizedBox(height: 24),
                          if (_treasuryBalances.isNotEmpty)
                            _buildTreasuryBalanceChart(localizations, theme),
                        ],
                      ),
                    ),
                  ),
                ),
                // Categories tab
                RefreshIndicator(
                  onRefresh: _loadData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDateRangeIndicator(localizations, theme),
                          const SizedBox(height: 16),
                          _buildCategoryCharts(localizations, theme),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildDateRangeIndicator(AppLocalizations localizations, ThemeData theme) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(Icons.date_range, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${localizations.dateRange}: ${DateFormat('yyyy-MM-dd').format(_dateRange.start)} to ${DateFormat('yyyy-MM-dd').format(_dateRange.end)}',
                style: theme.textTheme.bodyMedium,
              ),
            ),
            TextButton(
              onPressed: _selectDateRange,
              child: Text(localizations.change),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(AppLocalizations localizations, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.overallSummary,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: FutureBuilder<String>(
                future: CurrencyService.instance.formatAmount(_totalIncome),
                builder: (context, snapshot) {
                  return _buildSummaryCard(
                    title: localizations.totalIncome,
                    value: snapshot.data ?? CurrencyService.instance.formatAmountOnly(_totalIncome),
                    icon: Icons.arrow_downward,
                    color: Colors.green,
                    theme: theme,
                  );
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: FutureBuilder<String>(
                future: CurrencyService.instance.formatAmount(_totalExpenses),
                builder: (context, snapshot) {
                  return _buildSummaryCard(
                    title: localizations.totalExpenses,
                    value: snapshot.data ?? CurrencyService.instance.formatAmountOnly(_totalExpenses),
                    icon: Icons.arrow_upward,
                    color: Colors.red,
                    theme: theme,
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        FutureBuilder<String>(
          future: CurrencyService.instance.formatAmount(_totalBalance),
          builder: (context, snapshot) {
            return _buildSummaryCard(
              title: localizations.totalBalance,
              value: snapshot.data ?? CurrencyService.instance.formatAmountOnly(_totalBalance),
              icon: Icons.account_balance_wallet,
              color: _totalBalance >= 0 ? Colors.green : Colors.red,
              theme: theme,
            );
          },
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required ThemeData theme,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeExpensesChart(AppLocalizations localizations, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.incomeVsExpenses,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  barGroups: [
                    BarChartGroupData(
                      x: 0,
                      barRods: [
                        BarChartRodData(
                          toY: _totalIncome,
                          color: Colors.green,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            topRight: Radius.circular(6),
                          ),
                        ),
                      ],
                    ),
                    BarChartGroupData(
                      x: 1,
                      barRods: [
                        BarChartRodData(
                          toY: _totalExpenses,
                          color: Colors.red,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            topRight: Radius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  ],
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          String text = '';
                          if (value == 0) {
                            text = localizations.income;
                          } else if (value == 1) {
                            text = localizations.expenses;
                          }
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              text,
                              style: TextStyle(
                                color: theme.colorScheme.onSurface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  gridData: FlGridData(show: false),
                  borderData: FlBorderData(show: false),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreasuryBalanceChart(AppLocalizations localizations, ThemeData theme) {
    final entries = _treasuryBalances.entries.toList();
    if (entries.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.treasuryBalances,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  barGroups: List.generate(
                    entries.length,
                    (index) => BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: entries[index].value.abs(),
                          color: entries[index].value >= 0 ? Colors.green : Colors.red,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            topRight: Radius.circular(6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < entries.length) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                entries[index].key,
                                style: TextStyle(
                                  color: theme.colorScheme.onSurface,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ),
                  gridData: FlGridData(show: false),
                  borderData: FlBorderData(show: false),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // New chart for monthly trends
  Widget _buildMonthlyTrendsChart(AppLocalizations localizations, ThemeData theme) {
    if (_monthlyReports.isEmpty) {
      return _buildEmptyDataCard(
        localizations.noDataAvailable,
        Icons.timeline_outlined,
        theme
      );
    }

    // Calculate max value for Y-axis scale
    double maxY = 0;
    for (final report in _monthlyReports) {
      maxY = math.max(maxY, math.max(report['income'] as double, report['expenses'] as double));
    }

    // Add 10% padding to max value
    maxY = maxY * 1.1;

    // Create line chart data points
    final incomeSpots = <FlSpot>[];
    final expenseSpots = <FlSpot>[];
    final balanceSpots = <FlSpot>[];

    for (int i = 0; i < _monthlyReports.length; i++) {
      final report = _monthlyReports[i];
      incomeSpots.add(FlSpot(i.toDouble(), report['income'] as double));
      expenseSpots.add(FlSpot(i.toDouble(), report['expenses'] as double));
      balanceSpots.add(FlSpot(i.toDouble(), report['balance'] as double));
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations.monthlyTrends,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: LineChart(
                LineChartData(
                  lineTouchData: LineTouchData(
                    touchTooltipData: LineTouchTooltipData(
                      tooltipBgColor: theme.colorScheme.surface.withValues(alpha: 0.3),
                      getTooltipItems: (List<LineBarSpot> touchedSpots) {
                        return touchedSpots.map((spot) {
                          final index = spot.x.toInt();
                          if (index >= 0 && index < _monthlyReports.length) {
                            final report = _monthlyReports[index];
                            final date = DateTime.parse(report['date'] as String);
                            final month = DateFormat('MMM yyyy').format(date);

                            String label = '';
                            Color color = Colors.grey;

                            if (spot.barIndex == 0) {
                              label = '${localizations.income}: ${NumberFormat("#,##0.00").format(spot.y)}';
                              color = Colors.green;
                            } else if (spot.barIndex == 1) {
                              label = '${localizations.expenses}: ${NumberFormat("#,##0.00").format(spot.y)}';
                              color = Colors.red;
                            } else if (spot.barIndex == 2) {
                              label = '${localizations.balance}: ${NumberFormat("#,##0.00").format(spot.y)}';
                              color = Colors.blue;
                            }

                            return LineTooltipItem(
                              '$month\n$label',
                              TextStyle(
                                color: color,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          }
                          return null;
                        }).toList();
                      },
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: theme.dividerColor.withValues(alpha: 0.3),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 35,
                        getTitlesWidget: (value, meta) {
                          if (value == 0) {
                            return const SizedBox.shrink();
                          }
                          final formattedValue = NumberFormat.compact().format(value);
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            space: 8,
                            child: Text(
                              formattedValue,
                              style: TextStyle(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                                fontSize: 10,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < _monthlyReports.length && index % 2 == 0) {
                            final report = _monthlyReports[index];
                            return SideTitleWidget(
                              axisSide: meta.axisSide,
                              space: 8,
                              child: Text(
                                DateFormat('MMM yy').format(DateTime.parse(report['date'] as String)),
                                style: TextStyle(
                                  color: theme.colorScheme.onSurface,
                                  fontSize: 10,
                                ),
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border(
                      bottom: BorderSide(
                        color: theme.dividerColor,
                        width: 1,
                      ),
                      left: BorderSide(
                        color: theme.dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  minX: 0,
                  maxX: (_monthlyReports.length - 1).toDouble(),
                  minY: 0,
                  maxY: maxY,
                  lineBarsData: [
                    // Income line
                    LineChartBarData(
                      spots: incomeSpots,
                      isCurved: true,
                      color: Colors.green,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.green.withValues(alpha: 0.3),
                      ),
                    ),
                    // Expenses line
                    LineChartBarData(
                      spots: expenseSpots,
                      isCurved: true,
                      color: Colors.red,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    // Balance line
                    LineChartBarData(
                      spots: balanceSpots,
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem(Colors.green, localizations.income, theme),
                const SizedBox(width: 16),
                _buildLegendItem(Colors.red, localizations.expenses, theme),
                const SizedBox(width: 16),
                _buildLegendItem(Colors.blue, localizations.balance, theme),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label, ThemeData theme) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall,
        ),
      ],
    );
  }

  // Chart for category breakdown
  Widget _buildCategoryCharts(AppLocalizations localizations, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Income categories
        _buildPieChart(
          localizations.topIncomeSources,
          _topIncomeCategories,
          'description',
          'total_income',
          Colors.green,
          localizations,
          theme,
        ),
        const SizedBox(height: 24),
        // Expense categories
        _buildPieChart(
          localizations.topExpenseCategories,
          _topExpenseCategories,
          'description',
          'total_expenses',
          Colors.red,
          localizations,
          theme,
        ),
      ],
    );
  }

  Widget _buildPieChart(
    String title,
    List<Map<String, dynamic>> data,
    String labelKey,
    String valueKey,
    Color baseColor,
    AppLocalizations localizations,
    ThemeData theme,
  ) {
    if (data.isEmpty) {
      return _buildEmptyDataCard(
        localizations.noDataAvailable,
        Icons.pie_chart_outline,
        theme
      );
    }

    // Calculate total value for percentage calculations
    double totalValue = 0;
    for (final item in data) {
      totalValue += (item[valueKey] as double? ?? 0.0);
    }

    // Generate pie sections
    final sections = <PieChartSectionData>[];
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final value = item[valueKey] as double? ?? 0.0;
      final percentage = totalValue > 0 ? value / totalValue : 0.0;

      // Generate color variations based on index
      final color = HSLColor.fromColor(baseColor)
          .withLightness(0.3 + ((i + 1) / data.length) * 0.5)
          .toColor();

      sections.add(
        PieChartSectionData(
          color: color,
          value: value,
          title: '${(percentage * 100).toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: sections,
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                  pieTouchData: PieTouchData(
                    touchCallback: (FlTouchEvent event, pieTouchResponse) {},
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Legend
            ...List.generate(data.length, (index) {
              final item = data[index];
              final value = item[valueKey] as double? ?? 0.0;
              final percentage = totalValue > 0 ? value / totalValue : 0.0;

              // Generate color variations based on index
              final color = HSLColor.fromColor(baseColor)
                  .withLightness(0.3 + ((index + 1) / data.length) * 0.5)
                  .toColor();

              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.rectangle,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        item[labelKey] as String? ?? '',
                        style: theme.textTheme.bodyMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      NumberFormat("#,##0.00").format(value),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${(percentage * 100).toStringAsFixed(1)}%)',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyDataCard(String message, IconData icon, ThemeData theme) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 64,
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTreasuriesList(AppLocalizations localizations, ThemeData theme) {
    if (_treasuries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              size: 80,
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              localizations.noTreasuriesFound,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.treasuries,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_treasuries.length, (index) {
          final treasury = _treasuries[index];
          return Card(
            elevation: 1,
            margin: const EdgeInsets.only(bottom: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              title: Text(
                treasury.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                DateFormat('yyyy-MM-dd').format(treasury.date),
              ),
              trailing: Text(
                NumberFormat("#,##0.00").format(treasury.finalBalance),
                style: TextStyle(
                  color: treasury.finalBalance >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => TreasuryTransactionListScreen(treasury: treasury),
                  ),
                ).then((_) => _loadData());
              },
            ),
          );
        }),
      ],
    );
  }
}

class TreasuryTransactionListScreen extends StatefulWidget {
  final Treasury treasury;

  const TreasuryTransactionListScreen({
    super.key,
    required this.treasury,
  });

  @override
  State<TreasuryTransactionListScreen> createState() => _TreasuryTransactionListScreenState();
}

class _TreasuryTransactionListScreenState extends State<TreasuryTransactionListScreen> {
  late unified.UnifiedTreasuryService _databaseHelper;
  List<TreasuryTransaction> _transactions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _databaseHelper = DatabaseProvider.of(context);
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transactionsData = await _databaseHelper.getTreasuryTransactions(widget.treasury.id!);
      final transactions = transactionsData.map((data) => TreasuryTransaction.fromMap(data)).toList();
      setState(() {
        _transactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading transactions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.treasury.name),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildTreasurySummary(),
                Expanded(
                  child: _transactions.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.receipt_long,
                                size: 80,
                                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                localizations.noTransactions,
                                style: theme.textTheme.titleLarge,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : ListView.separated(
                          padding: const EdgeInsets.all(16),
                          itemCount: _transactions.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final transaction = _transactions[index];
                            return ListTile(
                              title: Text(
                                transaction.description,
                                style: const TextStyle(fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    DateFormat('yyyy-MM-dd').format(transaction.date),
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                  if (transaction.notes.isNotEmpty)
                                    Text(
                                      transaction.notes,
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                ],
                              ),
                              trailing: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  if (transaction.income > 0)
                                    Text(
                                      '+${NumberFormat("#,##0.00").format(transaction.income)}',
                                      style: const TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  if (transaction.expenses > 0)
                                    Text(
                                      '-${NumberFormat("#,##0.00").format(transaction.expenses)}',
                                      style: const TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => TreasuryTransactionForm(treasuryId: widget.treasury.id!),
            ),
          ).then((_) => _loadTransactions());
        },
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTreasurySummary() {
    final localizations = AppLocalizations.of(context)!;
    final currencyFormat = NumberFormat("#,##0.00");

    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildInfoBox(
                    localizations.previousBalance,
                    currencyFormat.format(widget.treasury.previousBalance),
                    Colors.blue.shade100,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildInfoBox(
                    localizations.currentBalance,
                    currencyFormat.format(widget.treasury.currentBalance),
                    Colors.green.shade100,
                    textColor: Colors.green.shade700,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildInfoBox(
                    localizations.finalBalance,
                    currencyFormat.format(widget.treasury.finalBalance),
                    widget.treasury.finalBalance >= 0 ? Colors.green.shade100 : Colors.red.shade100,
                    textColor: widget.treasury.finalBalance >= 0 ? Colors.green.shade700 : Colors.red.shade700,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoBox(String label, String value, Color backgroundColor, {Color? textColor}) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
