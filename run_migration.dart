import 'dart:io';

/// تشغيل عملية النقل الكاملة للخدمات الموحدة
Future<void> main() async {
  print('🚀 بدء عملية النقل الكاملة للخدمات الموحدة...\n');

  try {
    // الخطوة 1: التحقق من وجود قاعدة البيانات الموحدة
    print('1️⃣ التحقق من قاعدة البيانات الموحدة...');
    await checkUnifiedDatabase();
    
    // الخطوة 2: نقل البيانات إذا لم تكن منقولة
    print('\n2️⃣ نقل البيانات إلى قاعدة البيانات الموحدة...');
    await migrateData();
    
    // الخطوة 3: تحديث ملفات التطبيق
    print('\n3️⃣ تحديث ملفات التطبيق لاستخدام الخدمات الموحدة...');
    await updateAppFiles();
    
    // الخطوة 4: اختبار النظام الجديد
    print('\n4️⃣ اختبار النظام الجديد...');
    await testUnifiedSystem();
    
    print('\n🎉 تم اكتمال عملية النقل بنجاح!');
    print('📋 الخطوات التالية:');
    print('   1. تشغيل: flutter clean');
    print('   2. تشغيل: flutter pub get');
    print('   3. إعادة تشغيل التطبيق');
    print('   4. اختبار جميع الوظائف');
    
  } catch (e) {
    print('❌ خطأ في عملية النقل: $e');
    print('💡 يُنصح بمراجعة الأخطاء وإعادة المحاولة');
  }
}

/// التحقق من وجود قاعدة البيانات الموحدة
Future<void> checkUnifiedDatabase() async {
  try {
    final result = await Process.run('dart', ['run', 'test_unified_database.dart']);
    if (result.exitCode == 0) {
      print('   ✅ قاعدة البيانات الموحدة موجودة وتعمل بشكل صحيح');
    } else {
      print('   ⚠️ قاعدة البيانات الموحدة تحتاج إنشاء...');
      await createUnifiedDatabase();
    }
  } catch (e) {
    print('   ⚠️ إنشاء قاعدة البيانات الموحدة...');
    await createUnifiedDatabase();
  }
}

/// إنشاء قاعدة البيانات الموحدة
Future<void> createUnifiedDatabase() async {
  try {
    final result = await Process.run('dart', ['run', 'recreate_unified_database.dart']);
    if (result.exitCode == 0) {
      print('   ✅ تم إنشاء قاعدة البيانات الموحدة بنجاح');
    } else {
      throw Exception('فشل في إنشاء قاعدة البيانات الموحدة');
    }
  } catch (e) {
    throw Exception('خطأ في إنشاء قاعدة البيانات الموحدة: $e');
  }
}

/// نقل البيانات إلى قاعدة البيانات الموحدة
Future<void> migrateData() async {
  try {
    final result = await Process.run('dart', ['run', 'simple_migration_test.dart']);
    if (result.exitCode == 0) {
      print('   ✅ تم نقل البيانات بنجاح');
      
      // عرض ملخص البيانات المنقولة
      final output = result.stdout as String;
      final lines = output.split('\n');
      
      for (final line in lines) {
        if (line.contains('✅') && (line.contains('نقل') || line.contains('تم'))) {
          print('      $line');
        }
      }
    } else {
      print('   ⚠️ تحذير: قد تكون هناك مشاكل في نقل البيانات');
      print('   📋 تفاصيل: ${result.stderr}');
    }
  } catch (e) {
    print('   ⚠️ تحذير: خطأ في نقل البيانات: $e');
    print('   💡 يمكن المتابعة إذا كانت البيانات منقولة مسبقاً');
  }
}

/// تحديث ملفات التطبيق
Future<void> updateAppFiles() async {
  try {
    final result = await Process.run('dart', ['run', 'update_app_to_unified_services.dart']);
    if (result.exitCode == 0) {
      print('   ✅ تم تحديث ملفات التطبيق بنجاح');
      
      // عرض ملخص التحديثات
      final output = result.stdout as String;
      final lines = output.split('\n');
      
      for (final line in lines) {
        if (line.contains('📊') || line.contains('✅') || line.contains('📁')) {
          print('      $line');
        }
      }
    } else {
      throw Exception('فشل في تحديث ملفات التطبيق');
    }
  } catch (e) {
    throw Exception('خطأ في تحديث ملفات التطبيق: $e');
  }
}

/// اختبار النظام الجديد
Future<void> testUnifiedSystem() async {
  try {
    final result = await Process.run('dart', ['run', 'test_unified_services_simple.dart']);
    if (result.exitCode == 0) {
      print('   ✅ النظام الجديد يعمل بشكل صحيح');
      
      // عرض ملخص الاختبارات
      final output = result.stdout as String;
      final lines = output.split('\n');
      
      for (final line in lines) {
        if (line.contains('✅') && line.contains('تعمل بشكل صحيح')) {
          print('      $line');
        }
        if (line.contains('📊') && line.contains('الإحصائيات النهائية')) {
          print('      $line');
          // عرض الإحصائيات التالية
          final index = lines.indexOf(line);
          for (int i = index + 1; i < lines.length && i < index + 5; i++) {
            if (lines[i].trim().isNotEmpty && lines[i].contains('إجمالي')) {
              print('         ${lines[i]}');
            }
          }
          break;
        }
      }
    } else {
      throw Exception('فشل في اختبار النظام الجديد');
    }
  } catch (e) {
    throw Exception('خطأ في اختبار النظام الجديد: $e');
  }
}

/// إنشاء تقرير النقل
Future<void> generateMigrationReport() async {
  final report = StringBuffer();
  final timestamp = DateTime.now().toIso8601String();
  
  report.writeln('# تقرير نقل الخدمات الموحدة');
  report.writeln('');
  report.writeln('**تاريخ النقل:** $timestamp');
  report.writeln('');
  report.writeln('## ملخص العملية');
  report.writeln('');
  report.writeln('✅ تم إنشاء قاعدة البيانات الموحدة');
  report.writeln('✅ تم نقل البيانات من قواعد البيانات المنفصلة');
  report.writeln('✅ تم تحديث ملفات التطبيق');
  report.writeln('✅ تم اختبار النظام الجديد');
  report.writeln('');
  report.writeln('## الخدمات الموحدة الجديدة');
  report.writeln('');
  report.writeln('- **UnifiedInvoiceService** - إدارة الفواتير والعملاء والموردين');
  report.writeln('- **UnifiedTreasuryService** - إدارة الخزينة والمعاملات');
  report.writeln('- **UnifiedCuttingService** - إدارة التقطيع والمهام');
  report.writeln('- **UnifiedAluminumService** - إدارة قطاعات وعروض أسعار الألومنيوم');
  report.writeln('- **UnifiedUpvcService** - إدارة قطاعات وعروض أسعار uPVC');
  report.writeln('');
  report.writeln('## المزايا الجديدة');
  report.writeln('');
  report.writeln('- 🚀 أداء أفضل مع قاعدة بيانات واحدة موحدة');
  report.writeln('- 🔧 سهولة الصيانة والتطوير');
  report.writeln('- 📊 إمكانيات متقدمة للتحليل والتقارير');
  report.writeln('- 🔒 استقرار أكبر وأمان أفضل');
  report.writeln('');
  report.writeln('## الخطوات التالية');
  report.writeln('');
  report.writeln('1. تشغيل `flutter clean && flutter pub get`');
  report.writeln('2. إعادة تشغيل التطبيق');
  report.writeln('3. اختبار جميع الوظائف');
  report.writeln('4. مراقبة الأداء والاستقرار');
  
  final reportFile = File('migration_report_$timestamp.md');
  await reportFile.writeAsString(report.toString());
  
  print('📄 تم إنشاء تقرير النقل: ${reportFile.path}');
}
