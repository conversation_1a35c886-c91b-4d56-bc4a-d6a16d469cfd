import 'package:flutter/material.dart';
import '../models/profile_series.dart';
import '../models/aluminum_profile.dart';

/// نافذة حوار للتعامل مع المجموعات الموجودة عند الاستيراد
class ExistingSeriesDialog extends StatelessWidget {
  final ProfileSeries existingSeries;
  final ProfileSeries onlineSeries;

  const ExistingSeriesDialog({
    Key? key,
    required this.existingSeries,
    required this.onlineSeries,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 28,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              'مجموعة موجودة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade700,
              ),
            ),
          ),
        ],
      ),
      content: Container(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المجموعة
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تم العثور على مجموعة مطابقة في البيانات المحلية:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  SizedBox(height: 12),
                  _buildInfoRow('اسم المجموعة:', existingSeries.name),
                  _buildInfoRow('كود المجموعة:', existingSeries.code),
                  _buildInfoRow('النوع:', existingSeries.type == ProfileType.hinge ? 'مفصلي' : 'سحاب'),
                  _buildInfoRow('الوصف:', existingSeries.description.isNotEmpty ? existingSeries.description : 'غير محدد'),
                ],
              ),
            ),
            SizedBox(height: 20),
            
            // رسالة التوضيح
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'يرجى اختيار كيفية التعامل مع هذه المجموعة:',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        // زر الإلغاء
        TextButton(
          onPressed: () => Navigator.of(context).pop(null),
          style: TextButton.styleFrom(
            foregroundColor: Colors.grey.shade600,
          ),
          child: Text(
            'إلغاء',
            style: TextStyle(fontSize: 16),
          ),
        ),
        
        SizedBox(width: 8),
        
        // زر المسح والاستيراد
        ElevatedButton.icon(
          onPressed: () => Navigator.of(context).pop('replace'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red.shade600,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          icon: Icon(Icons.delete_forever, size: 18),
          label: Text(
            'مسح واستيراد جديد',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
        
        SizedBox(width: 8),
        
        // زر التحديث
        ElevatedButton.icon(
          onPressed: () => Navigator.of(context).pop('update'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green.shade600,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          icon: Icon(Icons.update, size: 18),
          label: Text(
            'تحديث البيانات',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      ],
      actionsPadding: EdgeInsets.fromLTRB(24, 0, 24, 24),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }
}