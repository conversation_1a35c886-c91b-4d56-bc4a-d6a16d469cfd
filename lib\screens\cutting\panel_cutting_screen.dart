// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/order_item.dart';
import '../../models/panel_cutting_models.dart';
import '../../services/unified_cutting_service.dart';
import 'panel_optimization_screen.dart';

class PanelCuttingScreen extends StatefulWidget {
  final OrderItem orderItem;

  const PanelCuttingScreen({
    super.key,
    required this.orderItem,
  });

  @override
  State<PanelCuttingScreen> createState() => _PanelCuttingScreenState();
}

class _PanelCuttingScreenState extends State<PanelCuttingScreen> {
  // Controllers for input fields
  final TextEditingController _panelWidthController = TextEditingController();
  final TextEditingController _panelHeightController = TextEditingController();
  final TextEditingController _pieceWidthController = TextEditingController();
  final TextEditingController _pieceHeightController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _typeController = TextEditingController();
  final TextEditingController _numberController = TextEditingController();

  // Focus nodes
  final FocusNode _pieceWidthFocusNode = FocusNode();
  final FocusNode _pieceHeightFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();
  final FocusNode _typeFocusNode = FocusNode();
  final FocusNode _numberFocusNode = FocusNode();

  // Database helper
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();

  // List to store cutting measurements
  List<PanelMeasurement> _measurements = [];

  // Panel dimensions
  double _panelWidth = 280.0;
  double _panelHeight = 140.0;
  bool _allowRotation = true;
  double _cuttingKerf = 3.0;

  @override
  void initState() {
    super.initState();
    _panelWidthController.text = _panelWidth.toString();
    _panelHeightController.text = _panelHeight.toString();    _typeController.text = widget.orderItem.itemType;
    _numberController.text = widget.orderItem.itemNumber;
    _loadMeasurements();
  }

  @override
  void dispose() {
    _panelWidthController.dispose();
    _panelHeightController.dispose();
    _pieceWidthController.dispose();
    _pieceHeightController.dispose();
    _quantityController.dispose();
    _typeController.dispose();
    _numberController.dispose();
    _pieceWidthFocusNode.dispose();
    _pieceHeightFocusNode.dispose();
    _quantityFocusNode.dispose();
    _typeFocusNode.dispose();
    _numberFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadMeasurements() async {
    try {
      final measurementsData = await _databaseHelper.getPanelMeasurements(widget.orderItem.id!);
      final measurements = measurementsData.map((data) => PanelMeasurement.fromMap(data)).toList();
      setState(() {
        _measurements = measurements;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القياسات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _addMeasurement() async {
    if (_pieceWidthController.text.isEmpty ||
        _pieceHeightController.text.isEmpty ||
        _quantityController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final double pieceWidth = double.parse(_pieceWidthController.text);
      final double pieceHeight = double.parse(_pieceHeightController.text);
      final int quantity = int.parse(_quantityController.text);

      if (pieceWidth <= 0 || pieceHeight <= 0 || quantity <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جميع القيم يجب أن تكون أكبر من الصفر'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      final measurement = PanelMeasurement(
        orderItemId: widget.orderItem.id!,
        pieceWidth: pieceWidth,
        pieceHeight: pieceHeight,
        quantity: quantity,
        type: _typeController.text,
        number: _numberController.text,
      );

      final id = await _databaseHelper.insertPanelMeasurement(measurement);
      measurement.id = id;

      setState(() {
        _measurements.add(measurement);
      });

      // Clear input fields
      _pieceWidthController.clear();
      _pieceHeightController.clear();
      _quantityController.clear();

      // Focus on first field
      _pieceWidthFocusNode.requestFocus();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تمت إضافة القياس بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة القياس: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteMeasurement(int index) async {
    try {
      final measurement = _measurements[index];
      if (measurement.id != null) {
        await _databaseHelper.deletePanelMeasurement(measurement.id!);
      }

      setState(() {
        _measurements.removeAt(index);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف القياس بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حذف القياس: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _updatePanelDimensions() {
    try {
      final double width = double.parse(_panelWidthController.text);
      final double height = double.parse(_panelHeightController.text);

      if (width > 0 && height > 0) {
        setState(() {
          _panelWidth = width;
          _panelHeight = height;
        });
      }
    } catch (e) {
      // Invalid input, ignore
    }
  }
  void _optimizeCutting() {
    if (_measurements.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة قياسات أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PanelOptimizationScreen(
          orderItem: widget.orderItem,
          panelWidth: _panelWidth,
          panelHeight: _panelHeight,
          measurements: _measurements,
          cuttingKerf: _cuttingKerf,
        ),
      ),
    );
  }

  // Edit measurement inline
  void _editMeasurement(int index, String field) {
    final measurement = _measurements[index];
    String currentValue = '';

    switch (field) {
      case 'pieceWidth':
        currentValue = measurement.pieceWidth.toString();
        break;
      case 'pieceHeight':
        currentValue = measurement.pieceHeight.toString();
        break;
      case 'quantity':
        currentValue = measurement.quantity.toString();
        break;
      case 'type':
        currentValue = measurement.type;
        break;
      case 'number':
        currentValue = measurement.number;
        break;
    }

    _showQuickEditDialog(index, field, currentValue);
  }

  // Show quick edit dialog for single field
  void _showQuickEditDialog(int index, String field, String currentValue) {
    final controller = TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل ${_getFieldLabel(field)}'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: _getFieldLabel(field),
            border: const OutlineInputBorder(),
          ),
          keyboardType: field == 'quantity' || field == 'pieceWidth' || field == 'pieceHeight'
              ? TextInputType.number
              : TextInputType.text,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              _updateMeasurementField(index, field, controller.text);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  String _getFieldLabel(String field) {
    switch (field) {
      case 'pieceWidth':
        return 'العرض';
      case 'pieceHeight':
        return 'الارتفاع';
      case 'quantity':
        return 'العدد';
      case 'type':
        return 'النوع';
      case 'number':
        return 'الرقم';
      default:
        return field;
    }
  }

  // Show edit dialog for complete editing
  void _showEditDialog(int index) {
    final measurement = _measurements[index];

    final pieceWidthController = TextEditingController(text: measurement.pieceWidth.toString());
    final pieceHeightController = TextEditingController(text: measurement.pieceHeight.toString());
    final quantityController = TextEditingController(text: measurement.quantity.toString());
    final typeController = TextEditingController(text: measurement.type == '-' ? '' : measurement.type);
    final numberController = TextEditingController(text: measurement.number == '-' ? '' : measurement.number);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل القطعة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: pieceWidthController,
                      decoration: const InputDecoration(
                        labelText: 'العرض (سم)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: pieceHeightController,
                      decoration: const InputDecoration(
                        labelText: 'الارتفاع (سم)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                decoration: const InputDecoration(
                  labelText: 'الكمية',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: typeController,
                decoration: const InputDecoration(
                  labelText: 'النوع (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: numberController,
                decoration: const InputDecoration(
                  labelText: 'الرقم (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final newMeasurement = PanelMeasurement(
                id: measurement.id,
                orderItemId: measurement.orderItemId,
                pieceWidth: double.tryParse(pieceWidthController.text) ?? measurement.pieceWidth,
                pieceHeight: double.tryParse(pieceHeightController.text) ?? measurement.pieceHeight,
                quantity: int.tryParse(quantityController.text) ?? measurement.quantity,
                type: typeController.text.trim().isEmpty ? '-' : typeController.text.trim(),
                number: numberController.text.trim().isEmpty ? '-' : numberController.text.trim(),
              );

              _updateMeasurement(index, newMeasurement);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // Update specific field of measurement
  void _updateMeasurementField(int index, String field, String newValue) {
    final measurement = _measurements[index];

    PanelMeasurement updatedMeasurement;
    switch (field) {
      case 'pieceWidth':
        final width = double.tryParse(newValue) ?? measurement.pieceWidth;
        updatedMeasurement = PanelMeasurement(
          id: measurement.id,
          orderItemId: measurement.orderItemId,
          pieceWidth: width,
          pieceHeight: measurement.pieceHeight,
          quantity: measurement.quantity,
          type: measurement.type,
          number: measurement.number,
        );
        break;
      case 'pieceHeight':
        final height = double.tryParse(newValue) ?? measurement.pieceHeight;
        updatedMeasurement = PanelMeasurement(
          id: measurement.id,
          orderItemId: measurement.orderItemId,
          pieceWidth: measurement.pieceWidth,
          pieceHeight: height,
          quantity: measurement.quantity,
          type: measurement.type,
          number: measurement.number,
        );
        break;
      case 'quantity':
        final quantity = int.tryParse(newValue) ?? measurement.quantity;
        updatedMeasurement = PanelMeasurement(
          id: measurement.id,
          orderItemId: measurement.orderItemId,
          pieceWidth: measurement.pieceWidth,
          pieceHeight: measurement.pieceHeight,
          quantity: quantity,
          type: measurement.type,
          number: measurement.number,
        );
        break;
      case 'type':
        updatedMeasurement = PanelMeasurement(
          id: measurement.id,
          orderItemId: measurement.orderItemId,
          pieceWidth: measurement.pieceWidth,
          pieceHeight: measurement.pieceHeight,
          quantity: measurement.quantity,
          type: newValue.isEmpty ? '-' : newValue,
          number: measurement.number,
        );
        break;
      case 'number':
        updatedMeasurement = PanelMeasurement(
          id: measurement.id,
          orderItemId: measurement.orderItemId,
          pieceWidth: measurement.pieceWidth,
          pieceHeight: measurement.pieceHeight,
          quantity: measurement.quantity,
          type: measurement.type,
          number: newValue.isEmpty ? '-' : newValue,
        );
        break;
      default:
        return;
    }

    _updateMeasurement(index, updatedMeasurement);
  }
  // Update measurement in database and UI
  void _updateMeasurement(int index, PanelMeasurement newMeasurement) async {
    try {
      // Update in database if measurement has an ID
      if (newMeasurement.id != null) {
        await _databaseHelper.updatePanelMeasurement(
          newMeasurement.id!,
          newMeasurement,
        );
      }

      // Update in UI
      setState(() {
        _measurements[index] = newMeasurement;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث القطعة بنجاح'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 1),
        ),
      );
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث القطعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقطيع الألواح - ${widget.orderItem.itemName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد ما إذا كانت الشاشة صغيرة (موبايل) أم كبيرة (ويندوز/متصفح)
          bool isMobile = constraints.maxWidth < 800;

          if (isMobile) {
            // تصميم عمودي للموبايل
            return _buildMobileLayout();
          } else {
            // تصميم أفقي للشاشات الكبيرة
            return _buildDesktopLayout();
          }        },
      ),
      floatingActionButton: _measurements.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: _optimizeCutting,
              icon: const Icon(Icons.calculate),
              label: const Text('بدء تحسين التقطيع'),
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            )
          : null,
    );
  }

  Widget _buildMobileLayout() {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(
        scrollbars: true,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Input section only - no results section for mobile
            _buildInputSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left side - Input section
          Expanded(
            flex: 1,
            child: _buildInputSection(),
          ),

          const SizedBox(width: 24),

          // Right side - Results section
          Expanded(
            flex: 1,
            child: _buildResultsSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header - Compact
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.dashboard,
                    color: Theme.of(context).colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'مقاسات تقطيع الألواح',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Panel dimensions input - Compact
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildCompactInputField(
                    label: 'عرض اللوح (سم)',
                    controller: _panelWidthController,
                    hint: '280',
                    icon: Icons.width_full,
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _updatePanelDimensions(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: _buildCompactInputField(
                    label: 'طول اللوح (سم)',
                    controller: _panelHeightController,
                    hint: '140',
                    icon: Icons.height,
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _updatePanelDimensions(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'سماكة المنشار (مم)',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 11,
                        ),
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        height: 36,
                        child: TextField(
                          controller: TextEditingController(text: _cuttingKerf.toString()),
                          keyboardType: TextInputType.number,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            hintText: '3',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                            prefixIcon: Icon(Icons.content_cut, size: 16),
                          ),
                          onChanged: (value) {
                            try {
                              _cuttingKerf = double.parse(value);
                            } catch (e) {
                              _cuttingKerf = 3.0;
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Allow rotation checkbox
            Row(
              children: [
                Checkbox(
                  value: _allowRotation,
                  onChanged: (value) {
                    setState(() {
                      _allowRotation = value ?? true;
                    });
                  },
                ),
                const Text('السماح بتدوير القطع', style: TextStyle(fontSize: 12)),
              ],
            ),

            const SizedBox(height: 12),

            // Measurements input section - Compact
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إضافة قطعة جديدة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Row 1: النوع و الرقم
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'النوع (اختياري)',
                          controller: _typeController,
                          focusNode: _typeFocusNode,
                          hint: 'مثال: رف',
                          icon: Icons.category,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'الرقم (اختياري)',
                          controller: _numberController,
                          focusNode: _numberFocusNode,
                          hint: 'مثال: 1',
                          icon: Icons.tag,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 10),

                  // Row 2: أبعاد القطعة
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'عرض القطعة (سم)',
                          controller: _pieceWidthController,
                          focusNode: _pieceWidthFocusNode,
                          hint: 'العرض',
                          icon: Icons.width_full,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'طول القطعة (سم)',
                          controller: _pieceHeightController,
                          focusNode: _pieceHeightFocusNode,
                          hint: 'الطول',
                          icon: Icons.height,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInputField(
                          label: 'الكمية',
                          controller: _quantityController,
                          focusNode: _quantityFocusNode,
                          hint: '#',
                          icon: Icons.numbers,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _addMeasurement,
                        icon: const Icon(Icons.add, size: 16),
                        label: const Text('إضافة', style: TextStyle(fontSize: 12)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Measurements DataGrid
            if (_measurements.isNotEmpty) ...[
              const SizedBox(height: 8),

              Text(
                'القطع المضافة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
              const SizedBox(height: 8),

              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),                  borderRadius: BorderRadius.circular(6),
                ),
                child: ScrollConfiguration(
                  behavior: ScrollConfiguration.of(context).copyWith(
                    scrollbars: true,
                  ),                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minWidth: MediaQuery.of(context).size.width > 800
                            ? MediaQuery.of(context).size.width * 0.45
                            : MediaQuery.of(context).size.width * 0.9,
                      ),
                      child: DataTable(
                        columnSpacing: MediaQuery.of(context).size.width > 1200 ? 16 :
                                     MediaQuery.of(context).size.width > 800 ? 12 : 8,
                        dataRowMinHeight: MediaQuery.of(context).size.width > 1200 ? 56 :
                                        MediaQuery.of(context).size.width > 800 ? 48 : 42,
                        dataRowMaxHeight: MediaQuery.of(context).size.width > 1200 ? 64 :
                                        MediaQuery.of(context).size.width > 800 ? 56 : 48,
                        headingRowHeight: MediaQuery.of(context).size.width > 1200 ? 56 :
                                        MediaQuery.of(context).size.width > 800 ? 48 : 42,
                        headingRowColor: WidgetStateProperty.all(Colors.grey[100]),                        columns: [
                          DataColumn(
                            label: Text(
                              '#',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'النوع',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'الرقم',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'العرض',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'الطول',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'العدد',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'عمل',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MediaQuery.of(context).size.width > 1200 ? 16 :
                                         MediaQuery.of(context).size.width > 800 ? 14 : 12
                              ),
                            ),
                          ),
                        ],                      rows: List.generate(_measurements.length, (index) {
                        final measurement = _measurements[index];
                        final fontSize = MediaQuery.of(context).size.width > 1200 ? 16.0 :
                                       MediaQuery.of(context).size.width > 800 ? 14.0 : 12.0;
                        final cellPadding = MediaQuery.of(context).size.width > 1200 ? 12.0 :
                                          MediaQuery.of(context).size.width > 800 ? 8.0 : 4.0;
                        final iconSize = MediaQuery.of(context).size.width > 1200 ? 20.0 :
                                       MediaQuery.of(context).size.width > 800 ? 18.0 : 16.0;

                        return DataRow(
                          cells: [
                            DataCell(Text('${index + 1}', style: TextStyle(fontSize: fontSize))),
                            DataCell(
                              GestureDetector(
                                onTap: () => _editMeasurement(index, 'type'),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: cellPadding, horizontal: cellPadding),
                                  child: Text(
                                    measurement.type,
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.blue,
                                      fontSize: fontSize,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),
                            DataCell(
                              GestureDetector(
                                onTap: () => _editMeasurement(index, 'number'),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: cellPadding, horizontal: cellPadding),
                                  child: Text(
                                    measurement.number,
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.blue,
                                      fontSize: fontSize,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),                            DataCell(
                              GestureDetector(
                                onTap: () => _editMeasurement(index, 'pieceWidth'),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: cellPadding, horizontal: cellPadding),
                                  child: Text(
                                    '${measurement.pieceWidth}',
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.blue,
                                      fontSize: fontSize,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            DataCell(
                              GestureDetector(
                                onTap: () => _editMeasurement(index, 'pieceHeight'),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: cellPadding, horizontal: cellPadding),
                                  child: Text(
                                    '${measurement.pieceHeight}',
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.blue,
                                      fontSize: fontSize,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            DataCell(
                              GestureDetector(
                                onTap: () => _editMeasurement(index, 'quantity'),
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: cellPadding, horizontal: cellPadding),
                                  child: Text(
                                    '${measurement.quantity}',                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      color: Colors.blue,
                                      fontSize: fontSize,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            DataCell(
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [                                  IconButton(
                                    icon: Icon(Icons.edit, color: Colors.orange, size: iconSize),
                                    onPressed: () => _showEditDialog(index),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    tooltip: 'تعديل',
                                  ),
                                  const SizedBox(width: 4),
                                  IconButton(
                                    icon: Icon(Icons.delete, color: Colors.red, size: iconSize),
                                    onPressed: () => _deleteMeasurement(index),
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    tooltip: 'حذف',
                                  ),
                                ],                              ),
                            ),
                          ],
                        );
                      }),
                      ),
                    ),
                  ),
                ),
              ),
            ],            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    // Statistics
    final totalPieces = _measurements.fold<int>(0, (sum, measurement) => sum + measurement.quantity);
    final uniqueTypes = _measurements.map((m) => m.type).toSet().length;
    final totalArea = _measurements.fold<double>(0, (sum, measurement) =>
        sum + (measurement.pieceWidth * measurement.pieceHeight * measurement.quantity));

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header - Compact
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.bar_chart,
                    color: Theme.of(context).colorScheme.primary,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'ملخص القطع',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            if (_measurements.isEmpty)
              // رسالة عدم وجود قطع
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'لم يتم إضافة أي قطع بعد',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'أضف أبعاد القطع المطلوبة ثم اضغط "بدء تحسين التقطيع"',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              // إحصائيات القطع
              Column(
                children: [
                  // الصف الأول من الإحصائيات
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'إجمالي القطع',
                          '$totalPieces',
                          Icons.dashboard,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'أنواع مختلفة',
                          '$uniqueTypes',
                          Icons.category,
                          Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // الصف الثاني من الإحصائيات
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'أبعاد اللوح',
                          '${_panelWidth.toInt()}×${_panelHeight.toInt()}',
                          Icons.aspect_ratio,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'المساحة الكلية',
                          '${(totalArea / 10000).toStringAsFixed(2)} م²',
                          Icons.square_foot,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // رسالة تشجيعية
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle_outline,
                          color: Colors.green[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'جاهز للتحسين! اضغط "بدء تحسين التقطيع" لحساب التوزيع الأمثل',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCompactInputField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    FocusNode? focusNode,
    TextInputType keyboardType = TextInputType.text,
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 11,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 36,
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            keyboardType: keyboardType,
            style: const TextStyle(fontSize: 13),
            decoration: InputDecoration(
              hintText: hint,
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              prefixIcon: Icon(icon, size: 16),
            ),
            onChanged: onChanged,
            inputFormatters: keyboardType == TextInputType.number
                ? [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))]
                : null,
          ),
        ),
      ],
    );
  }
}

// Panel optimization result screen
class PanelOptimizationResultScreen extends StatefulWidget {
  final double panelWidth;
  final double panelHeight;
  final List<PanelMeasurement> measurements;
  final bool allowRotation;
  final double cuttingKerf;

  const PanelOptimizationResultScreen({
    super.key,
    required this.panelWidth,
    required this.panelHeight,
    required this.measurements,
    required this.allowRotation,
    required this.cuttingKerf,
  });

  @override
  State<PanelOptimizationResultScreen> createState() => _PanelOptimizationResultScreenState();
}

class _PanelOptimizationResultScreenState extends State<PanelOptimizationResultScreen> {
  List<OptimizedPanelResult> _optimizedPanels = [];
  double _wastePercentage = 0.0;
  bool _isOptimizing = false;

  @override
  void initState() {
    super.initState();
    _optimizePanels();
  }

  Future<void> _optimizePanels() async {
    setState(() {
      _isOptimizing = true;
    });

    // محاكاة وقت المعالجة
    await Future.delayed(const Duration(milliseconds: 500));

    // خوارزمية تحسين بسيطة
    final optimizer = SimplePanelOptimizer(
      panelWidth: widget.panelWidth,
      panelHeight: widget.panelHeight,
      allowRotation: widget.allowRotation,
      cuttingKerf: widget.cuttingKerf,
    );

    final result = optimizer.optimize(widget.measurements);

    setState(() {
      _optimizedPanels = result.panels;
      _wastePercentage = result.wastePercentage;
      _isOptimizing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نتائج التحسين'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isOptimizing
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري حساب التحسين...'),
                ],
              ),
            )
          : Column(
              children: [
                // Summary section
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'ملخص النتائج',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Column(
                            children: [
                              Text(
                                '${_optimizedPanels.length}',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                              const Text('عدد الألواح'),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                '${_wastePercentage.toStringAsFixed(1)}%',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange,
                                ),
                              ),
                              const Text('نسبة الهدر'),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Panels list
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _optimizedPanels.length,
                    itemBuilder: (context, index) {
                      final panel = _optimizedPanels[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ExpansionTile(
                          leading: CircleAvatar(
                            backgroundColor: Colors.blue,
                            child: Text('${index + 1}'),
                          ),
                          title: Text('لوح ${index + 1}'),
                          subtitle: Text(
                            'القطع: ${panel.pieces.length}, الكفاءة: ${panel.efficiency.toStringAsFixed(1)}%',
                          ),
                          children: panel.pieces.map((piece) {
                            return ListTile(
                              leading: const Icon(Icons.crop_square),
                              title: Text('${piece.type} - ${piece.number}'),
                              subtitle: Text(
                                'الأبعاد: ${piece.width} × ${piece.height} سم\n'
                                'الموضع: (${piece.x}, ${piece.y})',
                              ),
                              isThreeLine: true,
                            );
                          }).toList(),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }
}

// Simple panel optimizer
class SimplePanelOptimizer {
  final double panelWidth;
  final double panelHeight;
  final bool allowRotation;
  final double cuttingKerf;

  SimplePanelOptimizer({
    required this.panelWidth,
    required this.panelHeight,
    required this.allowRotation,
    required this.cuttingKerf,
  });

  OptimizationResult optimize(List<PanelMeasurement> measurements) {
    final List<OptimizedPanelResult> panels = [];
    final List<PieceToPlace> piecesToPlace = [];

    // Convert measurements to pieces
    for (final measurement in measurements) {
      for (int i = 0; i < measurement.quantity; i++) {
        piecesToPlace.add(PieceToPlace(
          width: measurement.pieceWidth,
          height: measurement.pieceHeight,
          type: measurement.type,
          number: measurement.number,
        ));
      }
    }

    // Simple first-fit algorithm
    while (piecesToPlace.isNotEmpty) {
      final panel = OptimizedPanelResult(
        width: panelWidth,
        height: panelHeight,
        pieces: [],
        efficiency: 0.0,
      );

      final placedPieces = <PlacedPiece>[];
      final piecesUsed = <int>[];

      for (int i = 0; i < piecesToPlace.length; i++) {
        final piece = piecesToPlace[i];
        final position = _findBestPosition(placedPieces, piece);

        if (position != null) {
          placedPieces.add(PlacedPiece(
            width: piece.width,
            height: piece.height,
            x: position.dx,
            y: position.dy,
            type: piece.type,
            number: piece.number,
          ));
          piecesUsed.add(i);
        }
      }

      // Remove used pieces
      for (int i = piecesUsed.length - 1; i >= 0; i--) {
        piecesToPlace.removeAt(piecesUsed[i]);
      }

      panel.pieces = placedPieces;
      panel.efficiency = _calculateEfficiency(placedPieces);
      panels.add(panel);

      // Prevent infinite loop
      if (placedPieces.isEmpty) break;
    }

    final totalArea = panels.length * panelWidth * panelHeight;
    final usedArea = panels.fold(0.0, (sum, panel) =>
        sum + panel.pieces.fold(0.0, (pieceSum, piece) =>
            pieceSum + piece.width * piece.height));
    final wastePercentage = totalArea > 0 ? ((totalArea - usedArea) / totalArea) * 100 : 0.0;

    return OptimizationResult(
      panels: panels,
      wastePercentage: wastePercentage,
    );
  }

  Offset? _findBestPosition(List<PlacedPiece> placedPieces, PieceToPlace piece) {
    // Try to place piece at position (0, 0) first
    if (_canPlaceAt(placedPieces, piece, 0, 0)) {
      return const Offset(0, 0);
    }

    // Try other positions based on existing pieces
    for (final placedPiece in placedPieces) {
      // Try to the right
      final rightX = placedPiece.x + placedPiece.width + cuttingKerf;
      if (_canPlaceAt(placedPieces, piece, rightX, placedPiece.y)) {
        return Offset(rightX, placedPiece.y);
      }

      // Try below
      final belowY = placedPiece.y + placedPiece.height + cuttingKerf;
      if (_canPlaceAt(placedPieces, piece, placedPiece.x, belowY)) {
        return Offset(placedPiece.x, belowY);
      }
    }

    return null;
  }

  bool _canPlaceAt(List<PlacedPiece> placedPieces, PieceToPlace piece, double x, double y) {
    // Check if piece fits within panel bounds
    if (x + piece.width > panelWidth || y + piece.height > panelHeight) {
      return false;
    }

    // Check for overlaps with existing pieces
    for (final placedPiece in placedPieces) {
      if (_rectsOverlap(
        x, y, piece.width, piece.height,
        placedPiece.x, placedPiece.y, placedPiece.width, placedPiece.height,
      )) {
        return false;
      }
    }

    return true;
  }

  bool _rectsOverlap(double x1, double y1, double w1, double h1,
                     double x2, double y2, double w2, double h2) {
    return !(x1 + w1 + cuttingKerf <= x2 ||
             x2 + w2 + cuttingKerf <= x1 ||
             y1 + h1 + cuttingKerf <= y2 ||
             y2 + h2 + cuttingKerf <= y1);
  }

  double _calculateEfficiency(List<PlacedPiece> pieces) {
    final usedArea = pieces.fold(0.0, (sum, piece) => sum + piece.width * piece.height);
    final totalArea = panelWidth * panelHeight;
    return totalArea > 0 ? (usedArea / totalArea) * 100 : 0.0;
  }
}

// Helper classes
class PieceToPlace {
  final double width;
  final double height;
  final String type;
  final String number;

  PieceToPlace({
    required this.width,
    required this.height,
    required this.type,
    required this.number,
  });
}

class PlacedPiece {
  final double width;
  final double height;
  final double x;
  final double y;
  final String type;
  final String number;

  PlacedPiece({
    required this.width,
    required this.height,
    required this.x,
    required this.y,
    required this.type,
    required this.number,
  });
}

class OptimizedPanelResult {
  final double width;
  final double height;
  List<PlacedPiece> pieces;
  double efficiency;

  OptimizedPanelResult({
    required this.width,
    required this.height,
    required this.pieces,
    required this.efficiency,
  });
}

class OptimizationResult {
  final List<OptimizedPanelResult> panels;
  final double wastePercentage;

  OptimizationResult({
    required this.panels,
    required this.wastePercentage,
  });
}
