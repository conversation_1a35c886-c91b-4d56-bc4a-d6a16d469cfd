import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// خدمة الحصول على معلومات الجهاز
class DeviceInfoService {
  static DeviceInfoService? _instance;
  static DeviceInfoService get instance => _instance ??= DeviceInfoService._();
  
  DeviceInfoService._();
  
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  /// معلومات الجهاز المخزنة محلياً
  static Map<String, dynamic>? _deviceData;
  
  /// الحصول على معلومات الجهاز المحفوظة
  static Map<String, dynamic>? get deviceData => _deviceData;
  
  /// الحصول على سيريال الجهاز
  static String? get deviceSerial {
    if (_deviceData == null) return null;

    // ترتيب الأولوية حسب المنصة
    if (_deviceData!['platform'] == 'Android') {
      // للأندرويد: استخدم androidId أولاً، ثم serial إذا كان متوفراً
      return _deviceData!['androidId'] ?? _deviceData!['serial'] ?? _deviceData!['fingerprint'];
    } else if (_deviceData!['platform'] == 'iOS') {
      // لـ iOS: استخدم identifierForVendor
      return _deviceData!['identifierForVendor'] ?? _deviceData!['serial'];
    } else if (_deviceData!['platform'] == 'Windows') {
      // لـ Windows: استخدم deviceId أو computerName
      return _deviceData!['deviceId'] ?? _deviceData!['computerName'];
    } else if (_deviceData!['platform'] == 'macOS') {
      // لـ macOS: استخدم systemGUID
      return _deviceData!['systemGUID'] ?? _deviceData!['serial'];
    } else if (_deviceData!['platform'] == 'Linux') {
      // لـ Linux: استخدم machineId
      return _deviceData!['machineId'] ?? _deviceData!['serial'];
    }

    // احتياطي عام
    final fallbackSerial = _deviceData!['serial'] ?? _deviceData!['serialNumber'] ?? _deviceData!['androidId'] ?? _deviceData!['deviceId'];

    // إذا لم نجد أي معرف، أنشئ معرف فريد من معلومات الجهاز
    if (fallbackSerial == null || fallbackSerial.toString().isEmpty) {
      return _generateFallbackSerial();
    }

    return fallbackSerial;
  }

  /// إنشاء معرف فريد احتياطي من معلومات الجهاز
  static String _generateFallbackSerial() {
    if (_deviceData == null) return 'UNKNOWN_DEVICE';

    // جمع معلومات الجهاز المتاحة لإنشاء معرف فريد
    final platform = _deviceData!['platform'] ?? 'Unknown';
    final model = _deviceData!['model'] ?? _deviceData!['computerName'] ?? 'Unknown';
    final manufacturer = _deviceData!['manufacturer'] ?? _deviceData!['brand'] ?? 'Unknown';
    final fingerprint = _deviceData!['fingerprint'] ?? _deviceData!['systemGUID'] ?? '';

    // إنشاء معرف مركب
    final combinedInfo = '$platform-$model-$manufacturer-$fingerprint';

    // إنشاء hash بسيط من المعلومات المجمعة
    int hash = combinedInfo.hashCode;
    if (hash < 0) hash = -hash; // تأكد من أن الرقم موجب

    return '${platform.toUpperCase()}_${hash.toString().padLeft(10, '0')}';
  }
  
  /// الحصول على اسم الجهاز
  static String? get deviceName {
    if (_deviceData == null) return null;
    return _deviceData!['name'] ?? _deviceData!['model'] ?? _deviceData!['computerName'];
  }
  
  /// الحصول على نوع الجهاز/المنصة
  static String get devicePlatform {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }
  
  /// جلب معلومات الجهاز من النظام
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      debugPrint('📱 بدء جلب معلومات الجهاز...');
      
      Map<String, dynamic> deviceData = {};
      
      if (kIsWeb) {
        // معلومات المتصفح
        final webBrowserInfo = await _deviceInfo.webBrowserInfo;
        deviceData = {
          'platform': 'Web',
          'browserName': webBrowserInfo.browserName.name,
          'userAgent': webBrowserInfo.userAgent,
          'language': webBrowserInfo.language,
          'platform_details': webBrowserInfo.platform,
          'serial': 'WEB-${webBrowserInfo.userAgent?.hashCode ?? 'UNKNOWN'}',
        };
      } else if (Platform.isAndroid) {
        // معلومات Android
        final androidInfo = await _deviceInfo.androidInfo;

        // محاولة الحصول على السيريال، مع التعامل مع القيود الأمنية
        String? serialNumber;
        try {
          serialNumber = androidInfo.serialNumber;
          // في الأندرويد 10+ قد يكون "unknown" أو فارغ
          if (serialNumber == "unknown" || serialNumber.isEmpty) {
            serialNumber = null;
          }
        } catch (e) {
          debugPrint('⚠️ لا يمكن الوصول لسيريال الأندرويد: $e');
          serialNumber = null;
        }

        deviceData = {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'product': androidInfo.product,
          'androidId': androidInfo.id, // هذا هو المعرف الأساسي للأندرويد
          'serial': serialNumber,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'fingerprint': androidInfo.fingerprint,
          'board': androidInfo.board,
          'bootloader': androidInfo.bootloader,
          'display': androidInfo.display,
          'hardware': androidInfo.hardware,
          'host': androidInfo.host,
          'tags': androidInfo.tags,
          'type': androidInfo.type,
        };
      } else if (Platform.isIOS) {
        // معلومات iOS
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData = {
          'platform': 'iOS',
          'name': iosInfo.name,
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'identifierForVendor': iosInfo.identifierForVendor,
          'serial': iosInfo.identifierForVendor, // iOS لا يوفر السيريال مباشرة
          'localizedModel': iosInfo.localizedModel,
          'utsname': iosInfo.utsname.machine,
        };
      } else if (Platform.isWindows) {
        // معلومات Windows
        final windowsInfo = await _deviceInfo.windowsInfo;
        deviceData = {
          'platform': 'Windows',
          'computerName': windowsInfo.computerName,
          'numberOfCores': windowsInfo.numberOfCores,
          'systemMemoryInMegabytes': windowsInfo.systemMemoryInMegabytes,
          'userName': windowsInfo.userName,
          'majorVersion': windowsInfo.majorVersion,
          'minorVersion': windowsInfo.minorVersion,
          'buildNumber': windowsInfo.buildNumber,
          'platformId': windowsInfo.platformId,
          'csdVersion': windowsInfo.csdVersion,
          'servicePackMajor': windowsInfo.servicePackMajor,
          'servicePackMinor': windowsInfo.servicePackMinor,
          'suitMask': windowsInfo.suitMask,
          'productType': windowsInfo.productType,
          'reserved': windowsInfo.reserved,
          'buildLab': windowsInfo.buildLab,
          'buildLabEx': windowsInfo.buildLabEx,
          'digitalProductId': windowsInfo.digitalProductId,
          'displayVersion': windowsInfo.displayVersion,
          'editionId': windowsInfo.editionId,
          'installDate': windowsInfo.installDate,
          'productId': windowsInfo.productId,
          'productName': windowsInfo.productName,
          'registeredOwner': windowsInfo.registeredOwner,
          'releaseId': windowsInfo.releaseId,
          'deviceId': windowsInfo.deviceId,
          'serial': windowsInfo.deviceId, // استخدام deviceId كسيريال
        };
      } else if (Platform.isMacOS) {
        // معلومات macOS
        final macOsInfo = await _deviceInfo.macOsInfo;
        deviceData = {
          'platform': 'macOS',
          'computerName': macOsInfo.computerName,
          'hostName': macOsInfo.hostName,
          'arch': macOsInfo.arch,
          'model': macOsInfo.model,
          'kernelVersion': macOsInfo.kernelVersion,
          'majorVersion': macOsInfo.majorVersion,
          'minorVersion': macOsInfo.minorVersion,
          'patchVersion': macOsInfo.patchVersion,
          'osRelease': macOsInfo.osRelease,
          'activeCPUs': macOsInfo.activeCPUs,
          'memorySize': macOsInfo.memorySize,
          'cpuFrequency': macOsInfo.cpuFrequency,
          'systemGUID': macOsInfo.systemGUID,
          'serial': macOsInfo.systemGUID, // استخدام systemGUID كسيريال
        };
      } else if (Platform.isLinux) {
        // معلومات Linux
        final linuxInfo = await _deviceInfo.linuxInfo;
        deviceData = {
          'platform': 'Linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'idLike': linuxInfo.idLike,
          'versionCodename': linuxInfo.versionCodename,
          'versionId': linuxInfo.versionId,
          'prettyName': linuxInfo.prettyName,
          'buildId': linuxInfo.buildId,
          'variant': linuxInfo.variant,
          'variantId': linuxInfo.variantId,
          'machineId': linuxInfo.machineId,
          'serial': linuxInfo.machineId, // استخدام machineId كسيريال
        };
      }
      
      // حفظ البيانات محلياً
      _deviceData = deviceData;
      
      debugPrint('✅ تم جلب معلومات الجهاز بنجاح');
      debugPrint('📱 المنصة: ${deviceData['platform']}');
      debugPrint('🔢 السيريال: ${deviceSerial ?? 'غير متوفر'}');
      debugPrint('📝 اسم الجهاز: ${deviceName ?? 'غير متوفر'}');
      
      return {
        'success': true,
        'data': deviceData,
        'serial': deviceSerial,
        'name': deviceName,
        'platform': deviceData['platform'],
      };
      
    } catch (e) {
      debugPrint('❌ خطأ في جلب معلومات الجهاز: $e');
      debugPrint('🔍 تفاصيل الخطأ: ${e.runtimeType}');
      return {
        'success': false,
        'error': 'خطأ في جلب معلومات الجهاز: $e',
        'data': null,
        'serial': null,
        'name': null,
        'platform': devicePlatform,
      };
    }
  }
  
  /// طباعة معلومات الجهاز بشكل مفصل
  static void printDeviceInfo() {
    if (_deviceData == null) {
      debugPrint('⚠️ لم يتم جلب معلومات الجهاز بعد');
      return;
    }
    
    debugPrint('');
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('📱 معلومات الجهاز - Device Information');
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('🌐 المنصة (Platform): ${_deviceData!['platform']}');
    debugPrint('🔢 السيريال (Serial): ${deviceSerial ?? 'غير متوفر'}');
    debugPrint('📝 اسم الجهاز (Device Name): ${deviceName ?? 'غير متوفر'}');
    debugPrint('');
    debugPrint('📋 تفاصيل إضافية:');
    _deviceData!.forEach((key, value) {
      if (key != 'platform') {
        debugPrint('   $key: $value');
      }
    });
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('');
  }
  
  /// إعادة تعيين البيانات المحفوظة
  static void resetDeviceData() {
    _deviceData = null;
  }

  /// الحصول على معلومات السيريال مع التفاصيل
  static Map<String, dynamic> getSerialInfo() {
    if (_deviceData == null) {
      return {
        'serial': null,
        'source': 'not_loaded',
        'platform': 'unknown',
        'available_ids': [],
        'error': 'Device data not loaded',
      };
    }

    final platform = _deviceData!['platform'];
    List<String> availableIds = [];
    String? primarySerial;
    String source = 'unknown';

    if (platform == 'Android') {
      if (_deviceData!['androidId'] != null && _deviceData!['androidId'].toString().isNotEmpty) {
        availableIds.add('androidId: ${_deviceData!['androidId']}');
        primarySerial ??= _deviceData!['androidId'];
        source = 'androidId';
      }
      if (_deviceData!['serial'] != null && _deviceData!['serial'].toString().isNotEmpty) {
        availableIds.add('serial: ${_deviceData!['serial']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['serial'];
          source = 'serial';
        }
      }
      if (_deviceData!['fingerprint'] != null && _deviceData!['fingerprint'].toString().isNotEmpty) {
        availableIds.add('fingerprint: ${_deviceData!['fingerprint']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['fingerprint'];
          source = 'fingerprint';
        }
      }
    } else if (platform == 'iOS') {
      if (_deviceData!['identifierForVendor'] != null) {
        availableIds.add('identifierForVendor: ${_deviceData!['identifierForVendor']}');
        primarySerial = _deviceData!['identifierForVendor'];
        source = 'identifierForVendor';
      }
    } else if (platform == 'Windows') {
      if (_deviceData!['deviceId'] != null) {
        availableIds.add('deviceId: ${_deviceData!['deviceId']}');
        primarySerial = _deviceData!['deviceId'];
        source = 'deviceId';
      }
      if (_deviceData!['computerName'] != null) {
        availableIds.add('computerName: ${_deviceData!['computerName']}');
        primarySerial ??= _deviceData!['computerName'];
        if (source == 'unknown') source = 'computerName';
      }
    }

    // إذا لم نجد معرف، استخدم المعرف الاحتياطي
    if (primarySerial == null || primarySerial.toString().isEmpty) {
      primarySerial = _generateFallbackSerial();
      source = 'generated_fallback';
    }

    return {
      'serial': primarySerial,
      'source': source,
      'platform': platform,
      'available_ids': availableIds,
      'error': null,
    };
  }

  /// طباعة معلومات السيريال للتشخيص
  static void printSerialDiagnostics() {
    debugPrint('');
    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');
    debugPrint('🔑 تشخيص سيريال الجهاز - Serial Diagnostics');
    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');

    final serialInfo = getSerialInfo();

    debugPrint('📱 المنصة: ${serialInfo['platform']}');
    debugPrint('🔑 السيريال المستخدم: ${serialInfo['serial'] ?? "غير متوفر"}');
    debugPrint('📍 مصدر السيريال: ${serialInfo['source']}');

    if (serialInfo['available_ids'].isNotEmpty) {
      debugPrint('📋 المعرفات المتاحة:');
      for (String id in serialInfo['available_ids']) {
        debugPrint('   - $id');
      }
    } else {
      debugPrint('⚠️ لا توجد معرفات متاحة');
    }

    if (serialInfo['error'] != null) {
      debugPrint('❌ خطأ: ${serialInfo['error']}');
    }

    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');
    debugPrint('');
  }
}
