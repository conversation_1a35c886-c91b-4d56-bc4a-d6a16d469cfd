import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

enum ProjectStatus { 
  pending('قيد الانتظار', Colors.orange),
  inProgress('جاري التنفيذ', Colors.blue),
  completed('مكتمل', Colors.green),
  cancelled('ملغي', Colors.red);

  const ProjectStatus(this.label, this.color);
  final String label;
  final Color color;
}

enum ProjectType {
  kitchen('مطبخ', Icons.kitchen),
  aluminum('ألومنيوم', Icons.window),
  cutting('تقطيع', Icons.content_cut),
  maintenance('صيانة', Icons.handyman);

  const ProjectType(this.label, this.icon);
  final String label;
  final IconData icon;
}

class Project {
  final String id;
  final String name;
  final String clientName;
  final String clientPhone;
  final String description;
  final ProjectType type;
  final ProjectStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final double estimatedCost;
  final double actualCost;
  final String notes;
  final List<String> attachments;

  Project({
    required this.id,
    required this.name,
    required this.clientName,
    required this.clientPhone,
    required this.description,
    required this.type,
    required this.status,
    required this.startDate,
    this.endDate,
    required this.estimatedCost,
    this.actualCost = 0.0,
    this.notes = '',
    this.attachments = const [],
  });

  Project copyWith({
    String? id,
    String? name,
    String? clientName,
    String? clientPhone,
    String? description,
    ProjectType? type,
    ProjectStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    double? estimatedCost,
    double? actualCost,
    String? notes,
    List<String>? attachments,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      actualCost: actualCost ?? this.actualCost,
      notes: notes ?? this.notes,
      attachments: attachments ?? this.attachments,
    );
  }

  double get progress {
    if (status == ProjectStatus.completed) return 1.0;
    if (status == ProjectStatus.cancelled) return 0.0;
    if (status == ProjectStatus.pending) return 0.0;
    
    final now = DateTime.now();
    final totalDuration = (endDate ?? now).difference(startDate).inDays;
    final passedDuration = now.difference(startDate).inDays;
    
    if (totalDuration <= 0) return 0.0;
    return (passedDuration / totalDuration).clamp(0.0, 1.0);
  }
}

class ProjectsService extends StatefulWidget {
  const ProjectsService({super.key});

  @override
  State<ProjectsService> createState() => _ProjectsServiceState();
}

class _ProjectsServiceState extends State<ProjectsService>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;  List<Project> _projects = [];
  List<Project> _filteredProjects = [];
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSampleData();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
        _filterProjects();
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadSampleData() {
    _projects = [
      Project(
        id: '1',
        name: 'مطبخ فيلا السلام',
        clientName: 'أحمد محمد',
        clientPhone: '**********',
        description: 'تصميم وتنفيذ مطبخ مودرن للفيلا',
        type: ProjectType.kitchen,
        status: ProjectStatus.inProgress,
        startDate: DateTime.now().subtract(const Duration(days: 5)),
        endDate: DateTime.now().add(const Duration(days: 10)),
        estimatedCost: 25000,
        actualCost: 12000,
        notes: 'تم استلام الخامات',
      ),
      Project(
        id: '2',
        name: 'شبابيك مكتب الشركة',
        clientName: 'سارة أحمد',
        clientPhone: '0507654321',
        description: 'تركيب شبابيك ألومنيوم حراري',
        type: ProjectType.aluminum,
        status: ProjectStatus.pending,
        startDate: DateTime.now().add(const Duration(days: 3)),
        endDate: DateTime.now().add(const Duration(days: 20)),
        estimatedCost: 15000,
        notes: 'في انتظار موافقة العميل',
      ),
      Project(
        id: '3',
        name: 'تقطيع خشب للمشروع الجديد',
        clientName: 'محمد علي',
        clientPhone: '0556789012',
        description: 'تقطيع ألواح خشب MDF حسب المقاسات',
        type: ProjectType.cutting,
        status: ProjectStatus.completed,
        startDate: DateTime.now().subtract(const Duration(days: 10)),
        endDate: DateTime.now().subtract(const Duration(days: 2)),
        estimatedCost: 3000,
        actualCost: 2800,
        notes: 'تم التسليم بنجاح',
      ),
      Project(
        id: '4',
        name: 'صيانة مطبخ قديم',
        clientName: 'فاطمة خالد',
        clientPhone: '0503456789',
        description: 'صيانة وإصلاح أبواب المطبخ',
        type: ProjectType.maintenance,
        status: ProjectStatus.cancelled,
        startDate: DateTime.now().subtract(const Duration(days: 15)),
        estimatedCost: 1500,
        notes: 'ألغى العميل الطلب',
      ),
    ];
    _filterProjects();
  }

  void _filterProjects() {
    List<Project> filtered = _projects;
    
    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((project) {
        return project.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               project.clientName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               project.description.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
    
    setState(() {
      _filteredProjects = filtered;
    });
  }

  List<Project> _getProjectsByStatus(ProjectStatus status) {
    return _filteredProjects.where((project) => project.status == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المشاريع'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewProject,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showProjectStatistics,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: ProjectStatus.values.map((status) {
            final count = _getProjectsByStatus(status).length;
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(status.label),
                  if (count > 0) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        count.toString(),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن مشروع...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[200],
              ),
            ),
          ),
          
          // Statistics cards
          _buildQuickStats(),
          
          // Projects list
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: ProjectStatus.values.map((status) {
                final projects = _getProjectsByStatus(status);
                return _buildProjectsList(projects, status);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final totalProjects = _projects.length;
    final activeProjects = _projects.where((p) => p.status == ProjectStatus.inProgress).length;
    final completedProjects = _projects.where((p) => p.status == ProjectStatus.completed).length;
    final totalRevenue = _projects
        .where((p) => p.status == ProjectStatus.completed)
        .fold(0.0, (sum, p) => sum + p.actualCost);

    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المشاريع',
              totalProjects.toString(),
              Icons.assignment,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'قيد التنفيذ',
              activeProjects.toString(),
              Icons.hourglass_empty,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'مكتملة',
              completedProjects.toString(),
              Icons.check_circle,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'الإيرادات',
              '${(totalRevenue / 1000).toStringAsFixed(0)}ك',
              Icons.attach_money,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 10),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectsList(List<Project> projects, ProjectStatus status) {
    if (projects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مشاريع ${status.label}',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        return _buildProjectCard(project);
      },
    );
  }

  Widget _buildProjectCard(Project project) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showProjectDetails(project),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: project.type.icon == Icons.kitchen
                          ? Colors.orange.withValues(alpha: 0.1)
                          : project.type.icon == Icons.window
                              ? Colors.blue.withValues(alpha: 0.1)
                              : project.type.icon == Icons.content_cut
                                  ? Colors.green.withValues(alpha: 0.1)
                                  : Colors.purple.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      project.type.icon,
                      color: project.type.icon == Icons.kitchen
                          ? Colors.orange
                          : project.type.icon == Icons.window
                              ? Colors.blue
                              : project.type.icon == Icons.content_cut
                                  ? Colors.green
                                  : Colors.purple,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          project.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          project.clientName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: project.status.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: project.status.color.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      project.status.label,
                      style: TextStyle(
                        color: project.status.color,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Text(
                project.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('dd/MM/yyyy').format(project.startDate),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.attach_money, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${project.estimatedCost.toStringAsFixed(0)} ريال',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
              
              if (project.status == ProjectStatus.inProgress) ...[
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: project.progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(project.status.color),
                ),
                const SizedBox(height: 4),
                Text(
                  'التقدم: ${(project.progress * 100).toInt()}%',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showProjectDetails(Project project) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: project.status.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        project.type.icon,
                        color: project.status.color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            project.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            project.type.label,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                _buildDetailRow('العميل', project.clientName, Icons.person),
                _buildDetailRow('الهاتف', project.clientPhone, Icons.phone),
                _buildDetailRow('الوصف', project.description, Icons.description),
                _buildDetailRow('تاريخ البداية', DateFormat('dd/MM/yyyy').format(project.startDate), Icons.calendar_today),
                if (project.endDate != null)
                  _buildDetailRow('تاريخ النهاية', DateFormat('dd/MM/yyyy').format(project.endDate!), Icons.event),
                _buildDetailRow('التكلفة المقدرة', '${project.estimatedCost.toStringAsFixed(0)} ريال', Icons.attach_money),
                if (project.actualCost > 0)
                  _buildDetailRow('التكلفة الفعلية', '${project.actualCost.toStringAsFixed(0)} ريال', Icons.money),
                _buildDetailRow('الحالة', project.status.label, Icons.flag),
                if (project.notes.isNotEmpty)
                  _buildDetailRow('ملاحظات', project.notes, Icons.note),
                
                const SizedBox(height: 20),
                
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _editProject(project);
                        },
                        icon: const Icon(Icons.edit),
                        label: const Text('تعديل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _deleteProject(project);
                        },
                        icon: const Icon(Icons.delete, color: Colors.red),
                        label: const Text('حذف', style: TextStyle(color: Colors.red)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addNewProject() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مشروع جديد'),
        content: const Text('سيتم تطوير نموذج إضافة المشاريع قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _editProject(Project project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل المشروع'),
        content: const Text('سيتم تطوير نموذج تعديل المشاريع قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _deleteProject(Project project) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المشروع'),
        content: Text('هل أنت متأكد من حذف مشروع "${project.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _projects.removeWhere((p) => p.id == project.id);
                _filterProjects();
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المشروع بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showProjectStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات المشاريع'),
        content: const Text('سيتم تطوير شاشة الإحصائيات قريباً.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}