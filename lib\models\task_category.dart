import 'package:flutter/material.dart';

class TaskCategory {
  final int? id;
  final String name;
  final String description;
  final Color color;
  final IconData icon;
  final DateTime createdAt;
  final DateTime updatedAt;

  TaskCategory({
    this.id,
    required this.name,
    this.description = '',
    required this.color,
    required this.icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  TaskCategory copyWith({
    int? id,
    String? name,
    String? description,
    Color? color,
    IconData? icon,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.toARGB32(),
      'icon': icon.codePoint,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory TaskCategory.fromMap(Map<String, dynamic> map) {
    return TaskCategory(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      color: Color(map['color']?.toInt() ?? Colors.blue.toARGB32()),
      icon: IconData(map['icon']?.toInt() ?? Icons.category.codePoint, fontFamily: 'MaterialIcons'),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  @override
  String toString() {
    return 'TaskCategory{id: $id, name: $name, description: $description, color: $color, icon: $icon, createdAt: $createdAt, updatedAt: $updatedAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskCategory &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.color == color &&
        other.icon == icon &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        description.hashCode ^
        color.hashCode ^
        icon.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}

// Predefined categories for easy setup
class DefaultTaskCategories {
  static List<TaskCategory> get defaultCategories => [
    TaskCategory(
      name: 'Work',
      description: 'Work-related tasks',
      color: Colors.blue,
      icon: Icons.work,
    ),
    TaskCategory(
      name: 'Personal',
      description: 'Personal tasks and activities',
      color: Colors.green,
      icon: Icons.person,
    ),
    TaskCategory(
      name: 'Shopping',
      description: 'Shopping lists and purchases',
      color: Colors.orange,
      icon: Icons.shopping_cart,
    ),
    TaskCategory(
      name: 'Health',
      description: 'Health and fitness related tasks',
      color: Colors.red,
      icon: Icons.favorite,
    ),
    TaskCategory(
      name: 'Education',
      description: 'Learning and educational tasks',
      color: Colors.purple,
      icon: Icons.school,
    ),
    TaskCategory(
      name: 'Home',
      description: 'Home maintenance and chores',
      color: Colors.brown,
      icon: Icons.home,
    ),
    TaskCategory(
      name: 'Finance',
      description: 'Financial tasks and planning',
      color: Colors.teal,
      icon: Icons.account_balance_wallet,
    ),
    TaskCategory(
      name: 'Travel',
      description: 'Travel planning and activities',
      color: Colors.indigo,
      icon: Icons.flight,
    ),
  ];

  static List<TaskCategory> get defaultCategoriesAr => [
    TaskCategory(
      name: 'العمل',
      description: 'المهام المتعلقة بالعمل',
      color: Colors.blue,
      icon: Icons.work,
    ),
    TaskCategory(
      name: 'شخصي',
      description: 'المهام والأنشطة الشخصية',
      color: Colors.green,
      icon: Icons.person,
    ),
    TaskCategory(
      name: 'التسوق',
      description: 'قوائم التسوق والمشتريات',
      color: Colors.orange,
      icon: Icons.shopping_cart,
    ),
    TaskCategory(
      name: 'الصحة',
      description: 'المهام المتعلقة بالصحة واللياقة',
      color: Colors.red,
      icon: Icons.favorite,
    ),
    TaskCategory(
      name: 'التعليم',
      description: 'مهام التعلم والتعليم',
      color: Colors.purple,
      icon: Icons.school,
    ),
    TaskCategory(
      name: 'المنزل',
      description: 'صيانة المنزل والأعمال المنزلية',
      color: Colors.brown,
      icon: Icons.home,
    ),
    TaskCategory(
      name: 'المالية',
      description: 'المهام المالية والتخطيط',
      color: Colors.teal,
      icon: Icons.account_balance_wallet,
    ),
    TaskCategory(
      name: 'السفر',
      description: 'تخطيط السفر والأنشطة',
      color: Colors.indigo,
      icon: Icons.flight,
    ),
  ];
}

// Available colors for categories
class CategoryColors {
  static const List<Color> availableColors = [
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
  ];
}

// Available icons for categories
class CategoryIcons {
  static const List<IconData> availableIcons = [
    Icons.work,
    Icons.person,
    Icons.home,
    Icons.school,
    Icons.shopping_cart,
    Icons.favorite,
    Icons.fitness_center,
    Icons.restaurant,
    Icons.car_rental,
    Icons.flight,
    Icons.hotel,
    Icons.local_hospital,
    Icons.pets,
    Icons.sports_soccer,
    Icons.music_note,
    Icons.movie,
    Icons.book,
    Icons.computer,
    Icons.phone,
    Icons.email,
    Icons.calendar_today,
    Icons.alarm,
    Icons.star,
    Icons.lightbulb,
    Icons.build,
    Icons.cleaning_services,
    Icons.kitchen,
    Icons.local_laundry_service,
    Icons.account_balance_wallet,
    Icons.savings,
    Icons.payment,
    Icons.receipt,
    Icons.category,
    Icons.folder,
    Icons.assignment,
    Icons.task_alt,
    Icons.check_circle,
    Icons.priority_high,
    Icons.schedule,
    Icons.today,
  ];
}
