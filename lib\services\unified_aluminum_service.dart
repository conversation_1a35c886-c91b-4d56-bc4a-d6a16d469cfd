import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../models/aluminum_profile.dart';
import '../models/aluminum_quotation.dart';
import '../models/profile_series.dart';
import 'unified_database_service.dart';

/// خدمة الألومنيوم الموحدة - تستخدم قاعدة البيانات الموحدة بدلاً من الخدمات المنفصلة
class UnifiedAluminumService {
  static final UnifiedAluminumService _instance = UnifiedAluminumService._internal();
  factory UnifiedAluminumService() => _instance;
  UnifiedAluminumService._internal();

  final UnifiedDatabaseService _unifiedDb = UnifiedDatabaseService();

  /// الحصول على قاعدة البيانات
  Future<Database> get database async => await _unifiedDb.database;

  // ==================== مجموعات القطاعات ====================

  /// إدراج مجموعة قطاعات جديدة
  Future<int> insertProfileSeries(ProfileSeries series) async {
    final db = await database;
    final seriesMap = series.toMap();
    seriesMap['created_at'] = DateTime.now().millisecondsSinceEpoch;
    seriesMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('profile_series', seriesMap);
  }

  /// الحصول على جميع مجموعات القطاعات
  Future<List<Map<String, dynamic>>> getAllProfileSeries() async {
    return await _unifiedDb.getAluminumProfileSeries();
  }

  /// الحصول على جميع السلاسل (اسم بديل)
  Future<List<Map<String, dynamic>>> getAllSeries() async {
    return await getAllProfileSeries();
  }

  /// إدراج سلسلة جديدة (اسم بديل)
  Future<int> insertSeries(Map<String, dynamic> series) async {
    final db = await database;
    series['created_at'] = DateTime.now().millisecondsSinceEpoch;
    series['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.insert('profile_series', series);
  }

  /// الحصول على مجموعة قطاعات بالمعرف
  Future<ProfileSeries?> getProfileSeriesById(int id) async {
    final db = await database;
    final result = await db.query(
      'profile_series',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? ProfileSeries.fromMap(result.first) : null;
  }

  /// تحديث مجموعة قطاعات
  Future<int> updateProfileSeries(ProfileSeries series) async {
    final db = await database;
    final seriesMap = series.toMap();
    seriesMap['updated_at'] = DateTime.now().millisecondsSinceEpoch;
    return await db.update(
      'profile_series',
      seriesMap,
      where: 'id = ?',
      whereArgs: [series.id],
    );
  }

  /// حذف مجموعة قطاعات (soft delete)
  Future<int> deleteProfileSeries(int id) async {
    final db = await database;
    return await db.update(
      'profile_series',
      {
        'is_active': 0,
        'updated_at': DateTime.now().millisecondsSinceEpoch,
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// توليد كود مجموعة قطاعات جديد
  Future<String> generateSeriesCode(ProfileType type) async {
    final typePrefix = type.key.substring(0, 1).toUpperCase();

    final db = await database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM profile_series
      WHERE code LIKE ?
    ''', ['$typePrefix%']);

    final count = result.first['count'] as int;
    final sequence = (count + 1).toString().padLeft(3, '0');

    return '$typePrefix$sequence';
  }

  // ==================== قطاعات الألومنيوم ====================

  /// إدراج قطاع ألومنيوم جديد
  Future<int> insertAluminumProfile(AluminumProfile profile) async {
    return await _unifiedDb.insertAluminumProfile(profile.toMap());
  }

  /// الحصول على جميع قطاعات الألومنيوم
  Future<List<Map<String, dynamic>>> getAllProfiles() async {
    return await _unifiedDb.getAllAluminumProfiles();
  }

  /// الحصول على قطاع ألومنيوم بالمعرف
  Future<AluminumProfile?> getProfileById(int id) async {
    final db = await database;
    final result = await db.query(
      'aluminum_profiles',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? AluminumProfile.fromMap(result.first) : null;
  }

  /// تحديث قطاع ألومنيوم
  Future<int> updateProfile(dynamic profile) async {
    final map = profile is Map ? profile : profile.toMap();
    final id = profile is Map ? profile['id'] : profile.id;
    return await _unifiedDb.updateAluminumProfile(id!, map);
  }

  /// حذف قطاع ألومنيوم
  Future<int> deleteProfile(int id) async {
    return await _unifiedDb.deleteAluminumProfile(id);
  }

  /// البحث في قطاعات الألومنيوم
  Future<List<AluminumProfile>> searchProfiles(String query) async {
    final db = await database;
    final result = await db.query(
      'aluminum_profiles',
      where: 'name LIKE ? OR code LIKE ? OR description LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );
    return result.map((profile) => AluminumProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب النوع
  Future<List<AluminumProfile>> getProfilesByType(String type) async {
    final db = await database;
    final result = await db.query(
      'aluminum_profiles',
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'name ASC',
    );
    return result.map((profile) => AluminumProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب الفئة
  Future<List<AluminumProfile>> getProfilesByCategory(String category) async {
    final db = await database;
    final result = await db.query(
      'aluminum_profiles',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'name ASC',
    );
    return result.map((profile) => AluminumProfile.fromMap(profile)).toList();
  }

  /// الحصول على قطاعات حسب المجموعة
  Future<List<Map<String, dynamic>>> getProfilesBySeries(int seriesId) async {
    return await _unifiedDb.getAluminumProfilesBySeries('$seriesId');
  }

  // ==================== عروض أسعار الألومنيوم ====================

  /// إدراج عرض سعر ألومنيوم جديد
  Future<int> insertQuotation(dynamic quotation) async {
    final map = quotation is Map ? quotation : quotation.toMap();
    return await _unifiedDb.insertAluminumQuotation(map);
  }

  /// الحصول على جميع عروض أسعار الألومنيوم
  Future<List<Map<String, dynamic>>> getAllQuotations() async {
    return await _unifiedDb.getAllAluminumQuotations();
  }

  /// الحصول على عرض سعر بالمعرف
  Future<AluminumQuotation?> getQuotationById(int id) async {
    final db = await database;
    final result = await db.query(
      'aluminum_quotations',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? AluminumQuotation.fromMap(result.first) : null;
  }

  /// تحديث عرض سعر
  Future<int> updateQuotation(dynamic quotation) async {
    final map = quotation is Map ? quotation : quotation.toMap();
    final id = quotation is Map ? quotation['id'] : quotation.id;
    return await _unifiedDb.updateAluminumQuotation(id!, map);
  }

  /// حذف عرض سعر
  Future<int> deleteQuotation(int id) async {
    return await _unifiedDb.deleteAluminumQuotation(id);
  }

  // ==================== عناصر عروض الأسعار ====================

  /// إدراج عنصر عرض سعر جديد
  Future<int> insertQuotationItem(dynamic item) async {
    final map = item is Map ? item : item.toMap();
    return await _unifiedDb.insertAluminumQuotationItem(map);
  }

  /// الحصول على عناصر عرض سعر
  Future<List<Map<String, dynamic>>> getQuotationItems(int quotationId) async {
    return await _unifiedDb.getAluminumQuotationItems(quotationId);
  }

  /// تحديث عنصر عرض سعر
  Future<int> updateQuotationItem(dynamic item) async {
    final map = item is Map ? item : item.toMap();
    final id = item is Map ? item['id'] : item.id;
    return await _unifiedDb.updateAluminumQuotationItem(id!, map);
  }

  /// حذف عنصر عرض سعر
  Future<int> deleteQuotationItem(int id) async {
    return await _unifiedDb.deleteAluminumQuotationItem(id);
  }

  // ==================== التصاميم ====================

  /// إدراج تصميم مفصلي جديد
  Future<int> insertHingeDesign(Map<String, dynamic> design) async {
    return await _unifiedDb.insertHingeDesign(design);
  }

  /// إدراج تصميم سحاب جديد
  Future<int> insertSlidingDesign(Map<String, dynamic> design) async {
    return await _unifiedDb.insertSlidingDesign(design);
  }

  /// الحصول على تصاميم المفصلي
  Future<List<Map<String, dynamic>>> getHingeDesigns() async {
    final db = await database;
    return await db.query(
      'hinge_designs',
      orderBy: 'created_at DESC',
    );
  }

  /// الحصول على تصاميم السحاب
  Future<List<Map<String, dynamic>>> getSlidingDesigns() async {
    final db = await database;
    return await db.query(
      'sliding_designs',
      orderBy: 'created_at DESC',
    );
  }

  // ==================== الإحصائيات ====================

  /// الحصول على إحصائيات الألومنيوم
  Future<Map<String, dynamic>> getAluminumStatistics() async {
    final db = await database;
    
    // عدد القطاعات
    final profilesResult = await db.rawQuery('SELECT COUNT(*) as count FROM aluminum_profiles');
    final profilesCount = profilesResult.first['count'] as int;
    
    // عدد عروض الأسعار
    final quotationsResult = await db.rawQuery('SELECT COUNT(*) as count FROM aluminum_quotations');
    final quotationsCount = quotationsResult.first['count'] as int;
    
    // عدد التصاميم
    final hingeDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM hinge_designs');
    final hingeDesignsCount = hingeDesignsResult.first['count'] as int;
    
    final slidingDesignsResult = await db.rawQuery('SELECT COUNT(*) as count FROM sliding_designs');
    final slidingDesignsCount = slidingDesignsResult.first['count'] as int;
    
    return {
      'profiles_count': profilesCount,
      'quotations_count': quotationsCount,
      'hinge_designs_count': hingeDesignsCount,
      'sliding_designs_count': slidingDesignsCount,
      'total_designs': hingeDesignsCount + slidingDesignsCount,
    };
  }

  /// حذف مجموعة قطاعات مع قطاعاتها
  Future<void> deleteSeriesWithProfiles(int seriesId) async {
    final db = await database;
    await db.transaction((txn) async {
      // حذف القطاعات المرتبطة بالمجموعة
      await txn.delete(
        'aluminum_profiles',
        where: 'series_id = ?',
        whereArgs: [seriesId],
      );
      
      // حذف المجموعة نفسها
      await txn.delete(
        'profile_series',
        where: 'id = ?',
        whereArgs: [seriesId],
      );
    });
  }

  /// البحث في عروض الأسعار
  Future<List<AluminumQuotation>> searchQuotations(String query) async {
    final db = await database;
    final result = await db.query(
      'aluminum_quotations',
      where: 'quotation_number LIKE ? OR client_name LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'quotation_date DESC',
    );
    return result.map((quotation) => AluminumQuotation.fromMap(quotation)).toList();
  }

  /// توليد رقم عرض سعر جديد
  Future<String> generateQuotationNumber() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM aluminum_quotations');
    final count = result.first['count'] as int;
    return 'ALQ${(count + 1).toString().padLeft(4, '0')}';
  }



  // ==================== دوال إضافية مطلوبة للإعدادات ====================

  /// إدراج بيانات تجريبية
  Future<void> insertSampleData() async {
    // تم نقل البيانات إلى قاعدة البيانات الموحدة
    // يمكن إضافة بيانات تجريبية هنا إذا لزم الأمر
  }

  /// إعادة تعيين قاعدة البيانات
  Future<void> resetDatabase() async {
    // تم نقل البيانات إلى قاعدة البيانات الموحدة
    // يمكن إضافة إعادة تعيين هنا إذا لزم الأمر
  }

  /// إعادة إنشاء قاعدة البيانات
  Future<void> recreateDatabase() async {
    // تم نقل البيانات إلى قاعدة البيانات الموحدة
    // يمكن إضافة إعادة إنشاء هنا إذا لزم الأمر
  }

  /// الحصول على السلاسل حسب النوع
  Future<List<Map<String, dynamic>>> getSeriesByType(dynamic type) async {
    return await getAllProfileSeries();
  }

  /// إدراج ملف (اسم بديل)
  Future<int> insertProfile(dynamic profile) async {
    return await _unifiedDb.insertAluminumProfile(profile is Map ? profile : profile.toMap());
  }

  /// الحصول على تصميم المفصلة حسب السلسلة
  Future<dynamic> getHingeDesignBySeries(int seriesId) async {
    // إرجاع null لأن هذه الدالة غير مطلوبة حالياً
    return null;
  }

  /// تحديث تصميم المفصلة
  Future<int> updateHingeDesign(dynamic design) async {
    // إرجاع 0 لأن هذه الدالة غير مطلوبة حالياً
    return 0;
  }



  /// الحصول على تصميم الانزلاق حسب السلسلة وعدد الدلف
  Future<dynamic> getSlidingDesignBySeriesAndDalfaCount(int seriesId, int dalfaCount) async {
    // إرجاع null لأن هذه الدالة غير مطلوبة حالياً
    return null;
  }

  /// تحديث تصميم الانزلاق
  Future<int> updateSlidingDesign(dynamic design) async {
    // إرجاع 0 لأن هذه الدالة غير مطلوبة حالياً
    return 0;
  }

  /// الحصول على مجموعة قطاعات بالكود
  Future<ProfileSeries?> getSeriesByCode(String code) async {
    final db = await database;
    final result = await db.query(
      'profile_series',
      where: 'code = ?',
      whereArgs: [code],
    );
    return result.isNotEmpty ? ProfileSeries.fromMap(result.first) : null;
  }

  /// الحصول على قطاع بالكود
  Future<AluminumProfile?> getProfileByCode(String code) async {
    final db = await database;
    final result = await db.query(
      'aluminum_profiles',
      where: 'code = ?',
      whereArgs: [code],
    );
    return result.isNotEmpty ? AluminumProfile.fromMap(result.first) : null;
  }

  /// تحديث قطاع بالمعرف
  Future<int> updateProfileById(int id, Map<String, dynamic> data) async {
    return await _unifiedDb.updateAluminumProfile(id, data);
  }

}
