import 'package:flutter/material.dart';

abstract class AppLocalizations {
  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    delegate,
  ];

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];

  // App Info
  String get appTitle;
  String get appSubtitle;
  String get home;
  String get settings;
  String get about;

  // Services
  String get availableServices;
  String get searchHint;
  String get noServicesFound;

  // Main Services
  String get cuttingService;
  String get cuttingServiceDesc;
  String get costCalculation;
  String get costCalculationDesc;
  String get kitchenDesign;
  String get kitchenDesignDesc;
  String get windowsDoors;
  String get windowsDoorsDesc;
  String get aluminumDesign;
  String get aluminumDesignDesc;
  String get upvcDesign;
  String get upvcDesignDesc;
  String get projectManagement;
  String get projectManagementDesc;
  String get inventory;
  String get inventoryDesc;
  String get aiVision;
  String get aiVisionDesc;
  String get aiAssistant;
  String get aiAssistantDesc;
  String get aiDesign;
  String get aiDesignDesc;
  String get treasury;
  String get treasuryDesc;
  String get tasks;
  String get tasksDesc;

  // UI
  String get comingSoon;
  String get comingSoonMessage;
  String get ok;

  // Common Actions
  String get cancel;
  String get save;
  String get delete;
  String get edit;
  String get add;
  String get create;
  String get update;
  String get confirm;
  String get back;

  // Cutting Service
  String get cuttingProjects;
  String get addNewProject;
  String get projectNumber;
  String get customerName;
  String get date;
  String get deliveryDate;
  String get phone;
  String get address;
  String get notes;
  String get actions;
  String get deleteProject;
  String get editProject;
  String get confirmDelete;
  String get deleteProjectMessage;
  String get projectDetails;
  String get required;
  String get pleaseEnter;
  String get projectSaved;
  String get projectUpdated;
  String get projectDeleted;
  String get error;
  String get noProjectsFound;
  String get searchProjects;

  // About
  String get aboutDescription;
  String get aboutCopyright;
  String get version;

  // Currency settings
  String get currency;
  String get currencySettings;
  String get currencyName;
  String get currencySymbol;
  String get selectCurrency;
  String get customCurrency;
  String get enterCurrencyName;
  String get enterCurrencySymbol;
  String get predefinedCurrencies;
  String get currencyUpdated;

  // Treasury and Financial
  String get treasurySummary;
  String get overallSummary;
  String get totalIncome;
  String get totalExpenses;
  String get totalBalance;
  String get income;
  String get expenses;
  String get balance;
  String get transactions;
  String get addTransaction;
  String get editTransaction;
  String get deleteTransaction;
  String get deleteTransactionConfirmation;
  String get transactionAdded;
  String get transactionUpdated;
  String get transactionDeleted;
  String get incomeOrExpensesRequired;
  String get description;
  String get enterDescription;
  String get descriptionRequired;
  String get enterNotes;
  String get reset;
  String get noTransactions;
  String get previousBalance;
  String get currentBalance;
  String get finalBalance;
  String get treasuries;
  String get addTreasury;
  String get treasuryName;
  String get initialBalance;
  String get treasuryNameRequired;
  String get searchTreasury;
  String get noTreasuriesFound;
  String get noTreasuriesMatchSearch;
  String get treasurySummaryDesc;
  String get selectDateRange;
  String get exportReport;
  String get overview;
  String get trends;
  String get categories;
  String get dateRange;
  String get change;
  String get incomeVsExpenses;
  String get treasuryBalances;
  String get noDataAvailable;
  String get monthlyTrends;
  String get topIncomeSources;
  String get topExpenseCategories;
  String get todaySummary;
  String get recentTransactions;
  String errorLoadingData(String error);

  // Additional missing strings
  String get viewProject;
  String get automaticNumber;
  String get sticks;
  String get boards;
  String get itemDetails;
  String get itemNumber;
  String get enterMeasurements;
  String get savePrice;
  String get itemName;
  String get requiredBoards;
  String get unitPrice;
  String get totalAmount;
  String get discountPercent;
  String get discountAmount;
  String get finalAmount;
  String get projectView;
  String get orderItems;
  String get createNewOrder;
  String get noItemsYet;
  String get addFirstItem;
  String get selectItem;
  String get clickItemToView;
  String get addItem;
  String get cutting;
  String get itemType;
  String get enterItemName;

  // Task Management System
  String get taskManagement;
  String get taskManagementDesc;
  String get allTasks;
  String get addTask;
  String get editTask;
  String get deleteTask;
  String get taskTitle;
  String get taskDescription;
  String get taskCategory;
  String get taskPriority;
  String get taskDueDate;
  String get taskStatus;
  String get taskNotes;
  String get completed;
  String get pending;
  String get inProgress;
  String get overdue;
  String get high;
  String get medium;
  String get low;
  String get urgent;
  String get normal;
  String get taskCategories;
  String get addCategory;
  String get editCategory;
  String get deleteCategory;
  String get categoryName;
  String get categoryColor;
  String get categoryIcon;
  String get noTasksFound;
  String get noTasksInCategory;
  String get searchTasks;
  String get filterTasks;
  String get sortTasks;
  String get taskDetails;
  String get markAsCompleted;
  String get markAsPending;
  String get taskCreated;
  String get taskUpdated;
  String get taskDeleted;
  String get categoryCreated;
  String get categoryUpdated;
  String get categoryDeleted;
  String get confirmDeleteTask;
  String get confirmDeleteCategory;
  String get deleteTaskMessage;
  String get deleteCategoryMessage;
  String get taskTitleRequired;
  String get categoryNameRequired;
  String get selectCategory;
  String get selectPriority;
  String get selectDueDate;
  String get noDueDate;
  String get today;
  String get tomorrow;
  String get thisWeek;
  String get nextWeek;
  String get thisMonth;
  String get nextMonth;
  String get custom;
  String get dueDatePassed;
  String get dueToday;
  String get dueTomorrow;
  String get dueThisWeek;
  String get taskStatistics;
  String get totalTasks;
  String get completedTasks;
  String get pendingTasks;
  String get overdueTasks;
  String get completionRate;
  String get recentTasks;
  String get upcomingTasks;
  String get tasksByCategory;
  String get tasksByPriority;
  String get productivity;
  String get thisWeekProgress;
  String get lastWeekProgress;
  String get monthlyProgress;
  String get yearlyProgress;

  // Customer and Supplier Outstanding Amounts
  String get outstandingFromCustomers;
  String get outstandingToSuppliers;

  // Aluminum Quotations
  String get aluminumQuotations;
  String get aluminumQuotationsDesc;
  String get addQuotation;
  String get editQuotation;
  String get quotationNumber;
  String get quotationDate;
  String get clientName;
  String get clientPhone;
  String get clientAddress;
  String get quotationNotes;
  String get quotationItems;
  String get addQuotationItem;
  String get windowType;
  String get doorType;
  String get itemWidth;
  String get itemHeight;
  String get itemQuantity;
  String get itemNotes;
  String get hingeDoor;
  String get slidingDoor;
  String get hingeWindow;
  String get slidingWindow;
  String get fixedWindow;

  // Aluminum Settings
  String get aluminumSettings;
  String get hingeProfiles;
  String get slidingProfiles;
  String get glass;
  String get profiles;
  String get designs;
  String get glassTypes;
  String get georgia;
  String get spacing;

  // Aluminum Profile Types
  String get halaf;
  String get bar;
  String get dalfa;
  String get marad;
  String get baketa;
  String get soas;
  String get dalfaSilk;
  String get olba;
  String get filta;
  String get anf;
  String get skineh;

  // Profile Details
  String get profileName;
  String get profileNumber;
  String get profileThickness;
  String get lipType;
  String get lipThickness;
  String get addNewProfile;
  String get editProfile;
  String get deleteProfile;
  String get profileCode;
  String get profileWidth;
  String get profileHeight;  String get profileWeight;
  String get profileColor;
  String get profileDescription;
  String get noProfilesFound;
  String get profileSaved;
  String get profileUpdated;
  String get profileDeleted;

  // Additional profile and glass keys
  String get profileManagement;
  String get profileMeasurements;
  String get glassThickness;
  String get glassPrices;
  String get dateRequired;

  // uPVC Quotations
  String get upvcQuotations;
  String get upvcQuotationsDesc;
  String get upvcSettings;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'ar'),
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<AppLocalizations> load(Locale locale) => _load(locale);
  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

bool _isSupported(Locale locale) {
  for (var supportedLocale in _AppLocalizationsDelegate().supportedLocales) {
    if (supportedLocale.languageCode == locale.languageCode) {
      return true;
    }
  }
  return false;
}

Future<AppLocalizations> _load(Locale locale) {
  switch (locale.languageCode) {
    case 'ar':
      return Future.value(AppLocalizationsAr());
    case 'en':
      return Future.value(AppLocalizationsEn());
    default:
      return Future.value(AppLocalizationsAr());
  }
}

class AppLocalizationsAr extends AppLocalizations {
  @override
  String get appTitle => 'المساعد الذكي Uptime_Smart_Assist';

  @override
  String get appSubtitle =>
      'لخدمات المطابخ والألومنيوم وخدمات الذكاء الاصطناعى';

  @override
  String get home => 'الرئيسية';

  @override
  String get settings => 'الإعدادات';

  @override
  String get about => 'حول التطبيق';

  @override
  String get availableServices => 'الخدمات المتاحة';

  @override
  String get searchHint => 'ابحث عن خدمة...';

  @override
  String get noServicesFound => 'لا توجد خدمات تطابق بحثك.';

  @override
  String get cuttingService => 'خدمة التقطيع';

  @override
  String get cuttingServiceDesc => 'تقطيع أعواد الألومنيوم - ألواح خشب وزجاج وفيبر';

  @override
  String get costCalculation => 'حساب التكلفة';

  @override
  String get costCalculationDesc => 'حساب تكلفة المواد والعمالة';

  @override
  String get kitchenDesign => 'تخصيم المطابخ';

  @override
  String get kitchenDesignDesc => 'تخصيم ورسم المطابخ';

  @override
  String get windowsDoors => 'الأبواب والشبابيك';

  @override
  String get windowsDoorsDesc => 'حساب الألومنيوم وuPVC';

  @override
  String get aluminumDesign => 'تخصيمات الألومنيوم';

  @override
  String get aluminumDesignDesc => 'تخصيم وحساب أعمال الألومنيوم';

  @override
  String get upvcDesign => 'تخصيمات uPVC';

  @override
  String get upvcDesignDesc => 'تخصيم وحساب أعمال uPVC';

  @override
  String get projectManagement => 'إدارة المشاريع';

  @override
  String get projectManagementDesc => 'متابعة وإدارة المشاريع';

  @override
  String get inventory => 'المخزن';

  @override
  String get inventoryDesc => 'إدارة المخزون والمواد';

  @override
  String get aiVision => 'تحليل الصور';

  @override
  String get aiVisionDesc => 'التعرف على الصور واستخراج النصوص';

  @override
  String get aiAssistant => 'مستشار الذكاء';

  @override
  String get aiAssistantDesc => 'اسأل عن أي شيء بالذكاء الاصطناعي';

  @override
  String get aiDesign => 'ابتكر تصميم';

  @override
  String get aiDesignDesc => 'ابتكر تصاميم مذهلة بالذكاء الاصطناعي';

  @override
  String get treasury => 'الخزينة';

  @override
  String get treasuryDesc => 'إدارة الخزينة والمعاملات المالية';

  @override
  String get tasks => 'قائمة المهام';

  @override
  String get tasksDesc => 'تنظيم وإدارة قائمة المهام اليومية';

  @override
  String get comingSoon => 'قريباً';

  @override
  String get comingSoonMessage => 'هذه الخدمة قيد التطوير وستكون متاحة قريباً!';

  @override
  String get ok => 'حسناً';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get create => 'إنشاء';

  @override
  String get update => 'تحديث';

  @override
  String get confirm => 'تأكيد';

  @override
  String get back => 'رجوع';

  @override
  String get aboutDescription =>
      'تطبيق المساعد الذكي لخدمات المطابخ والألومنيوم يوفر مجموعة متكاملة من الأدوات لتسهيل عمل المتخصصين في هذا المجال.';

  @override
  String get aboutCopyright =>
      'تم تطوير التطبيق بواسطة فريق Uptime Smart Solutions © 2025';

  @override
  String get version => 'الإصدار 1.0.0';

  // Cutting Service - Arabic
  @override
  String get cuttingProjects => 'مشاريع التقطيع';

  @override
  String get addNewProject => 'إضافة مشروع جديد';

  @override
  String get projectNumber => 'رقم المشروع';

  @override
  String get customerName => 'اسم العميل';

  @override
  String get date => 'التاريخ';

  @override
  String get deliveryDate => 'تاريخ التسليم';

  @override
  String get phone => 'التليفون';

  @override
  String get address => 'العنوان';

  @override
  String get notes => 'ملاحظات';

  @override
  String get actions => 'الإجراءات';

  @override
  String get deleteProject => 'حذف المشروع';

  @override
  String get editProject => 'تعديل المشروع';

  @override
  String get confirmDelete => 'تأكيد الحذف';

  @override
  String get deleteProjectMessage => 'هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get projectDetails => 'تفاصيل المشروع';

  @override
  String get required => 'مطلوب';

  @override
  String get pleaseEnter => 'يرجى إدخال';

  @override
  String get projectSaved => 'تم حفظ المشروع بنجاح';

  @override
  String get projectUpdated => 'تم تحديث المشروع بنجاح';

  @override
  String get projectDeleted => 'تم حذف المشروع بنجاح';

  @override
  String get error => 'خطأ';

  @override
  String get noProjectsFound => 'لا توجد مشاريع';

  @override
  String get searchProjects => 'البحث في المشاريع...';

  // Currency settings - Arabic
  @override
  String get currency => 'العملة';

  @override
  String get currencySettings => 'إعدادات العملة';

  @override
  String get currencyName => 'اسم العملة';

  @override
  String get currencySymbol => 'رمز العملة';

  @override
  String get selectCurrency => 'اختر العملة';

  @override
  String get customCurrency => 'عملة مخصصة';

  @override
  String get enterCurrencyName => 'أدخل اسم العملة';

  @override
  String get enterCurrencySymbol => 'أدخل رمز العملة';

  @override
  String get predefinedCurrencies => 'العملات المحددة مسبقاً';

  @override
  String get currencyUpdated => 'تم تحديث العملة بنجاح';

  // Treasury and Financial - Arabic
  @override
  String get treasurySummary => 'ملخص الخزينة';

  @override
  String get overallSummary => 'الملخص العام';

  @override
  String get totalIncome => 'إجمالي الإيرادات';

  @override
  String get totalExpenses => 'إجمالي المصروفات';

  @override
  String get totalBalance => 'الرصيد الإجمالي';

  @override
  String get income => 'إيراد';

  @override
  String get expenses => 'مصروف';

  @override
  String get balance => 'الرصيد';

  @override
  String get transactions => 'المعاملات';

  @override
  String get addTransaction => 'إضافة معاملة';

  @override
  String get editTransaction => 'تعديل المعاملة';

  @override
  String get deleteTransaction => 'حذف المعاملة';

  @override
  String get deleteTransactionConfirmation => 'هل أنت متأكد من حذف هذه المعاملة؟';

  @override
  String get transactionAdded => 'تم إضافة المعاملة بنجاح';

  @override
  String get transactionUpdated => 'تم تحديث المعاملة بنجاح';

  @override
  String get transactionDeleted => 'تم حذف المعاملة بنجاح';

  @override
  String get incomeOrExpensesRequired => 'يجب إدخال مبلغ الإيراد أو المصروف';

  @override
  String get description => 'الوصف';

  @override
  String get enterDescription => 'أدخل الوصف';

  @override
  String get descriptionRequired => 'الوصف مطلوب';

  @override
  String get enterNotes => 'أدخل الملاحظات';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get noTransactions => 'لا توجد معاملات';

  @override
  String get previousBalance => 'الرصيد السابق';

  @override
  String get currentBalance => 'الرصيد الحالي';

  @override
  String get finalBalance => 'الرصيد النهائي';

  @override
  String get treasuries => 'الخزائن';

  @override
  String get addTreasury => 'إضافة خزينة';

  @override
  String get treasuryName => 'اسم الخزينة';

  @override
  String get initialBalance => 'الرصيد الابتدائي';

  @override
  String get treasuryNameRequired => 'اسم الخزينة مطلوب';

  @override
  String get searchTreasury => 'البحث في الخزائن...';

  @override
  String get noTreasuriesFound => 'لا توجد خزائن';

  @override
  String get noTreasuriesMatchSearch => 'لا توجد خزائن تطابق البحث';

  @override
  String get treasurySummaryDesc => 'عرض ملخص شامل للخزينة والمعاملات المالية';

  @override
  String get selectDateRange => 'اختر نطاق التاريخ';

  @override
  String get exportReport => 'تصدير التقرير';

  @override
  String get overview => 'نظرة عامة';

  @override
  String get trends => 'الاتجاهات';

  @override
  String get categories => 'الفئات';

  @override
  String get dateRange => 'نطاق التاريخ';

  @override
  String get change => 'التغيير';

  @override
  String get incomeVsExpenses => 'الإيرادات مقابل المصروفات';

  @override
  String get treasuryBalances => 'أرصدة الخزائن';

  @override
  String get noDataAvailable => 'لا توجد بيانات متاحة';

  @override
  String get monthlyTrends => 'الاتجاهات الشهرية';

  @override
  String get topIncomeSources => 'أهم مصادر الإيرادات';

  @override
  String get topExpenseCategories => 'أهم فئات المصروفات';

  @override
  String get todaySummary => 'ملخص اليوم';

  @override
  String get recentTransactions => 'المعاملات الأخيرة';

  @override
  String errorLoadingData(String error) => 'خطأ في تحميل البيانات: $error';

  // Additional missing strings - Arabic
  @override
  String get viewProject => 'عرض المشروع';

  @override
  String get automaticNumber => 'رقم تلقائي';

  @override
  String get sticks => 'أعواد';

  @override
  String get boards => 'ألواح';

  @override
  String get itemDetails => 'تفاصيل العنصر';

  @override
  String get itemNumber => 'رقم العنصر';

  @override
  String get enterMeasurements => 'إدخال القياسات';

  @override
  String get savePrice => 'حفظ السعر';

  @override
  String get itemName => 'اسم العنصر';

  @override
  String get requiredBoards => 'الألواح المطلوبة';

  @override
  String get unitPrice => 'سعر الوحدة';

  @override
  String get totalAmount => 'المبلغ الإجمالي';

  @override
  String get discountPercent => 'نسبة الخصم';

  @override
  String get discountAmount => 'مبلغ الخصم';

  @override
  String get finalAmount => 'المبلغ النهائي';

  @override
  String get projectView => 'عرض المشروع';

  @override
  String get orderItems => 'عناصر الطلب';

  @override
  String get createNewOrder => 'إنشاء طلب جديد';

  @override
  String get noItemsYet => 'لا توجد عناصر بعد';

  @override
  String get addFirstItem => 'أضف أول عنصر';

  @override
  String get selectItem => 'اختر عنصر';

  @override
  String get clickItemToView => 'انقر على عنصر لعرضه';

  @override
  String get addItem => 'إضافة عنصر';

  @override
  String get cutting => 'التقطيع';

  @override
  String get itemType => 'نوع العنصر';

  @override
  String get enterItemName => 'أدخل اسم العنصر';

  // Task Management System - Arabic
  @override
  String get taskManagement => 'منظم المهام';

  @override
  String get taskManagementDesc => 'تنظيم وإدارة قائمة المهام اليومية والمشاريع';

  @override
  String get allTasks => 'جميع المهام';

  @override
  String get addTask => 'إضافة مهمة';

  @override
  String get editTask => 'تعديل المهمة';

  @override
  String get deleteTask => 'حذف المهمة';

  @override
  String get taskTitle => 'عنوان المهمة';

  @override
  String get taskDescription => 'وصف المهمة';

  @override
  String get taskCategory => 'فئة المهمة';

  @override
  String get taskPriority => 'أولوية المهمة';

  @override
  String get taskDueDate => 'تاريخ الاستحقاق';

  @override
  String get taskStatus => 'حالة المهمة';

  @override
  String get taskNotes => 'ملاحظات المهمة';

  @override
  String get completed => 'مكتملة';

  @override
  String get pending => 'معلقة';

  @override
  String get inProgress => 'قيد التنفيذ';

  @override
  String get overdue => 'متأخرة';

  @override
  String get high => 'عالية';

  @override
  String get medium => 'متوسطة';

  @override
  String get low => 'منخفضة';

  @override
  String get urgent => 'عاجلة';

  @override
  String get normal => 'عادية';

  @override
  String get taskCategories => 'فئات المهام';

  @override
  String get addCategory => 'إضافة فئة';

  @override
  String get editCategory => 'تعديل الفئة';

  @override
  String get deleteCategory => 'حذف الفئة';

  @override
  String get categoryName => 'اسم الفئة';

  @override
  String get categoryColor => 'لون الفئة';

  @override
  String get categoryIcon => 'أيقونة الفئة';

  @override
  String get noTasksFound => 'لا توجد مهام';

  @override
  String get noTasksInCategory => 'لا توجد مهام في هذه الفئة';

  @override
  String get searchTasks => 'البحث في المهام...';

  @override
  String get filterTasks => 'تصفية المهام';

  @override
  String get sortTasks => 'ترتيب المهام';

  @override
  String get taskDetails => 'تفاصيل المهمة';

  @override
  String get markAsCompleted => 'تحديد كمكتملة';

  @override
  String get markAsPending => 'تحديد كمعلقة';

  @override
  String get taskCreated => 'تم إنشاء المهمة بنجاح';

  @override
  String get taskUpdated => 'تم تحديث المهمة بنجاح';

  @override
  String get taskDeleted => 'تم حذف المهمة بنجاح';

  @override
  String get categoryCreated => 'تم إنشاء الفئة بنجاح';

  @override
  String get categoryUpdated => 'تم تحديث الفئة بنجاح';

  @override
  String get categoryDeleted => 'تم حذف الفئة بنجاح';

  @override
  String get confirmDeleteTask => 'تأكيد حذف المهمة';

  @override
  String get confirmDeleteCategory => 'تأكيد حذف الفئة';

  @override
  String get deleteTaskMessage => 'هل أنت متأكد من حذف هذه المهمة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get deleteCategoryMessage => 'هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع المهام المرتبطة بها.';

  @override
  String get taskTitleRequired => 'عنوان المهمة مطلوب';

  @override
  String get categoryNameRequired => 'اسم الفئة مطلوب';

  @override
  String get selectCategory => 'اختر الفئة';

  @override
  String get selectPriority => 'اختر الأولوية';

  @override
  String get selectDueDate => 'اختر تاريخ الاستحقاق';

  @override
  String get noDueDate => 'بدون تاريخ استحقاق';

  @override
  String get today => 'اليوم';

  @override
  String get tomorrow => 'غداً';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get nextWeek => 'الأسبوع القادم';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get nextMonth => 'الشهر القادم';

  @override
  String get custom => 'مخصص';

  @override
  String get dueDatePassed => 'تاريخ الاستحقاق مضى';

  @override
  String get dueToday => 'مستحقة اليوم';

  @override
  String get dueTomorrow => 'مستحقة غداً';

  @override
  String get dueThisWeek => 'مستحقة هذا الأسبوع';

  @override
  String get taskStatistics => 'إحصائيات المهام';

  @override
  String get totalTasks => 'إجمالي المهام';

  @override
  String get completedTasks => 'المهام المكتملة';

  @override
  String get pendingTasks => 'المهام المعلقة';

  @override
  String get overdueTasks => 'المهام المتأخرة';

  @override
  String get completionRate => 'معدل الإنجاز';

  @override
  String get recentTasks => 'المهام الحديثة';

  @override
  String get upcomingTasks => 'المهام القادمة';

  @override
  String get tasksByCategory => 'المهام حسب الفئة';

  @override
  String get tasksByPriority => 'المهام حسب الأولوية';

  @override
  String get productivity => 'الإنتاجية';

  @override
  String get thisWeekProgress => 'تقدم هذا الأسبوع';

  @override
  String get lastWeekProgress => 'تقدم الأسبوع الماضي';

  @override
  String get monthlyProgress => 'التقدم الشهري';

  @override
  String get yearlyProgress => 'التقدم السنوي';

  // Customer and Supplier Outstanding Amounts - Arabic
  @override
  String get outstandingFromCustomers => 'المبلغ المستحق من العملاء';

  @override
  String get outstandingToSuppliers => 'المبلغ المستحق للموردين';

  // Aluminum Quotations - Arabic
  @override
  String get aluminumQuotations => 'مقايسات الألومنيوم';

  @override
  String get aluminumQuotationsDesc => 'إنشاء وإدارة مقايسات الألومنيوم';

  @override
  String get addQuotation => 'إضافة مقايسة';

  @override
  String get editQuotation => 'تعديل مقايسة';

  @override
  String get quotationNumber => 'رقم المقايسة';

  @override
  String get quotationDate => 'تاريخ المقايسة';

  @override
  String get clientName => 'اسم العميل';

  @override
  String get clientPhone => 'هاتف العميل';

  @override
  String get clientAddress => 'عنوان العميل';

  @override
  String get quotationNotes => 'ملاحظات المقايسة';

  @override
  String get quotationItems => 'بنود المقايسة';

  @override
  String get addQuotationItem => 'إضافة بند';

  @override
  String get windowType => 'نوع الشباك';

  @override
  String get doorType => 'نوع الباب';

  @override
  String get itemWidth => 'العرض';

  @override
  String get itemHeight => 'الارتفاع';

  @override
  String get itemQuantity => 'الكمية';

  @override
  String get itemNotes => 'ملاحظات البند';

  @override
  String get hingeDoor => 'باب مفصلي';

  @override
  String get slidingDoor => 'باب سحاب';

  @override
  String get hingeWindow => 'شباك مفصلي';

  @override
  String get slidingWindow => 'شباك سحاب';

  @override
  String get fixedWindow => 'شباك ثابت';

  // Aluminum Settings - Arabic
  @override
  String get aluminumSettings => 'إعدادات الألومنيوم';

  @override
  String get hingeProfiles => 'قطاعات مفصلي';

  @override
  String get slidingProfiles => 'قطاعات سحاب';

  @override
  String get glass => 'زجاج';

  @override
  String get profiles => 'قطاعات';

  @override
  String get designs => 'تخصيمات';

  @override
  String get glassTypes => 'أنواع الزجاج';

  @override
  String get georgia => 'الجورجيا';

  @override
  String get spacing => 'الفراغ';

  // Aluminum Profile Types - Arabic
  @override
  String get halaf => 'حلف';

  @override
  String get bar => 'بار';

  @override
  String get dalfa => 'ضلفة';

  @override
  String get marad => 'مرد';

  @override
  String get baketa => 'باكتة';

  @override
  String get soas => 'سؤاس';

  @override
  String get dalfaSilk => 'ضلفة سلك';

  @override
  String get olba => 'علبة';

  @override
  String get filta => 'فلتة';

  @override
  String get anf => 'الانف';

  @override
  String get skineh => 'سكينة';

  // Profile Details - Arabic
  @override
  String get profileName => 'اسم القطاع';

  @override
  String get profileNumber => 'رقم القطاع';

  @override
  String get profileThickness => 'سمك القطاع';

  @override
  String get lipType => 'نوع الشفة';

  @override
  String get lipThickness => 'سمك الشفة';

  @override
  String get addNewProfile => 'إضافة قطاع جديد';

  @override
  String get editProfile => 'تعديل القطاع';

  @override
  String get deleteProfile => 'حذف القطاع';

  @override
  String get profileCode => 'كود القطاع';

  @override
  String get profileWidth => 'عرض القطاع';

  @override
  String get profileHeight => 'ارتفاع القطاع';

  @override
  String get profileWeight => 'وزن القطاع';

  @override
  String get profileColor => 'لون القطاع';

  @override
  String get profileDescription => 'وصف القطاع';

  @override
  String get noProfilesFound => 'لا توجد قطاعات';

  @override
  String get profileSaved => 'تم حفظ القطاع بنجاح';
  @override
  String get profileUpdated => 'تم تحديث القطاع بنجاح';

  @override
  String get profileDeleted => 'تم حذف القطاع بنجاح';

  @override
  String get profileManagement => 'إدارة القطاعات';

  @override
  String get profileMeasurements => 'مقاسات القطاعات';

  @override
  String get glassThickness => 'سمك الزجاج';

  @override
  String get glassPrices => 'أسعار الزجاج';

  @override
  String get dateRequired => 'التاريخ مطلوب';

  // uPVC Quotations - Arabic
  @override
  String get upvcQuotations => 'مقايسات uPVC';

  @override
  String get upvcQuotationsDesc => 'إدارة مقايسات أعمال uPVC';

  @override
  String get upvcSettings => 'إعدادات uPVC';
}

class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appTitle => 'Uptime Smart Assist';

  @override
  String get appSubtitle => 'Kitchen, Aluminum & AI Services';

  @override
  String get home => 'Home';

  @override
  String get settings => 'Settings';

  @override
  String get about => 'About';

  @override
  String get availableServices => 'Available Services';

  @override
  String get searchHint => 'Search for a service...';

  @override
  String get noServicesFound => 'No services match your search.';

  @override
  String get cuttingService => 'Cutting Service';

  @override
  String get cuttingServiceDesc => 'Cut aluminum rods - wood, glass and fiber boards';

  @override
  String get costCalculation => 'Cost Calculation';

  @override
  String get costCalculationDesc => 'Calculate material and labor costs';

  @override
  String get kitchenDesign => 'Kitchen Customization';

  @override
  String get kitchenDesignDesc => 'Customize and design kitchens';

  @override
  String get windowsDoors => 'Windows & Doors';

  @override
  String get windowsDoorsDesc => 'Calculate aluminum and uPVC';

  @override
  String get aluminumDesign => 'Aluminum Customization';

  @override
  String get aluminumDesignDesc => 'Customize and calculate aluminum works';

  @override
  String get upvcDesign => 'uPVC Customization';

  @override
  String get upvcDesignDesc => 'Customize and calculate uPVC works';

  @override
  String get projectManagement => 'Project Management';

  @override
  String get projectManagementDesc => 'Track and manage projects';

  @override
  String get inventory => 'Inventory';

  @override
  String get inventoryDesc => 'Manage inventory and materials';

  @override
  String get aiVision => 'AI Vision';

  @override
  String get aiVisionDesc => 'Image recognition and text extraction';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get aiAssistantDesc => 'Ask anything with AI';

  @override
  String get aiDesign => 'AI Design';

  @override
  String get aiDesignDesc => 'Create amazing designs with AI';

  @override
  String get treasury => 'Treasury';

  @override
  String get treasuryDesc => 'Manage treasury and financial transactions';

  @override
  String get tasks => 'Task List';

  @override
  String get tasksDesc => 'Organize and manage daily tasks';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get comingSoonMessage => 'This service is under development and will be available soon!';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get create => 'Create';

  @override
  String get update => 'Update';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String get aboutDescription =>
      'Smart Assistant app for kitchen and aluminum services provides a comprehensive set of tools to facilitate the work of specialists in this field.';

  @override
  String get aboutCopyright =>
      'Developed by Uptime Smart Solutions team © 2025';

  @override
  String get version => 'Version 1.0.0';

  // Cutting Service - English
  @override
  String get cuttingProjects => 'Cutting Projects';

  @override
  String get addNewProject => 'Add New Project';

  @override
  String get projectNumber => 'Project Number';

  @override
  String get customerName => 'Customer Name';

  @override
  String get date => 'Date';

  @override
  String get deliveryDate => 'Delivery Date';

  @override
  String get phone => 'Phone';

  @override
  String get address => 'Address';

  @override
  String get notes => 'Notes';

  @override
  String get actions => 'Actions';

  @override
  String get deleteProject => 'Delete Project';

  @override
  String get editProject => 'Edit Project';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get deleteProjectMessage => 'Are you sure you want to delete this project? This action cannot be undone.';

  @override
  String get projectDetails => 'Project Details';

  @override
  String get required => 'Required';

  @override
  String get pleaseEnter => 'Please enter';

  @override
  String get projectSaved => 'Project saved successfully';

  @override
  String get projectUpdated => 'Project updated successfully';

  @override
  String get projectDeleted => 'Project deleted successfully';

  @override
  String get error => 'Error';

  @override
  String get noProjectsFound => 'No projects found';

  @override
  String get searchProjects => 'Search projects...';

  // Currency settings - English
  @override
  String get currency => 'Currency';

  @override
  String get currencySettings => 'Currency Settings';

  @override
  String get currencyName => 'Currency Name';

  @override
  String get currencySymbol => 'Currency Symbol';

  @override
  String get selectCurrency => 'Select Currency';

  @override
  String get customCurrency => 'Custom Currency';

  @override
  String get enterCurrencyName => 'Enter currency name';

  @override
  String get enterCurrencySymbol => 'Enter currency symbol';

  @override
  String get predefinedCurrencies => 'Predefined Currencies';

  @override
  String get currencyUpdated => 'Currency updated successfully';

  // Treasury and Financial - English
  @override
  String get treasurySummary => 'Treasury Summary';

  @override
  String get overallSummary => 'Overall Summary';

  @override
  String get totalIncome => 'Total Income';

  @override
  String get totalExpenses => 'Total Expenses';

  @override
  String get totalBalance => 'Total Balance';

  @override
  String get income => 'Income';

  @override
  String get expenses => 'Expenses';

  @override
  String get balance => 'Balance';

  @override
  String get transactions => 'Transactions';

  @override
  String get addTransaction => 'Add Transaction';

  @override
  String get editTransaction => 'Edit Transaction';

  @override
  String get deleteTransaction => 'Delete Transaction';

  @override
  String get deleteTransactionConfirmation => 'Are you sure you want to delete this transaction?';

  @override
  String get transactionAdded => 'Transaction added successfully';

  @override
  String get transactionUpdated => 'Transaction updated successfully';

  @override
  String get transactionDeleted => 'Transaction deleted successfully';

  @override
  String get incomeOrExpensesRequired => 'Income or expense amount is required';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Enter description';

  @override
  String get descriptionRequired => 'Description is required';

  @override
  String get enterNotes => 'Enter notes';

  @override
  String get reset => 'Reset';

  @override
  String get noTransactions => 'No transactions';

  @override
  String get previousBalance => 'Previous Balance';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get finalBalance => 'Final Balance';

  @override
  String get treasuries => 'Treasuries';

  @override
  String get addTreasury => 'Add Treasury';

  @override
  String get treasuryName => 'Treasury Name';

  @override
  String get initialBalance => 'Initial Balance';

  @override
  String get treasuryNameRequired => 'Treasury name is required';

  @override
  String get searchTreasury => 'Search treasuries...';

  @override
  String get noTreasuriesFound => 'No treasuries found';

  @override
  String get noTreasuriesMatchSearch => 'No treasuries match your search';

  @override
  String get treasurySummaryDesc => 'View comprehensive treasury and financial transactions summary';

  @override
  String get selectDateRange => 'Select Date Range';

  @override
  String get exportReport => 'Export Report';

  @override
  String get overview => 'Overview';

  @override
  String get trends => 'Trends';

  @override
  String get categories => 'Categories';

  @override
  String get dateRange => 'Date Range';

  @override
  String get change => 'Change';

  @override
  String get incomeVsExpenses => 'Income vs Expenses';

  @override
  String get treasuryBalances => 'Treasury Balances';

  @override
  String get noDataAvailable => 'No data available';

  @override
  String get monthlyTrends => 'Monthly Trends';

  @override
  String get topIncomeSources => 'Top Income Sources';

  @override
  String get topExpenseCategories => 'Top Expense Categories';

  @override
  String get todaySummary => 'Today\'s Summary';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String errorLoadingData(String error) => 'Error loading data: $error';

  // Additional missing strings - English
  @override
  String get viewProject => 'View Project';

  @override
  String get automaticNumber => 'Automatic Number';

  @override
  String get sticks => 'Sticks';

  @override
  String get boards => 'Boards';

  @override
  String get itemDetails => 'Item Details';

  @override
  String get itemNumber => 'Item Number';

  @override
  String get enterMeasurements => 'Enter Measurements';

  @override
  String get savePrice => 'Save Price';

  @override
  String get itemName => 'Item Name';

  @override
  String get requiredBoards => 'Required Boards';

  @override
  String get unitPrice => 'Unit Price';

  @override
  String get totalAmount => 'Total Amount';

  @override
  String get discountPercent => 'Discount Percent';

  @override
  String get discountAmount => 'Discount Amount';

  @override
  String get finalAmount => 'Final Amount';

  @override
  String get projectView => 'Project View';

  @override
  String get orderItems => 'Order Items';

  @override
  String get createNewOrder => 'Create New Order';

  @override
  String get noItemsYet => 'No items yet';

  @override
  String get addFirstItem => 'Add first item';

  @override
  String get selectItem => 'Select Item';

  @override
  String get clickItemToView => 'Click item to view';

  @override
  String get addItem => 'Add Item';

  @override
  String get cutting => 'Cutting';

  @override
  String get itemType => 'Item Type';

  @override
  String get enterItemName => 'Enter item name';

  // Task Management System - English
  @override
  String get taskManagement => 'Task Manager';

  @override
  String get taskManagementDesc => 'Organize and manage daily tasks and projects';

  @override
  String get allTasks => 'All Tasks';

  @override
  String get addTask => 'Add Task';

  @override
  String get editTask => 'Edit Task';

  @override
  String get deleteTask => 'Delete Task';

  @override
  String get taskTitle => 'Task Title';

  @override
  String get taskDescription => 'Task Description';

  @override
  String get taskCategory => 'Task Category';

  @override
  String get taskPriority => 'Task Priority';

  @override
  String get taskDueDate => 'Due Date';

  @override
  String get taskStatus => 'Task Status';

  @override
  String get taskNotes => 'Task Notes';

  @override
  String get completed => 'Completed';

  @override
  String get pending => 'Pending';

  @override
  String get inProgress => 'In Progress';

  @override
  String get overdue => 'Overdue';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get urgent => 'Urgent';

  @override
  String get normal => 'Normal';

  @override
  String get taskCategories => 'Task Categories';

  @override
  String get addCategory => 'Add Category';

  @override
  String get editCategory => 'Edit Category';

  @override
  String get deleteCategory => 'Delete Category';

  @override
  String get categoryName => 'Category Name';

  @override
  String get categoryColor => 'Category Color';

  @override
  String get categoryIcon => 'Category Icon';

  @override
  String get noTasksFound => 'No tasks found';

  @override
  String get noTasksInCategory => 'No tasks in this category';

  @override
  String get searchTasks => 'Search tasks...';

  @override
  String get filterTasks => 'Filter Tasks';

  @override
  String get sortTasks => 'Sort Tasks';

  @override
  String get taskDetails => 'Task Details';

  @override
  String get markAsCompleted => 'Mark as Completed';

  @override
  String get markAsPending => 'Mark as Pending';

  @override
  String get taskCreated => 'Task created successfully';

  @override
  String get taskUpdated => 'Task updated successfully';

  @override
  String get taskDeleted => 'Task deleted successfully';

  @override
  String get categoryCreated => 'Category created successfully';

  @override
  String get categoryUpdated => 'Category updated successfully';

  @override
  String get categoryDeleted => 'Category deleted successfully';

  @override
  String get confirmDeleteTask => 'Confirm Delete Task';

  @override
  String get confirmDeleteCategory => 'Confirm Delete Category';

  @override
  String get deleteTaskMessage => 'Are you sure you want to delete this task? This action cannot be undone.';

  @override
  String get deleteCategoryMessage => 'Are you sure you want to delete this category? All associated tasks will be deleted.';

  @override
  String get taskTitleRequired => 'Task title is required';

  @override
  String get categoryNameRequired => 'Category name is required';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get selectPriority => 'Select Priority';

  @override
  String get selectDueDate => 'Select Due Date';

  @override
  String get noDueDate => 'No due date';

  @override
  String get today => 'Today';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get thisWeek => 'This Week';

  @override
  String get nextWeek => 'Next Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get nextMonth => 'Next Month';

  @override
  String get custom => 'Custom';

  @override
  String get dueDatePassed => 'Due date passed';

  @override
  String get dueToday => 'Due today';

  @override
  String get dueTomorrow => 'Due tomorrow';

  @override
  String get dueThisWeek => 'Due this week';

  @override
  String get taskStatistics => 'Task Statistics';

  @override
  String get totalTasks => 'Total Tasks';

  @override
  String get completedTasks => 'Completed Tasks';

  @override
  String get pendingTasks => 'Pending Tasks';

  @override
  String get overdueTasks => 'Overdue Tasks';

  @override
  String get completionRate => 'Completion Rate';

  @override
  String get recentTasks => 'Recent Tasks';

  @override
  String get upcomingTasks => 'Upcoming Tasks';

  @override
  String get tasksByCategory => 'Tasks by Category';

  @override
  String get tasksByPriority => 'Tasks by Priority';

  @override
  String get productivity => 'Productivity';

  @override
  String get thisWeekProgress => 'This Week Progress';

  @override
  String get lastWeekProgress => 'Last Week Progress';

  @override
  String get monthlyProgress => 'Monthly Progress';

  @override
  String get yearlyProgress => 'Yearly Progress';

  // Customer and Supplier Outstanding Amounts - English
  @override
  String get outstandingFromCustomers => 'Outstanding from Customers';

  @override
  String get outstandingToSuppliers => 'Outstanding to Suppliers';

  // Aluminum Quotations - English
  @override
  String get aluminumQuotations => 'Aluminum Quotations';

  @override
  String get aluminumQuotationsDesc => 'Create and manage aluminum quotations';

  @override
  String get addQuotation => 'Add Quotation';

  @override
  String get editQuotation => 'Edit Quotation';

  @override
  String get quotationNumber => 'Quotation Number';

  @override
  String get quotationDate => 'Quotation Date';

  @override
  String get clientName => 'Client Name';

  @override
  String get clientPhone => 'Client Phone';

  @override
  String get clientAddress => 'Client Address';

  @override
  String get quotationNotes => 'Quotation Notes';

  @override
  String get quotationItems => 'Quotation Items';

  @override
  String get addQuotationItem => 'Add Item';

  @override
  String get windowType => 'Window Type';

  @override
  String get doorType => 'Door Type';

  @override
  String get itemWidth => 'Width';

  @override
  String get itemHeight => 'Height';

  @override
  String get itemQuantity => 'Quantity';

  @override
  String get itemNotes => 'Item Notes';

  @override
  String get hingeDoor => 'Hinge Door';

  @override
  String get slidingDoor => 'Sliding Door';

  @override
  String get hingeWindow => 'Hinge Window';

  @override
  String get slidingWindow => 'Sliding Window';

  @override
  String get fixedWindow => 'Fixed Window';

  // Aluminum Settings - English
  @override
  String get aluminumSettings => 'Aluminum Settings';

  @override
  String get hingeProfiles => 'Hinge Profiles';

  @override
  String get slidingProfiles => 'Sliding Profiles';

  @override
  String get glass => 'Glass';

  @override
  String get profiles => 'Profiles';

  @override
  String get designs => 'Customizations';

  @override
  String get glassTypes => 'Glass Types';

  @override
  String get georgia => 'Georgia';

  @override
  String get spacing => 'Spacing';

  // Aluminum Profile Types - English
  @override
  String get halaf => 'Frame';

  @override
  String get bar => 'Bar';

  @override
  String get dalfa => 'Sash';

  @override
  String get marad => 'Mullion';

  @override
  String get baketa => 'Bead';

  @override
  String get soas => 'Sill';

  @override
  String get dalfaSilk => 'Screen Sash';

  @override
  String get olba => 'Box';

  @override
  String get filta => 'Gasket';

  @override
  String get anf => 'Nose';

  @override
  String get skineh => 'Track';

  // Profile Details - English
  @override
  String get profileName => 'Profile Name';

  @override
  String get profileNumber => 'Profile Number';

  @override
  String get profileThickness => 'Profile Thickness';

  @override
  String get lipType => 'Lip Type';

  @override
  String get lipThickness => 'Lip Thickness';

  @override
  String get addNewProfile => 'Add New Profile';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get deleteProfile => 'Delete Profile';

  @override
  String get profileCode => 'Profile Code';

  @override
  String get profileWidth => 'Profile Width';

  @override
  String get profileHeight => 'Profile Height';

  @override
  String get profileWeight => 'Profile Weight';

  @override
  String get profileColor => 'Profile Color';

  @override
  String get profileDescription => 'Profile Description';

  @override
  String get noProfilesFound => 'No Profiles Found';

  @override
  String get profileSaved => 'Profile saved successfully';

  @override
  String get profileUpdated => 'Profile updated successfully';
  @override
  String get profileDeleted => 'Profile deleted successfully';

  @override
  String get profileManagement => 'Profile Management';

  @override
  String get profileMeasurements => 'Profile Measurements';

  @override
  String get glassThickness => 'Glass Thickness';

  @override
  String get glassPrices => 'Glass Prices';

  @override
  String get dateRequired => 'Date Required';

  // uPVC Quotations - English
  @override
  String get upvcQuotations => 'uPVC Quotations';

  @override
  String get upvcQuotationsDesc => 'Manage uPVC work quotations';

  @override
  String get upvcSettings => 'uPVC Settings';
}
