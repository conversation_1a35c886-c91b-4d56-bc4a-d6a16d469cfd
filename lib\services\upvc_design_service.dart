import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/upvc_design.dart';

class UnifiedUpvcService {
  static const String _databaseName = 'upvc_designs.db';
  static const int _databaseVersion = 1;
  static const String _hingeDesignsTable = 'upvc_hinge_designs';
  static const String _slidingDesignsTable = 'upvc_sliding_designs';

  Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // جدول تخصيمات المفصلي
    await db.execute('''
      CREATE TABLE $_hingeDesignsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_on_halaf REAL,
        marad_dalfa_complete REAL,
        marad_dalfa_with_kaab REAL,
        marad_between_dalfa REAL,
        dalfa_from_ground REAL,
        dalfa_glass REAL,
        fixed_glass REAL,
        moving_silk REAL,
        fixed_silk REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // جدول تخصيمات السحاب
    await db.execute('''
      CREATE TABLE $_slidingDesignsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        series_id INTEGER,
        dalfa_count INTEGER NOT NULL,
        dalfa_method INTEGER NOT NULL,
        dalfa_width_plus REAL,
        dalfa_height_minus REAL,
        skineh_height_minus REAL,
        silk_width_plus REAL,
        silk_height_minus REAL,
        glass_width_minus REAL,
        glass_height_minus REAL,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');
  }

  // === تخصيمات المفصلي ===

  Future<int> insertHingeDesign(UpvcHingeDesign design) async {
    final db = await database;
    return await db.insert(_hingeDesignsTable, design.toMap());
  }

  Future<List<UpvcHingeDesign>> getHingeDesignsBySeries(int seriesId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _hingeDesignsTable,
      where: 'series_id = ?',
      whereArgs: [seriesId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return UpvcHingeDesign.fromMap(maps[i]);
    });
  }

  Future<UpvcHingeDesign?> getHingeDesignById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _hingeDesignsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UpvcHingeDesign.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateHingeDesign(UpvcHingeDesign design) async {
    final db = await database;
    return await db.update(
      _hingeDesignsTable,
      design.toMap(),
      where: 'id = ?',
      whereArgs: [design.id],
    );
  }

  Future<int> deleteHingeDesign(int id) async {
    final db = await database;
    return await db.delete(
      _hingeDesignsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // === تخصيمات السحاب ===

  Future<int> insertSlidingDesign(UpvcSlidingDesign design) async {
    final db = await database;
    return await db.insert(_slidingDesignsTable, design.toMap());
  }

  Future<List<UpvcSlidingDesign>> getSlidingDesignsBySeries(int seriesId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _slidingDesignsTable,
      where: 'series_id = ?',
      whereArgs: [seriesId],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return UpvcSlidingDesign.fromMap(maps[i]);
    });
  }

  Future<UpvcSlidingDesign?> getSlidingDesignById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _slidingDesignsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UpvcSlidingDesign.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateSlidingDesign(UpvcSlidingDesign design) async {
    final db = await database;
    return await db.update(
      _slidingDesignsTable,
      design.toMap(),
      where: 'id = ?',
      whereArgs: [design.id],
    );
  }

  Future<int> deleteSlidingDesign(int id) async {
    final db = await database;
    return await db.delete(
      _slidingDesignsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // === وظائف عامة ===

  Future<void> insertSampleData() async {
    final db = await database;

    // التحقق من وجود بيانات
    final hingeCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_hingeDesignsTable'),
    );

    if (hingeCount != null && hingeCount > 0) {
      debugPrint('Sample uPVC design data already exists');
      return;
    }

    final now = DateTime.now();

    // إدراج تخصيمات مفصلي تجريبية
    final hingeDesign = UpvcHingeDesign(
      seriesId: 1, // افتراض وجود مجموعة بـ ID = 1
      dalfaOnHalaf: 1.5,
      maradDalfaComplete: 3.0,
      maradDalfaWithKaab: 2.5,
      maradBetweenDalfa: 4.0,
      dalfaFromGround: 1.0,
      dalfaGlass: 2.0,
      fixedGlass: 1.8,
      movingSilk: 1.2,
      fixedSilk: 1.0,
      createdAt: now,
      updatedAt: now,
    );

    await db.insert(_hingeDesignsTable, hingeDesign.toMap());

    // إدراج تخصيمات سحاب تجريبية
    final slidingDesign = UpvcSlidingDesign(
      seriesId: 1,
      dalfaCount: UpvcDalfaCount.two,
      dalfaMethod: UpvcDalfaMethod.degrees45,
      dalfaWidthPlus: 2.0,
      dalfaHeightMinus: 1.5,
      skinehHeightMinus: 1.0,
      silkWidthPlus: 1.8,
      silkHeightMinus: 1.2,
      glassWidthMinus: 2.5,
      glassHeightMinus: 2.0,
      createdAt: now,
      updatedAt: now,
    );

    await db.insert(_slidingDesignsTable, slidingDesign.toMap());

    debugPrint('Sample uPVC design data inserted successfully');
  }

  Future<void> recreateDatabase() async {
    final db = await database;
    await db.delete(_hingeDesignsTable);
    await db.delete(_slidingDesignsTable);
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
