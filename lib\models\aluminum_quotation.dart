class AluminumQuotation {
  final int? id;
  final String quotationNumber;
  final DateTime quotationDate;
  final String clientName;
  final String clientPhone;
  final String clientAddress;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  AluminumQuotation({
    this.id,
    required this.quotationNumber,
    required this.quotationDate,
    required this.clientName,
    this.clientPhone = '',
    this.clientAddress = '',
    this.notes = '',
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quotation_number': quotationNumber,
      'quotation_date': quotationDate.millisecondsSinceEpoch,
      'client_name': clientName,
      'client_phone': clientPhone,
      'client_address': clientAddress,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory AluminumQuotation.fromMap(Map<String, dynamic> map) {
    return AluminumQuotation(
      id: map['id']?.toInt(),
      quotationNumber: map['quotation_number'] ?? '',
      quotationDate: DateTime.fromMillisecondsSinceEpoch(map['quotation_date']),
      clientName: map['client_name'] ?? '',
      clientPhone: map['client_phone'] ?? '',
      clientAddress: map['client_address'] ?? '',
      notes: map['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  AluminumQuotation copyWith({
    int? id,
    String? quotationNumber,
    DateTime? quotationDate,
    String? clientName,
    String? clientPhone,
    String? clientAddress,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AluminumQuotation(
      id: id ?? this.id,
      quotationNumber: quotationNumber ?? this.quotationNumber,
      quotationDate: quotationDate ?? this.quotationDate,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      clientAddress: clientAddress ?? this.clientAddress,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AluminumQuotation{id: $id, quotationNumber: $quotationNumber, clientName: $clientName, quotationDate: $quotationDate}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AluminumQuotation &&
        other.id == id &&
        other.quotationNumber == quotationNumber &&
        other.quotationDate == quotationDate &&
        other.clientName == clientName &&
        other.clientPhone == clientPhone &&
        other.clientAddress == clientAddress &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        quotationNumber.hashCode ^
        quotationDate.hashCode ^
        clientName.hashCode ^
        clientPhone.hashCode ^
        clientAddress.hashCode ^
        notes.hashCode;
  }
}
