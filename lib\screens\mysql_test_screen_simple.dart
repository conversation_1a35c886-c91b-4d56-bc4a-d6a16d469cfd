import 'package:flutter/material.dart';
import '../services/mysql_sync_service.dart';
import '../services/mysql_service.dart';

class MySQLTestScreen extends StatefulWidget {
  const MySQLTestScreen({super.key});

  @override
  State<MySQLTestScreen> createState() => _MySQLTestScreenState();
}

class _MySQLTestScreenState extends State<MySQLTestScreen> {
  String _connectionStatus = 'غير متصل';
  bool _isLoading = false;
  String _lastSyncResult = '';
  Map<String, dynamic>? _syncReport;
  final Color primaryColor = const Color(0xFF2196F3);

  @override
  void initState() {
    super.initState();
    _checkConnection();
  }

  Future<void> _checkConnection() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final connected = await MySQLService.instance.testConnection(useNewSettings: false);
      setState(() {
        _connectionStatus = connected ? 'متصل' : 'غير متصل';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _connectionStatus = 'خطأ في الاتصال: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSync() async {
    setState(() {
      _isLoading = true;
      _lastSyncResult = 'جاري المزامنة...';
    });

    try {
      final result = await MySQLSyncService.syncAllData();
      setState(() {
        _lastSyncResult = result['success'] 
            ? 'تمت المزامنة بنجاح' 
            : 'فشلت المزامنة: ${result['error']}';
        _syncReport = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _lastSyncResult = 'خطأ في المزامنة: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار مزامنة MySQL'),
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              primaryColor.withValues(alpha: 0.3),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // بطاقة حالة الاتصال
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        _connectionStatus == 'متصل' 
                            ? Icons.cloud_done 
                            : Icons.cloud_off,
                        size: 48,
                        color: _connectionStatus == 'متصل' 
                            ? Colors.green 
                            : Colors.red,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'حالة الاتصال: $_connectionStatus',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _checkConnection,
                        icon: const Icon(Icons.refresh),
                        label: const Text('اختبار الاتصال'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 20),

              // بطاقة عمليات المزامنة
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        'عمليات المزامنة',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      
                      // زر المزامنة الشاملة
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _testSync,
                        icon: const Icon(Icons.sync),
                        label: const Text('مزامنة شاملة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // تنبيه للمستخدم
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue[200]!),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[600]),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'للاختبار المخصص لمزامنة الألومنيوم، استخدم شاشة "اختبار مزامنة الألومنيوم" المنفصلة.',
                                style: TextStyle(color: Colors.blue[800]),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // بطاقة نتائج العمليات
              if (_lastSyncResult.isNotEmpty)
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'نتيجة آخر عملية:',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _lastSyncResult,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // بطاقة تفاصيل العمليات
              if (_syncReport != null)
                Card(
                  elevation: 4,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل العمليات:',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _formatSyncReport(_syncReport!),
                            style: const TextStyle(
                              fontSize: 14,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              if (_isLoading)
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatSyncReport(Map<String, dynamic> report) {
    final buffer = StringBuffer();
    buffer.writeln('الوقت: ${report['timestamp'] ?? 'غير محدد'}');
    buffer.writeln('النجاح: ${report['success'] ?? 'غير محدد'}');
    
    if (report['error'] != null) {
      buffer.writeln('الخطأ: ${report['error']}');
    }
    
    if (report['operations'] != null) {
      buffer.writeln('\nالعمليات:');
      final ops = report['operations'] as Map<String, dynamic>;
      ops.forEach((key, value) {
        if (value is Map) {
          buffer.writeln('  $key:');
          value.forEach((subKey, subValue) {
            buffer.writeln('    $subKey: $subValue');
          });
        } else {
          buffer.writeln('  $key: $value');
        }
      });
    }
    
    return buffer.toString();
  }
}
