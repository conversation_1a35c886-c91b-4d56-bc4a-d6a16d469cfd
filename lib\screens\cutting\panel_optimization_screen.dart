import 'package:flutter/material.dart';
import 'dart:math' show min, pi;
import '../../models/order_item.dart';
import '../../models/panel_cutting_models.dart';

class PanelOptimizationScreen extends StatefulWidget {
  final OrderItem orderItem;
  final List<PanelMeasurement> measurements;
  final double panelWidth;
  final double panelHeight;
  final double cuttingKerf;

  const PanelOptimizationScreen({
    super.key,
    required this.orderItem,
    required this.measurements,
    required this.panelWidth,
    required this.panelHeight,
    required this.cuttingKerf,
  });

  @override
  State<PanelOptimizationScreen> createState() => _PanelOptimizationScreenState();
}

class _PanelOptimizationScreenState extends State<PanelOptimizationScreen> {
  List<ScreenOptimizedPanel> _optimizedPanels = [];
  bool _isCalculating = false;
  double _totalWaste = 0.0;
  double _wastePercentage = 0.0;
  int _currentPanelIndex = 0;

  @override
  void initState() {
    super.initState();
    _calculateOptimization();
  }

  Future<void> _calculateOptimization() async {
    setState(() {
      _isCalculating = true;
    });

    // Simulate calculation delay for better UX
    await Future.delayed(const Duration(milliseconds: 500));

    final optimizedPanels = _optimizePanelCutting();

    setState(() {
      _optimizedPanels = optimizedPanels;
      _isCalculating = false;
      _calculateWasteStatistics();
    });
  }

  List<ScreenOptimizedPanel> _optimizePanelCutting() {
    final List<ScreenOptimizedPanel> panels = [];
    final List<PanelPiece> remainingPieces = [];

    // Convert measurements to panel pieces
    for (final measurement in widget.measurements) {
      for (int i = 0; i < measurement.quantity; i++) {
        remainingPieces.add(PanelPiece(
          width: measurement.pieceWidth,
          height: measurement.pieceHeight,
          type: measurement.type,
          number: measurement.number,
          id: '${measurement.type}_${measurement.number}_$i',
        ));
      }
    }

    // Sort pieces by area (largest first) for better optimization
    remainingPieces.sort((a, b) => (b.width * b.height).compareTo(a.width * a.height));

    int panelNumber = 1;
    while (remainingPieces.isNotEmpty) {
      final panel = ScreenOptimizedPanel(
        panelNumber: panelNumber,
        totalWidth: widget.panelWidth,
        totalHeight: widget.panelHeight,
        cuttingKerf: widget.cuttingKerf,
      );

      // Simple bin packing algorithm - first fit decreasing
      _packPiecesIntoPanel(panel, remainingPieces);

      if (panel.pieces.isNotEmpty) {
        panels.add(panel);
        panelNumber++;
      } else {
        // If no pieces fit, add the largest piece alone and continue
        if (remainingPieces.isNotEmpty) {
          final largePiece = remainingPieces.removeAt(0);
          panel.pieces.add(largePiece.copyWith(x: 0, y: 0));
          panels.add(panel);
          panelNumber++;
        }
      }
    }

    return panels;
  }

  void _packPiecesIntoPanel(ScreenOptimizedPanel panel, List<PanelPiece> remainingPieces) {
    final List<PanelPiece> piecesToRemove = [];

    for (final piece in remainingPieces) {
      final position = _findBestPosition(panel, piece);
      if (position != null) {
        panel.pieces.add(piece.copyWith(x: position.dx, y: position.dy));
        piecesToRemove.add(piece);
      }
    }

    // Remove fitted pieces from remaining list
    for (final piece in piecesToRemove) {
      remainingPieces.remove(piece);
    }

    // Calculate waste for this panel
    final usedArea = panel.pieces.fold<double>(
      0.0,
      (sum, piece) => sum + (piece.width * piece.height)
    );
    final totalArea = widget.panelWidth * widget.panelHeight;
    panel.wasteArea = totalArea - usedArea;
  }

  Offset? _findBestPosition(ScreenOptimizedPanel panel, PanelPiece piece) {
    // Use a more precise increment for better panel utilization
    // Start at (0,0) and use a step of 1 unit for better precision
    const increment = 1.0;

    // Try to find the best position with top-left gravity (better for visualization)
    double bestX = -1;
    double bestY = -1;
    double minY = double.infinity;
    double minX = double.infinity;

    for (double y = 0; y <= widget.panelHeight - piece.height; y += increment) {
      for (double x = 0; x <= widget.panelWidth - piece.width; x += increment) {
        if (_canPlacePieceAt(panel, piece, x, y)) {
          // Prefer positions closer to the top-left corner
          if (y < minY || (y == minY && x < minX)) {
            minY = y;
            minX = x;
            bestX = x;
            bestY = y;
          }

          // Early exit for the first row if we find a position
          if (y < increment * 2 && bestX != -1) {
            return Offset(bestX, bestY);
          }
        }
      }
    }

    return bestX != -1 ? Offset(bestX, bestY) : null;
  }

  bool _canPlacePieceAt(ScreenOptimizedPanel panel, PanelPiece piece, double x, double y) {
    // Check if piece fits within panel bounds
    if (x + piece.width > widget.panelWidth || y + piece.height > widget.panelHeight) {
      return false;
    }

    // Check for overlap with existing pieces
    for (final existingPiece in panel.pieces) {
      if (_piecesOverlap(
        x, y, piece.width, piece.height,
        existingPiece.x, existingPiece.y, existingPiece.width, existingPiece.height,
      )) {
        return false;
      }
    }

    return true;
  }

  bool _piecesOverlap(double x1, double y1, double w1, double h1,
                     double x2, double y2, double w2, double h2) {
    return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1);
  }

  void _calculateWasteStatistics() {
    if (_optimizedPanels.isEmpty) return;

    final totalPanelArea = _optimizedPanels.length * widget.panelWidth * widget.panelHeight;
    _totalWaste = _optimizedPanels.fold<double>(
      0.0,
      (sum, panel) => sum + panel.wasteArea
    );
    _wastePercentage = (_totalWaste / totalPanelArea) * 100;
  }

  Future<void> _shareResults() async {
    // TODO: Implement sharing functionality
    // This could include generating a PDF report, sharing as text, or saving to gallery
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة المشاركة قريباً'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تحسين تقطيع الألواح - ${widget.orderItem.itemName}'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          if (!_isCalculating && _optimizedPanels.isNotEmpty) ...[
            IconButton(
              onPressed: _shareResults,
              icon: const Icon(Icons.share),
              tooltip: 'مشاركة النتائج',
            ),
            IconButton(
              onPressed: _calculateOptimization,
              icon: const Icon(Icons.refresh),
              tooltip: 'إعادة الحساب',
            ),
          ],
        ],
      ),
      body: _isCalculating
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _calculateOptimization,
              child: _buildOptimizationResults(),
            ),
    );
  }
  Widget _buildOptimizationResults() {
    if (_optimizedPanels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.dashboard,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد قطع للتحسين',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Statistics header
        _buildStatisticsHeader(),

        // Panel navigation (only show if more than one panel)
        if (_optimizedPanels.length > 1) _buildPanelNavigation(),

        // Current panel visualization
        Expanded(
          child: _buildCurrentPanelView(),
        ),
      ],
    );
  }Widget _buildStatisticsHeader() {
    final isMobile = MediaQuery.of(context).size.width < 600;

    if (isMobile) {
      // عرض في صفين للهواتف المحمولة
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.orange[50]!, Colors.orange[100]!],
          ),
          border: Border(bottom: BorderSide(color: Colors.orange[200]!)),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(child: _buildEnhancedStatCard('عدد الألواح', '${_optimizedPanels.length}', Icons.dashboard, Colors.blue)),
                const SizedBox(width: 12),
                Expanded(child: _buildEnhancedStatCard('إجمالي الهدر', '${_totalWaste.toStringAsFixed(1)} سم²', Icons.delete, Colors.red)),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(child: _buildEnhancedStatCard('نسبة الهدر', '${_wastePercentage.toStringAsFixed(1)}%', Icons.pie_chart, Colors.orange)),
                const SizedBox(width: 12),
                Expanded(child: _buildEnhancedStatCard('الكفاءة', '${(100 - _wastePercentage).toStringAsFixed(1)}%', Icons.eco, Colors.green)),
              ],
            ),
          ],
        ),
      );
    } else {
      // عرض في صف واحد للأجهزة الأكبر
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.orange[50]!, Colors.orange[100]!],
          ),
          border: Border(bottom: BorderSide(color: Colors.orange[200]!)),
        ),
        child: Row(
          children: [
            Expanded(child: _buildEnhancedStatCard('عدد الألواح', '${_optimizedPanels.length}', Icons.dashboard, Colors.blue)),
            const SizedBox(width: 16),
            Expanded(child: _buildEnhancedStatCard('إجمالي الهدر', '${_totalWaste.toStringAsFixed(1)} سم²', Icons.delete, Colors.red)),
            const SizedBox(width: 16),
            Expanded(child: _buildEnhancedStatCard('نسبة الهدر', '${_wastePercentage.toStringAsFixed(1)}%', Icons.pie_chart, Colors.orange)),
            const SizedBox(width: 16),
            Expanded(child: _buildEnhancedStatCard('الكفاءة', '${(100 - _wastePercentage).toStringAsFixed(1)}%', Icons.eco, Colors.green)),
          ],
        ),
      );
    }
  }

  Widget _buildEnhancedStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  Widget _buildPanelNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [Colors.orange[50]!, Colors.orange[100]!],
        ),
        border: Border(bottom: BorderSide(color: Colors.orange[200]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // First and Previous buttons
          Row(
            children: [
              _buildNavButton(
                icon: Icons.first_page,
                tooltip: 'الأول',
                onPressed: _currentPanelIndex > 0 ? _goToFirstPanel : null,
              ),
              const SizedBox(width: 8),
              _buildNavButton(
                icon: Icons.chevron_right,
                tooltip: 'السابق',
                onPressed: _currentPanelIndex > 0 ? _goToPreviousPanel : null,
              ),
            ],
          ),

          // Current panel info with enhanced styling
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.orange[100]!, Colors.orange[200]!],
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.orange[300]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.dashboard, color: Colors.orange[700], size: 18),
                const SizedBox(width: 8),
                Text(
                  'لوح ${_currentPanelIndex + 1} من ${_optimizedPanels.length}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.orange[800],
                  ),
                ),
              ],
            ),
          ),

          // Next and Last buttons
          Row(
            children: [
              _buildNavButton(
                icon: Icons.chevron_left,
                tooltip: 'التالي',
                onPressed: _currentPanelIndex < _optimizedPanels.length - 1 ? _goToNextPanel : null,
              ),
              const SizedBox(width: 8),
              _buildNavButton(
                icon: Icons.last_page,
                tooltip: 'الأخير',
                onPressed: _currentPanelIndex < _optimizedPanels.length - 1 ? _goToLastPanel : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback? onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: onPressed != null
          ? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.orange[50]!],
            )
          : null,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: onPressed != null ? Colors.orange[300]! : Colors.grey[300]!,
        ),
        boxShadow: onPressed != null ? [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: onPressed != null ? Colors.orange[700] : Colors.grey[400],
        ),
        tooltip: tooltip,
        splashRadius: 20,
      ),
    );
  }
  Widget _buildCurrentPanelView() {
    final currentPanel = _optimizedPanels[_currentPanelIndex];
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Enhanced panel info card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.orange[50]!, Colors.orange[100]!],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: isMobile
              ? Column(
                  children: [
                    _buildPanelInfoRow('الأبعاد', '${widget.panelWidth} × ${widget.panelHeight} سم', Icons.straighten),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(child: _buildPanelInfoRow('عدد القطع', '${currentPanel.pieces.length}', Icons.grid_on)),
                        const SizedBox(width: 16),
                        Expanded(child: _buildPanelInfoRow('الهدر', '${currentPanel.wasteArea.toStringAsFixed(1)} سم²', Icons.delete)),
                      ],
                    ),
                  ],
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildPanelInfoRow('الأبعاد', '${widget.panelWidth} × ${widget.panelHeight} سم', Icons.straighten),
                    _buildPanelInfoRow('عدد القطع', '${currentPanel.pieces.length}', Icons.grid_on),
                    _buildPanelInfoRow('الهدر', '${currentPanel.wasteArea.toStringAsFixed(1)} سم²', Icons.delete),
                  ],
                ),
          ),

          const SizedBox(height: 16),

          // Panel visualization
          Expanded(
            child: isMobile
              ? Column(
                  children: [
                    // Panel visualization
                    Expanded(
                      flex: 3,
                      child: _buildPanelVisualization(currentPanel),
                    ),
                    const SizedBox(height: 16),
                    // Pieces list for mobile
                    Expanded(
                      flex: 2,
                      child: _buildMobilePiecesList(currentPanel),
                    ),
                  ],
                )
              : _buildPanelVisualization(currentPanel),
          ),
        ],
      ),
    );
  }

  Widget _buildPanelInfoRow(String label, String value, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: Colors.orange[700], size: 18),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPanelVisualization(ScreenOptimizedPanel currentPanel) {
    return Center(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.orange[300]!, width: 2),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            top: 30.0,
            left: 55.0,
            right: 10.0,
            bottom: 10.0,
          ),
          child: AspectRatio(
            aspectRatio: widget.panelWidth / widget.panelHeight,
            child: CustomPaint(
              painter: PanelPainter(
                panel: currentPanel,
                panelWidth: widget.panelWidth,
                panelHeight: widget.panelHeight,
              ),
              child: Container(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobilePiecesList(ScreenOptimizedPanel currentPanel) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list, color: Colors.orange[700], size: 20),
              const SizedBox(width: 8),
              Text(
                'قائمة القطع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: currentPanel.pieces.length,
              itemBuilder: (context, index) {
                final piece = currentPanel.pieces[index];
                final pieceColors = [
                  Colors.blue,
                  Colors.green,
                  Colors.purple,
                  Colors.teal,
                  Colors.amber,
                  Colors.pink,
                  Colors.indigo,
                  Colors.cyan,
                ];
                final color = pieceColors[index % pieceColors.length];

                return Container(
                  margin: const EdgeInsets.only(bottom: 6),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: color.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${piece.type != '-' ? piece.type : ''} ${piece.number != '-' ? piece.number : ''}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Text(
                        '${piece.width.toInt()} × ${piece.height.toInt()}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _goToFirstPanel() {
    setState(() {
      _currentPanelIndex = 0;
    });
  }

  void _goToPreviousPanel() {
    setState(() {
      _currentPanelIndex = (_currentPanelIndex - 1).clamp(0, _optimizedPanels.length - 1);
    });
  }

  void _goToNextPanel() {
    setState(() {
      _currentPanelIndex = (_currentPanelIndex + 1).clamp(0, _optimizedPanels.length - 1);
    });
  }

  void _goToLastPanel() {
    setState(() {
      _currentPanelIndex = _optimizedPanels.length - 1;
    });
  }
}

// Enhanced custom painter for panel visualization
class PanelPainter extends CustomPainter {
  final ScreenOptimizedPanel panel;
  final double panelWidth;
  final double panelHeight;
  final TextStyle style;
  final bool isRotated;

  PanelPainter({
    required this.panel,
    required this.panelWidth,
    required this.panelHeight,
    this.style = const TextStyle(
      color: Colors.black,
      fontSize: 14,
      fontWeight: FontWeight.bold,
      backgroundColor: Colors.white,
    ),
    this.isRotated = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Scale factors for converting panel dimensions to screen dimensions
    final scaleX = size.width / panelWidth;
    final scaleY = size.height / panelHeight;

    // Draw panel background with subtle grid pattern
    _drawPanelBackground(canvas, size);

    // Draw pieces with enhanced styling
    _drawPanelPieces(canvas, size, scaleX, scaleY);

    // Draw measurements
    _drawMeasurements(canvas, size, scaleX, scaleY);

    // Draw panel border
    _drawPanelBorder(canvas, size);
  }

  void _drawPanelBackground(Canvas canvas, Size size) {
    // Draw subtle grid pattern
    final gridPaint = Paint()
      ..color = Colors.grey[200]!
      ..strokeWidth = 0.5;

    // Draw vertical grid lines
    for (double x = 0; x <= size.width; x += size.width / 20) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // Draw horizontal grid lines
    for (double y = 0; y <= size.height; y += size.height / 20) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  void _drawPanelBorder(Canvas canvas, Size size) {
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..color = Colors.orange[400]!;

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      borderPaint,
    );
  }

  void _drawMeasurements(Canvas canvas, Size size, double scaleX, double scaleY) {
    // Draw panel dimensions
    _drawDimensionText(
      canvas: canvas,
      text: '${panelWidth.toStringAsFixed(0)} cm',
      offset: Offset(size.width / 2, -25),
      style: style,
      isRotated: false,
    );

    _drawDimensionText(
      canvas: canvas,
      text: '${panelHeight.toStringAsFixed(0)} cm',
      offset: Offset(-25, size.height / 2),
      style: style,
      isRotated: true,
    );
  }

  void _drawDimensionText({
    required Canvas canvas,
    required String text,
    required Offset offset,
    required TextStyle style,
    required bool isRotated,
  }) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();

    canvas.save();
    if (isRotated) {
      canvas.translate(offset.dx, offset.dy);
      canvas.rotate(-pi / 2);
      textPainter.paint(canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
    } else {
      canvas.translate(offset.dx, offset.dy);
      textPainter.paint(canvas, Offset(-textPainter.width / 2, 0));
    }
    canvas.restore();
  }

  void _drawPanelPieces(Canvas canvas, Size size, double scaleX, double scaleY) {
    final piecePaint = Paint()..style = PaintingStyle.fill;

    for (int i = 0; i < panel.pieces.length; i++) {
      final piece = panel.pieces[i];

      // Set color based on piece index
      piecePaint.color = _getPieceColor(i);

      final pieceRect = Rect.fromLTWH(
        piece.x * scaleX,
        piece.y * scaleY,
        piece.width * scaleX,
        piece.height * scaleY,
      );

      canvas.drawRect(pieceRect, piecePaint);

      // Draw piece border
      final pieceBorder = Paint()
        ..style = PaintingStyle.stroke
        ..color = Colors.black54
        ..strokeWidth = 1;
      canvas.drawRect(pieceRect, pieceBorder);
    }
  }

  Color _getPieceColor(int index) {
    final colors = [
      Colors.blue[100]!,
      Colors.green[100]!,
      Colors.orange[100]!,
      Colors.purple[100]!,
      Colors.teal[100]!,
      Colors.red[100]!,
      Colors.indigo[100]!,
      Colors.amber[100]!,
    ];
    return colors[index % colors.length];
  }

  // ignore: unused_element
  void _drawWasteAreas(Canvas canvas, Size size, double scaleX, double scaleY) {
    // Calculate waste areas (areas not covered by pieces)
    final wasteRegions = _calculateWasteRegions(size, scaleX, scaleY);

    for (final region in wasteRegions) {
      // Draw diagonal lines in waste areas
      final wastePaint = Paint()
        ..color = Colors.red.withValues(alpha: 0.3)
        ..strokeWidth = 1.5;

      canvas.save();
      canvas.clipRRect(RRect.fromRectAndRadius(region, const Radius.circular(2)));

      // Draw diagonal lines
      for (double i = -region.height; i < region.width + region.height; i += 8) {
        canvas.drawLine(
          Offset(region.left + i, region.top),
          Offset(region.left + i + region.height, region.bottom),
          wastePaint,
        );
      }

      canvas.restore();
    }
  }
  List<Rect> _calculateWasteRegions(Size size, double scaleX, double scaleY) {
    final List<Rect> wasteRegions = [];

    // Create a simplified waste calculation based on free corners and edges
    // This is a compromise between accuracy and performance

    // First, try to identify large rectangular regions with no pieces
    double gridStep = 20.0; // A reasonable grid size for checking waste areas

    for (double y = 0; y <= size.height - gridStep; y += gridStep) {
      for (double x = 0; x <= size.width - gridStep; x += gridStep) {
        // Check if this point (x,y) can be the top-left of a waste region
        double maxWidth = size.width - x;
        double maxHeight = size.height - y;

        // Try to expand the rectangle as much as possible
        boolean: {
          for (final piece in panel.pieces) {
            double pieceX = piece.x * scaleX;
            double pieceY = piece.y * scaleY;
            double pieceRight = pieceX + (piece.width * scaleX);
            double pieceBottom = pieceY + (piece.height * scaleY);

            // If the piece overlaps with our current point, skip this point
            if (x >= pieceX && x < pieceRight && y >= pieceY && y < pieceBottom) {
              break boolean;
            }

            // Limit the width if a piece is to the right
            if (pieceY <= y && pieceBottom > y && pieceX > x) {
              maxWidth = min(maxWidth, pieceX - x);
            }

            // Limit the height if a piece is below
            if (pieceX <= x && pieceRight > x && pieceY > y) {
              maxHeight = min(maxHeight, pieceY - y);
            }
          }

          // If we have a meaningful waste region, add it
          if (maxWidth >= gridStep && maxHeight >= gridStep) {
            final wasteRect = Rect.fromLTWH(x, y, maxWidth, maxHeight);

            // Only add if it's a significant area
            if (wasteRect.width * wasteRect.height > size.width * size.height * 0.01) {
              wasteRegions.add(wasteRect);
            }
          }
        }
      }
    }

    return wasteRegions;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Define the PanelPiece class in this file since it's not imported from elsewhere
class PanelPiece {
  final double width;
  final double height;
  final String type;
  final String number;
  final String id;
  final double x;
  final double y;

  PanelPiece({
    required this.width,
    required this.height,
    required this.type,
    required this.number,
    required this.id,
    this.x = 0.0,
    this.y = 0.0,
  });

  PanelPiece copyWith({
    double? width,
    double? height,
    String? type,
    String? number,
    String? id,
    double? x,
    double? y,
  }) {
    return PanelPiece(
      width: width ?? this.width,
      height: height ?? this.height,
      type: type ?? this.type,
      number: number ?? this.number,
      id: id ?? this.id,
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }
}

// Define a separate internal OptimizedPanel class for this screen
// to avoid conflicts with the model class
class ScreenOptimizedPanel {
  final int panelNumber;
  final double totalWidth;
  final double totalHeight;
  final double cuttingKerf;
  final List<PanelPiece> pieces = [];
  double wasteArea = 0.0;

  ScreenOptimizedPanel({
    required this.panelNumber,
    required this.totalWidth,
    required this.totalHeight,
    required this.cuttingKerf,
  });
}
