// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:uptime_smart_assist/main.dart';

void main() {
  testWidgets('Smart Assistant App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SmartAssistantApp());

    // Verify that the splash screen is displayed
    expect(find.text('المساعد الذكي'), findsOneWidget);
    expect(find.text('لخدمات المطابخ والألومنيوم'), findsOneWidget);

    // Wait for the timer to complete and navigate to home
    await tester.pumpAndSettle(const Duration(seconds: 4));

    // Verify navigation to home screen
    expect(find.text('الخدمات المتاحة'), findsOneWidget);
  });
}
