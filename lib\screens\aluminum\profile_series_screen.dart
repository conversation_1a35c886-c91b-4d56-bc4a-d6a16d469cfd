import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/profile_series.dart';
import '../../models/aluminum_profile.dart';
import '../../services/profile_series_service.dart';
import 'profile_categories_screen.dart';

class ProfileSeriesScreen extends StatefulWidget {
  final ProfileType profileType;

  const ProfileSeriesScreen({
    super.key,
    required this.profileType,
  });

  @override
  State<ProfileSeriesScreen> createState() => _ProfileSeriesScreenState();
}

class _ProfileSeriesScreenState extends State<ProfileSeriesScreen> {
  final ProfileSeriesService _seriesService = ProfileSeriesService();
  List<ProfileSeries> _series = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSeries();
    _initializeSampleData();
  }

  Future<void> _initializeSampleData() async {
    await _seriesService.insertSampleData();
  }

  Future<void> _loadSeries() async {
    setState(() => _isLoading = true);
    try {
      final series = await _seriesService.getSeriesByType(widget.profileType);
      setState(() {
        _series = series;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل مجموعات القطاعات: $e')),
        );
      }
    }
  }

  Future<void> _showAddEditDialog({ProfileSeries? series}) async {
    final result = await showDialog<ProfileSeries>(
      context: context,
      builder: (context) => _AddEditSeriesDialog(
        profileType: widget.profileType,
        series: series,
      ),
    );

    if (result != null) {
      try {
        if (series == null) {
          await _seriesService.insertSeries(result);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم حفظ المجموعة بنجاح')),
            );
          }
        } else {
          await _seriesService.updateSeries(result);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم تحديث المجموعة بنجاح')),
            );
          }
        }
        _loadSeries();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حفظ المجموعة: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.profileType.getLocalizedName(Localizations.localeOf(context).languageCode)} - مجموعات القطاعات',
        ),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _showAddEditDialog(),
            icon: const Icon(Icons.add),
            tooltip: 'إضافة مجموعة جديدة',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _series.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد مجموعات قطاعات',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _showAddEditDialog(),
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة مجموعة جديدة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF607D8B),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildSeriesGrid(isTablet),
      ),
    );
  }

  Widget _buildSeriesGrid(bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isTablet ? 3 : 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: isTablet ? 1.2 : 1.0,
        ),
        itemCount: _series.length,
        itemBuilder: (context, index) {
          final series = _series[index];
          return _buildSeriesCard(series);
        },
      ),
    );
  }

  Widget _buildSeriesCard(ProfileSeries series) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProfileCategoriesScreen(
                profileType: widget.profileType,
                series: series,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                const Color(0xFF607D8B).withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF607D8B).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.profileType == ProfileType.hinge
                      ? Icons.door_front_door
                      : Icons.door_sliding,
                  size: 32,
                  color: const Color(0xFF607D8B),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                series.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF607D8B),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                series.code,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (series.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  series.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    onPressed: () => _showAddEditDialog(series: series),
                    icon: const Icon(Icons.edit, color: Colors.blue, size: 20),
                    tooltip: 'تعديل',
                  ),
                  IconButton(
                    onPressed: () => _deleteSeries(series),
                    icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _deleteSeries(ProfileSeries series) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مجموعة "${series.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _seriesService.deleteSeries(series.id!);
        _loadSeries();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المجموعة بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف المجموعة: $e')),
          );
        }
      }
    }
  }
}

class _AddEditSeriesDialog extends StatefulWidget {
  final ProfileType profileType;
  final ProfileSeries? series;

  const _AddEditSeriesDialog({
    required this.profileType,
    this.series,
  });

  @override
  State<_AddEditSeriesDialog> createState() => _AddEditSeriesDialogState();
}

class _AddEditSeriesDialogState extends State<_AddEditSeriesDialog> {
  final _formKey = GlobalKey<FormState>();
  final ProfileSeriesService _seriesService = ProfileSeriesService();

  late TextEditingController _nameController;
  late TextEditingController _codeController;
  late TextEditingController _descriptionController;

  bool get _isEditing => widget.series != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (!_isEditing) {
      _generateCode();
    }
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.series?.name ?? '');
    _codeController = TextEditingController(text: widget.series?.code ?? '');
    _descriptionController = TextEditingController(text: widget.series?.description ?? '');
  }

  Future<void> _generateCode() async {
    try {
      final code = await _seriesService.generateSeriesCode(widget.profileType);
      setState(() {
        _codeController.text = code;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _saveSeries() {
    if (!_formKey.currentState!.validate()) return;

    final now = DateTime.now();
    final series = ProfileSeries(
      id: widget.series?.id,
      name: _nameController.text.trim(),
      code: _codeController.text.trim(),
      type: widget.profileType,
      description: _descriptionController.text.trim(),
      createdAt: widget.series?.createdAt ?? now,
      updatedAt: now,
    );

    Navigator.pop(context, series);
  }

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    final localizations = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(_isEditing ? 'تعديل المجموعة' : 'إضافة مجموعة جديدة'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المجموعة',
                  border: OutlineInputBorder(),
                  hintText: 'مثل: سوناتا 45',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم المجموعة مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _codeController,
                decoration: const InputDecoration(
                  labelText: 'كود المجموعة',
                  border: OutlineInputBorder(),
                  hintText: 'مثل: SON45',
                ),
                readOnly: _isEditing,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'كود المجموعة مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المجموعة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(localizations.cancel),
        ),
        ElevatedButton(
          onPressed: _saveSeries,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF607D8B),
            foregroundColor: Colors.white,
          ),
          child: Text(_isEditing ? localizations.update : localizations.save),
        ),
      ],
    );
  }
}
