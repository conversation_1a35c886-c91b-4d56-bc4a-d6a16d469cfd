
class Treasury {
  final int? id;
  final String name;
  final double previousBalance;
  final double currentBalance;
  final DateTime date;
  final DateTime createdAt;

  Treasury({
    this.id,
    required this.name,
    required this.previousBalance,
    required this.currentBalance,
    required this.date,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Treasury copyWith({
    int? id,
    String? name,
    double? previousBalance,
    double? currentBalance,
    DateTime? date,
    DateTime? createdAt,
  }) {
    return Treasury(
      id: id ?? this.id,
      name: name ?? this.name,
      previousBalance: previousBalance ?? this.previousBalance,
      currentBalance: currentBalance ?? this.currentBalance,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'previous_balance': previousBalance,
      'current_balance': currentBalance,
      'date': date.toIso8601String(),
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Treasury.fromMap(Map<String, dynamic> map) {
    return Treasury(
      id: map['id'] as int?,
      name: map['name'] as String,
      previousBalance: map['previous_balance'] as double,
      currentBalance: map['current_balance'] as double,
      date: DateTime.parse(map['date'] as String),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
    );
  }

    double get finalBalance => previousBalance + currentBalance;
}
