import 'package:flutter/foundation.dart';
import '../services/mysql_service.dart';
import '../services/unified_aluminum_service.dart';
import '../models/aluminum_profile.dart';
import '../models/profile_series.dart';

/// خدمة استيراد قطاعات الألومنيوم من قاعدة البيانات الأونلاين
class AluminumImportService {
  static final MySQLService _mysql = MySQLService.instance;
  static final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  /// الحصول على مجموعات القطاعات من قاعدة البيانات الأونلاين
  static Future<Map<String, dynamic>> getOnlineProfileSeries(ProfileType type) async {
    try {
      if (!await _mysql.connect()) {
        return {
          'success': false,
          'error': 'فشل الاتصال بقاعدة البيانات الأونلاين'
        };
      }

      // البحث عن المجموعات المتاحة من جدول profile_series
      final seriesData = await _mysql.select(
        'SELECT id, code, name, type, description FROM profile_series WHERE type = ? ORDER BY name',
        [type.key]
      );

      final series = <ProfileSeries>[];
      for (final data in seriesData) {
        try {
          series.add(ProfileSeries.fromMap(data));
        } catch (e) {
          debugPrint('خطأ في تحويل مجموعة القطاعات: $e');
          debugPrint('البيانات: $data');
          // تجاهل هذه المجموعة والمتابعة
        }
      }

      return {
        'success': true,
        'series': series,
        'count': series.length
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في جلب مجموعات القطاعات: $e'
      };
    }
  }

  /// الحصول على القطاعات لمجموعة معينة من قاعدة البيانات الأونلاين
  static Future<Map<String, dynamic>> getOnlineProfilesBySeries(dynamic seriesIdentifier) async {
    try {
      if (!await _mysql.connect()) {
        return {
          'success': false,
          'error': 'فشل الاتصال بقاعدة البيانات الأونلاين'
        };
      }

      final profilesData = await _mysql.select(
        'SELECT * FROM aluminum_profiles WHERE series_id = ?  ORDER BY name',
        [seriesIdentifier]
      );

      final profiles = <AluminumProfile>[];
      for (final data in profilesData) {
        try {
          profiles.add(AluminumProfile.fromMap(data));
        } catch (e) {
          debugPrint('خطأ في تحويل قطاع الألومنيوم: $e');
          debugPrint('البيانات: $data');
          // تجاهل هذا القطاع والمتابعة
        }
      }

      return {
        'success': true,
        'profiles': profiles,
        'count': profiles.length
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في جلب القطاعات: $e'
      };
    }
  }

  /// استيراد مجموعة قطاعات مع قطاعاتها
  static Future<Map<String, dynamic>> importSeriesWithProfiles(
    ProfileSeries series,
    Function(String message, int current, int total)? onProgress,
    {String? userChoice} // خيار المستخدم للتعامل مع المجموعة الموجودة
  ) async {
    try {
      int importedProfiles = 0;
      int totalProfiles = 0;
      List<String> errors = [];

      // تحديث التقدم - بدء الاستيراد
      onProgress?.call('بدء استيراد مجموعة ${series.name}...', 0, 100);

      // 1. استيراد المجموعة أولاً
      try {
        // التحقق من وجود المجموعة محلياً
        final existingSeries = await _aluminumService.getSeriesByCode(series.code);
        int localSeriesId;

        if (existingSeries != null) {
          // إذا لم يتم تحديد خيار المستخدم، إرجاع معلومات المجموعة الموجودة
          if (userChoice == null) {
            return {
              'success': false,
              'requiresUserChoice': true,
              'existingSeries': existingSeries,
              'onlineSeries': series,
              'message': 'تم العثور على مجموعة موجودة تتطلب اختيار المستخدم'
            };
          }
          
          if (userChoice == 'replace') {
            // مسح المجموعة والقطاعات الموجودة
            await _aluminumService.deleteSeriesWithProfiles(existingSeries.id!);
            // إدراج مجموعة جديدة
            localSeriesId = await _aluminumService.insertSeries(series.toMap());
            onProgress?.call('تم مسح المجموعة القديمة وإدراج الجديدة', 10, 100);
          } else if (userChoice == 'update') {
            // تحديث المجموعة الموجودة
            final updatedSeries = ProfileSeries(
              id: existingSeries.id,
              name: series.name,
              code: series.code,
              type: series.type,
              description: series.description,
              imagePath: series.imagePath,
              isActive: series.isActive,
              createdAt: existingSeries.createdAt,
              updatedAt: DateTime.now(),
            );
            await _aluminumService.updateProfileSeries(updatedSeries);
            localSeriesId = existingSeries.id!;
            onProgress?.call('تم تحديث مجموعة ${series.name}', 10, 100);
          } else {
            return {
              'success': false,
              'error': 'خيار غير صحيح للتعامل مع المجموعة الموجودة'
            };
          }
        } else {
          // إدراج مجموعة جديدة
          localSeriesId = await _aluminumService.insertSeries(series.toMap());
          onProgress?.call('تم إدراج مجموعة ${series.name}', 10, 100);
        }

        // 2. جلب القطاعات من قاعدة البيانات الأونلاين
        onProgress?.call('جلب قطاعات المجموعة...', 20, 100);
        
        final profilesResult = await getOnlineProfilesBySeries(series.id!);
        if (!profilesResult['success']) {
          return {
            'success': false,
            'error': profilesResult['error']
          };
        }

        final profilesList = profilesResult['profiles'] as List;
        final profiles = profilesList.cast<AluminumProfile>();
        totalProfiles = profiles.length;

        if (totalProfiles == 0) {
          return {
            'success': true,
            'importedSeries': 1,
            'importedProfiles': 0,
            'totalProfiles': 0,
            'errors': [],
            'message': 'تم استيراد المجموعة بنجاح ولكن لا توجد قطاعات'
          };
        }

        // 3. استيراد القطاعات
        for (int i = 0; i < profiles.length; i++) {
          final profile = profiles[i];
          try {
            // تحديث معرف المجموعة للقطاع المحلي
            final updatedProfile = AluminumProfile(
              id: null, // سيتم توليد معرف جديد
              name: profile.name,
              code: profile.code,
              type: profile.type,
              category: profile.category,
              seriesId: localSeriesId, // استخدام معرف المجموعة المحلي
              width: profile.width,
              height: profile.height,
              thickness: profile.thickness,
              weight: profile.weight,
              color: profile.color,
              description: profile.description,
              lipType: profile.lipType,
              lipThickness: profile.lipThickness,
              withBaketa: profile.withBaketa,
              withDalfa: profile.withDalfa,
              imagePath: profile.imagePath,
              pricePerMeter: profile.pricePerMeter,
              isActive: profile.isActive,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            // التحقق من وجود القطاع محلياً
            final existingProfile = await _aluminumService.getProfileByCode(profile.code);
            
            if (existingProfile != null) {
              // تحديث القطاع الموجود
              await _aluminumService.updateProfileById(existingProfile.id!, updatedProfile.toMap());
            } else {
              // إدراج قطاع جديد
              await _aluminumService.insertAluminumProfile(updatedProfile);
            }

            importedProfiles++;
            
            // تحديث التقدم
            final progress = 20 + ((i + 1) / totalProfiles * 70).round();
            onProgress?.call(
              'تم استيراد ${i + 1} من $totalProfiles قطاع',
              progress,
              100
            );

          } catch (e) {
            errors.add('خطأ في استيراد قطاع ${profile.name}: $e');
            debugPrint('خطأ في استيراد قطاع ${profile.name}: $e');
          }
        }

        onProgress?.call('تم الانتهاء من الاستيراد', 100, 100);

        return {
          'success': true,
          'importedSeries': 1,
          'importedProfiles': importedProfiles,
          'totalProfiles': totalProfiles,
          'errors': errors,
          'message': 'تم استيراد $importedProfiles من $totalProfiles قطاع بنجاح'
        };

      } catch (e) {
        return {
          'success': false,
          'error': 'خطأ في استيراد المجموعة: $e'
        };
      }

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ عام في الاستيراد: $e'
      };
    }
  }

  /// استيراد عدة مجموعات مع قطاعاتها
  static Future<Map<String, dynamic>> importMultipleSeries(
    List<ProfileSeries> seriesList,
    Function(String message, int current, int total)? onProgress,
    {Map<String, String>? userChoices} // خيارات المستخدم للمجموعات الموجودة
  ) async {
    try {
      int totalImportedSeries = 0;
      int totalImportedProfiles = 0;
      int totalProfiles = 0;
      List<String> allErrors = [];

      for (int i = 0; i < seriesList.length; i++) {
        final series = seriesList[i];
        
        onProgress?.call(
          'استيراد مجموعة ${i + 1} من ${seriesList.length}: ${series.name}',
          (i / seriesList.length * 100).round(),
          100
        );

        final result = await importSeriesWithProfiles(
          series,
          (message, current, total) {
            // تحديث التقدم الفرعي
            final overallProgress = (i / seriesList.length * 100).round();
            onProgress?.call(message, overallProgress, 100);
          },
          userChoice: userChoices?[series.code]
        );

        if (result['success']) {
          totalImportedSeries += result['importedSeries'] as int;
          totalImportedProfiles += result['importedProfiles'] as int;
          totalProfiles += result['totalProfiles'] as int;
          
          final errorsList = result['errors'] as List;
          final errors = errorsList.cast<String>();
          allErrors.addAll(errors);
        } else {
          allErrors.add('فشل استيراد مجموعة ${series.name}: ${result['error']}');
        }
      }

      onProgress?.call('تم الانتهاء من استيراد جميع المجموعات', 100, 100);

      return {
        'success': true,
        'importedSeries': totalImportedSeries,
        'importedProfiles': totalImportedProfiles,
        'totalProfiles': totalProfiles,
        'errors': allErrors,
        'message': 'تم استيراد $totalImportedSeries مجموعة و $totalImportedProfiles قطاع بنجاح'
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في استيراد المجموعات: $e'
      };
    }
  }

  /// اختبار الاتصال بقاعدة البيانات الأونلاين
  static Future<Map<String, dynamic>> testConnection() async {
    try {
      if (!await _mysql.connect()) {
        return {
          'success': false,
          'error': 'فشل الاتصال بقاعدة البيانات الأونلاين'
        };
      }

      // اختبار بسيط للتأكد من وجود الجداول
      final tables = await _mysql.select('SHOW TABLES');

      // التحقق من وجود جدول aluminum_profiles
      bool hasAluminumTable = tables.any((table) =>
        table.values.first.toString().toLowerCase() == 'aluminum_profiles');

      if (!hasAluminumTable) {
        return {
          'success': false,
          'error': 'جدول aluminum_profiles غير موجود في قاعدة البيانات'
        };
      }

      return {
        'success': true,
        'message': 'تم الاتصال بقاعدة البيانات الأونلاين بنجاح',
        'tables_count': tables.length
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في الاتصال: $e'
      };
    }
  }
}
