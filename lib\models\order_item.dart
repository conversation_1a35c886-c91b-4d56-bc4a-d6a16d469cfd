class OrderItem {
  final int? id;
  final int projectId;
  final String itemNumber;
  final String itemName;
  final String itemType; // 'sticks' or 'boards'
  final int requiredBoards;
  final double unitPrice;
  final double totalAmount;
  final double discountPercent;
  final double discountAmount;
  final double finalAmount;
  final DateTime createdAt;

  OrderItem({
    this.id,
    required this.projectId,
    required this.itemNumber,
    required this.itemName,
    required this.itemType,
    this.requiredBoards = 0,
    this.unitPrice = 0.0,
    this.totalAmount = 0.0,
    this.discountPercent = 0.0,
    this.discountAmount = 0.0,
    this.finalAmount = 0.0,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'project_id': projectId,
      'item_number': itemNumber,
      'item_name': itemName,
      'item_type': itemType,
      'required_boards': requiredBoards,
      'unit_price': unitPrice,
      'total_amount': totalAmount,
      'discount_percent': discountPercent,
      'discount_amount': discountAmount,
      'final_amount': finalAmount,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id']?.toInt(),
      projectId: map['project_id']?.toInt() ?? 0,
      itemNumber: map['item_number'] ?? '',
      itemName: map['item_name'] ?? '',
      itemType: map['item_type'] ?? '',
      requiredBoards: map['required_boards']?.toInt() ?? 0,
      unitPrice: map['unit_price']?.toDouble() ?? 0.0,
      totalAmount: map['total_amount']?.toDouble() ?? 0.0,
      discountPercent: map['discount_percent']?.toDouble() ?? 0.0,
      discountAmount: map['discount_amount']?.toDouble() ?? 0.0,
      finalAmount: map['final_amount']?.toDouble() ?? 0.0,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
    );
  }

  OrderItem copyWith({
    int? id,
    int? projectId,
    String? itemNumber,
    String? itemName,
    String? itemType,
    int? requiredBoards,
    double? unitPrice,
    double? totalAmount,
    double? discountPercent,
    double? discountAmount,
    double? finalAmount,
    DateTime? createdAt,
  }) {
    return OrderItem(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      itemNumber: itemNumber ?? this.itemNumber,
      itemName: itemName ?? this.itemName,
      itemType: itemType ?? this.itemType,
      requiredBoards: requiredBoards ?? this.requiredBoards,
      unitPrice: unitPrice ?? this.unitPrice,
      totalAmount: totalAmount ?? this.totalAmount,
      discountPercent: discountPercent ?? this.discountPercent,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'OrderItem(id: $id, projectId: $projectId, itemNumber: $itemNumber, itemName: $itemName, itemType: $itemType, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem &&
        other.id == id &&
        other.projectId == projectId &&
        other.itemNumber == itemNumber &&
        other.itemName == itemName &&
        other.itemType == itemType &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        projectId.hashCode ^
        itemNumber.hashCode ^
        itemName.hashCode ^
        itemType.hashCode ^
        createdAt.hashCode;
  }
}
