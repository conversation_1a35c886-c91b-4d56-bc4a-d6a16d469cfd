import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'lib/services/unified_database_service.dart';

/// اختبار قاعدة البيانات الموحدة
/// يقوم بإنشاء قاعدة البيانات وعرض معلومات شاملة عنها
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;

  print('🚀 بدء اختبار قاعدة البيانات الموحدة...\n');

  try {
    // إنشاء خدمة قاعدة البيانات الموحدة
    final unifiedDb = UnifiedDatabaseService();
    
    print('📊 إنشاء قاعدة البيانات الموحدة...');
    
    // الحصول على قاعدة البيانات (سيتم إنشاؤها تلقائياً)
    final db = await unifiedDb.database;
    
    print('✅ تم إنشاء قاعدة البيانات بنجاح!\n');
    
    // الحصول على معلومات قاعدة البيانات
    final dbInfo = await unifiedDb.getDatabaseInfo();
    
    print('📋 معلومات قاعدة البيانات الموحدة:');
    print('   📁 اسم قاعدة البيانات: ${dbInfo['database_name']}');
    print('   🔢 إصدار قاعدة البيانات: ${dbInfo['version']}');
    print('   📊 عدد الجداول: ${dbInfo['tables_count']}');
    print('');
    
    // عرض قائمة الجداول مع عدد السجلات
    final tables = dbInfo['tables'] as List<String>;
    final tableCounts = dbInfo['table_counts'] as Map<String, int>;
    
    print('📋 قائمة الجداول:');
    
    // تجميع الجداول حسب الأقسام
    final sections = {
      '📋 الفواتير والعملاء': ['customers', 'suppliers', 'invoices', 'invoice_items'],
      '🔧 الألومنيوم': ['profile_series', 'aluminum_profiles', 'aluminum_quotations', 'aluminum_quotation_items', 'hinge_designs', 'sliding_designs'],
      '🔩 uPVC': ['upvc_profile_series', 'upvc_profiles', 'upvc_quotations', 'upvc_quotation_items', 'upvc_hinge_designs', 'upvc_sliding_designs'],
      '✂️ التقطيع والمشاريع': ['cutting_projects', 'order_items', 'cutting_measurements'],
      '💰 الخزينة': ['treasuries', 'treasury_transactions'],
      '📋 إدارة المهام': ['task_categories', 'tasks'],
    };
    
    int totalTables = 0;
    for (final section in sections.entries) {
      print('\n${section.key}:');
      for (final tableName in section.value) {
        if (tables.contains(tableName)) {
          final count = tableCounts[tableName] ?? 0;
          print('   ✅ $tableName ($count سجل)');
          totalTables++;
        } else {
          print('   ❌ $tableName (غير موجود)');
        }
      }
    }
    
    // عرض الجداول الإضافية (إن وجدت)
    final extraTables = tables.where((table) => 
      !sections.values.expand((x) => x).contains(table)
    ).toList();
    
    if (extraTables.isNotEmpty) {
      print('\n🔍 جداول إضافية:');
      for (final tableName in extraTables) {
        final count = tableCounts[tableName] ?? 0;
        print('   📊 $tableName ($count سجل)');
        totalTables++;
      }
    }
    
    print('\n📊 ملخص النتائج:');
    print('   ✅ الجداول المنشأة: $totalTables');
    print('   📋 الجداول المتوقعة: ${sections.values.expand((x) => x).length}');
    
    if (totalTables == sections.values.expand((x) => x).length) {
      print('   🎉 تم إنشاء جميع الجداول المطلوبة بنجاح!');
    } else {
      print('   ⚠️ بعض الجداول مفقودة');
    }
    
    // اختبار العمليات الأساسية
    print('\n🧪 اختبار العمليات الأساسية...');
    
    // اختبار إدراج بيانات تجريبية
    await testBasicOperations(db);
    
    print('\n✅ تم اكتمال اختبار قاعدة البيانات الموحدة بنجاح!');
    
  } catch (e) {
    print('❌ خطأ في اختبار قاعدة البيانات: $e');
  }
}

/// اختبار العمليات الأساسية
Future<void> testBasicOperations(db) async {
  try {
    // اختبار إدراج عميل
    final customerId = await db.insert('customers', {
      'name': 'عميل تجريبي',
      'email': '<EMAIL>',
      'phone': '0501234567',
      'address': 'الرياض، السعودية',
      'notes': 'عميل تجريبي للاختبار',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج عميل تجريبي (ID: $customerId)');
    
    // اختبار إدراج خزينة
    final treasuryId = await db.insert('treasuries', {
      'name': 'الخزينة الرئيسية',
      'previous_balance': 0.0,
      'current_balance': 1000.0,
      'date': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج خزينة تجريبية (ID: $treasuryId)');
    
    // اختبار إدراج فئة مهام
    final categoryId = await db.insert('task_categories', {
      'name': 'مهام عامة',
      'description': 'فئة المهام العامة',
      'color': 0xFF2196F3,
      'icon': 0xe88a,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    print('   ✅ تم إدراج فئة مهام تجريبية (ID: $categoryId)');
    
    // اختبار الاستعلام
    final customers = await db.query('customers');
    print('   ✅ تم استرجاع ${customers.length} عميل');
    
    final treasuries = await db.query('treasuries');
    print('   ✅ تم استرجاع ${treasuries.length} خزينة');
    
    final categories = await db.query('task_categories');
    print('   ✅ تم استرجاع ${categories.length} فئة مهام');
    
  } catch (e) {
    print('   ❌ خطأ في اختبار العمليات الأساسية: $e');
  }
}
