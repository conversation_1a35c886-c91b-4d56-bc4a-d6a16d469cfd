import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'lib/services/unified_database_service.dart';

/// إعادة إنشاء قاعدة البيانات الموحدة
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🔄 إعادة إنشاء قاعدة البيانات الموحدة...\n');

  try {
    final unifiedDb = UnifiedDatabaseService();
    
    // إعادة إنشاء قاعدة البيانات
    await unifiedDb.recreateDatabase();
    
    print('✅ تم إعادة إنشاء قاعدة البيانات الموحدة بنجاح!');
    
    // عرض معلومات قاعدة البيانات الجديدة
    final dbInfo = await unifiedDb.getDatabaseInfo();
    
    print('\n📋 معلومات قاعدة البيانات الموحدة الجديدة:');
    print('   📁 اسم قاعدة البيانات: ${dbInfo['database_name']}');
    print('   🔢 إصدار قاعدة البيانات: ${dbInfo['version']}');
    print('   📊 عدد الجداول: ${dbInfo['tables_count']}');
    
    // فحص هيكل جدول العملاء
    final db = await unifiedDb.database;
    final customersSchema = await db.rawQuery("PRAGMA table_info(customers)");
    
    print('\n📋 هيكل جدول العملاء الجديد:');
    for (final column in customersSchema) {
      print('   📄 ${column['name']}: ${column['type']} ${column['notnull'] == 1 ? 'NOT NULL' : ''} ${column['dflt_value'] != null ? 'DEFAULT ${column['dflt_value']}' : ''}');
    }
    
    // اختبار إدراج عميل تجريبي
    print('\n🧪 اختبار إدراج عميل تجريبي...');
    final customerId = await unifiedDb.insertCustomer({
      'name': 'عميل تجريبي جديد',
      'phone': '0501234567',
      'address': 'الرياض، السعودية',
      'email': '<EMAIL>',
      'balance': 1000.0,
      'notes': 'عميل تجريبي للاختبار',
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'updated_at': DateTime.now().millisecondsSinceEpoch,
    });
    
    print('   ✅ تم إدراج عميل تجريبي (ID: $customerId)');
    
    // عرض جميع العملاء
    final customers = await unifiedDb.getAllCustomers();
    print('   📊 إجمالي العملاء: ${customers.length}');
    
    await unifiedDb.close();
    
  } catch (e) {
    print('❌ خطأ في إعادة إنشاء قاعدة البيانات: $e');
  }
}
