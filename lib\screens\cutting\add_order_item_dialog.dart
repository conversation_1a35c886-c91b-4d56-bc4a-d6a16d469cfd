import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/order_item.dart';
import '../../services/unified_cutting_service.dart';

class AddOrderItemDialog extends StatefulWidget {
  final int projectId;

  const AddOrderItemDialog({super.key, required this.projectId});

  @override
  State<AddOrderItemDialog> createState() => _AddOrderItemDialogState();
}

class _AddOrderItemDialogState extends State<AddOrderItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _itemNameController = TextEditingController();
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();

  String _selectedItemType = 'sticks';
  String _generatedItemNumber = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _generateItemNumber();
  }

  Future<void> _generateItemNumber() async {
    try {
      final itemNumber = await _databaseHelper.generateNextItemNumber(widget.projectId);
      setState(() {
        _generatedItemNumber = itemNumber;
      });
    } catch (e) {
      setState(() {
        _generatedItemNumber = '001';
      });
    }
  }

  @override
  void dispose() {
    _itemNameController.dispose();
    super.dispose();
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final localizations = AppLocalizations.of(context)!;

    try {
      final orderItem = OrderItem(
        projectId: widget.projectId,
        itemNumber: _generatedItemNumber,
        itemName: _itemNameController.text.trim(),
        itemType: _selectedItemType,
        createdAt: DateTime.now(),
      );

      await _databaseHelper.insertOrderItem(orderItem.toMap());

      if (mounted) {
        Navigator.of(context).pop(orderItem);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة البند بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${localizations.error}: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.add_shopping_cart,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(localizations.createNewOrder),
        ],
      ),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item Number (Automatic - Read-only)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      color: Colors.blue[600],
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localizations.itemNumber,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _generatedItemNumber.isNotEmpty
                                ? _generatedItemNumber
                                : '...',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[800],
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            localizations.automaticNumber,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.blue[500],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Item Name
              TextFormField(
                controller: _itemNameController,
                decoration: InputDecoration(
                  labelText: localizations.itemName,
                  hintText: localizations.enterItemName,
                  prefixIcon: const Icon(Icons.inventory),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${localizations.pleaseEnter} ${localizations.itemName}';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),

              const SizedBox(height: 16),

              // Item Type
              Text(
                localizations.itemType,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    RadioListTile<String>(
                      title: Row(
                        children: [
                          Icon(
                            Icons.straighten,
                            color: Colors.brown[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(localizations.sticks),
                        ],
                      ),
                      value: 'sticks',
                      groupValue: _selectedItemType,
                      onChanged: (value) {
                        setState(() {
                          _selectedItemType = value!;
                        });
                      },
                    ),
                    const Divider(height: 1),
                    RadioListTile<String>(
                      title: Row(
                        children: [
                          Icon(
                            Icons.view_module,
                            color: Colors.orange[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(localizations.boards),
                        ],
                      ),
                      value: 'boards',
                      groupValue: _selectedItemType,
                      onChanged: (value) {
                        setState(() {
                          _selectedItemType = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(localizations.cancel),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveItem,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(localizations.addItem),
        ),
      ],
    );
  }
}
