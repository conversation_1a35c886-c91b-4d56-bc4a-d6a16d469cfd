import 'package:sqflite/sqflite.dart';
import 'unified_database_service.dart';
import '../models/treasury.dart';

/// خدمة الخزينة الموحدة - تستخدم قاعدة البيانات الموحدة بدلاً من DatabaseHelperTreasury
class UnifiedTreasuryService {
  static final UnifiedTreasuryService _instance = UnifiedTreasuryService._internal();
  factory UnifiedTreasuryService() => _instance;
  UnifiedTreasuryService._internal();

  final UnifiedDatabaseService _unifiedDb = UnifiedDatabaseService();

  /// الحصول على قاعدة البيانات
  Future<Database> get database async => await _unifiedDb.database;

  /// إدراج خزينة جديدة
  Future<int> insertTreasury(Map<String, dynamic> treasury) async {
    return await _unifiedDb.insertTreasury(treasury);
  }

  /// إدراج معاملة خزينة
  Future<int> insertTreasuryTransaction(Map<String, dynamic> transaction) async {
    return await _unifiedDb.insertTreasuryTransaction(transaction);
  }

  /// الحصول على جميع الخزائن
  Future<List<Map<String, dynamic>>> getAllTreasuries() async {
    return await _unifiedDb.getAllTreasuries();
  }

  /// الحصول على خزينة بالمعرف
  Future<Map<String, dynamic>?> getTreasuryById(int id) async {
    final db = await database;
    final result = await db.query(
      'treasuries',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  /// تحديث خزينة
  Future<int> updateTreasury(int id, Map<String, dynamic> treasury) async {
    final db = await database;
    return await db.update(
      'treasuries',
      treasury,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف خزينة
  Future<int> deleteTreasury(int id) async {
    final db = await database;
    return await db.delete(
      'treasuries',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على معاملات خزينة
  Future<List<Map<String, dynamic>>> getTreasuryTransactions(int treasuryId) async {
    final db = await database;
    return await db.query(
      'treasury_transactions',
      where: 'treasury_id = ?',
      whereArgs: [treasuryId],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على جميع المعاملات
  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    final db = await database;
    return await db.query(
      'treasury_transactions',
      orderBy: 'date DESC',
    );
  }

  /// تحديث معاملة
  Future<int> updateTransaction(int id, Map<String, dynamic> transaction) async {
    final db = await database;
    return await db.update(
      'treasury_transactions',
      transaction,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف معاملة
  Future<int> deleteTransaction(int id) async {
    final db = await database;
    return await db.delete(
      'treasury_transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حساب الرصيد الحالي للخزينة
  Future<double> calculateCurrentBalance(int treasuryId) async {
    final db = await database;
    
    // الحصول على الرصيد السابق
    final treasuryResult = await db.query(
      'treasuries',
      columns: ['previous_balance'],
      where: 'id = ?',
      whereArgs: [treasuryId],
    );
    
    double previousBalance = 0.0;
    if (treasuryResult.isNotEmpty) {
      previousBalance = (treasuryResult.first['previous_balance'] as double?) ?? 0.0;
    }
    
    // حساب إجمالي الإيرادات والمصروفات
    final transactionsResult = await db.rawQuery('''
      SELECT 
        SUM(income) as total_income,
        SUM(expenses) as total_expenses
      FROM treasury_transactions
      WHERE treasury_id = ?
    ''', [treasuryId]);
    
    final totalIncome = (transactionsResult.first['total_income'] as double?) ?? 0.0;
    final totalExpenses = (transactionsResult.first['total_expenses'] as double?) ?? 0.0;
    
    return previousBalance + totalIncome - totalExpenses;
  }

  /// تحديث الرصيد الحالي للخزينة
  Future<void> updateTreasuryBalance(int treasuryId) async {
    final currentBalance = await calculateCurrentBalance(treasuryId);
    await updateTreasury(treasuryId, {'current_balance': currentBalance});
  }

  /// البحث في المعاملات
  Future<List<Map<String, dynamic>>> searchTransactions(String query) async {
    final db = await database;
    return await db.query(
      'treasury_transactions',
      where: 'description LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على معاملات حسب التاريخ
  Future<List<Map<String, dynamic>>> getTransactionsByDateRange(
    int treasuryId,
    String startDate,
    String endDate,
  ) async {
    final db = await database;
    return await db.query(
      'treasury_transactions',
      where: 'treasury_id = ? AND date BETWEEN ? AND ?',
      whereArgs: [treasuryId, startDate, endDate],
      orderBy: 'date DESC',
    );
  }

  /// الحصول على إحصائيات الخزينة
  Future<Map<String, dynamic>> getTreasuryStatistics(int treasuryId) async {
    final db = await database;
    
    // إحصائيات المعاملات
    final statsResult = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_transactions,
        SUM(income) as total_income,
        SUM(expenses) as total_expenses,
        COUNT(CASE WHEN income > 0 THEN 1 END) as income_count,
        COUNT(CASE WHEN expenses > 0 THEN 1 END) as expenses_count
      FROM treasury_transactions
      WHERE treasury_id = ?
    ''', [treasuryId]);
    
    final stats = statsResult.first;
    final totalIncome = (stats['total_income'] as double?) ?? 0.0;
    final totalExpenses = (stats['total_expenses'] as double?) ?? 0.0;
    final currentBalance = await calculateCurrentBalance(treasuryId);
    
    return {
      'treasury_id': treasuryId,
      'total_transactions': stats['total_transactions'] as int,
      'total_income': totalIncome,
      'total_expenses': totalExpenses,
      'net_amount': totalIncome - totalExpenses,
      'current_balance': currentBalance,
      'income_transactions_count': stats['income_count'] as int,
      'expenses_transactions_count': stats['expenses_count'] as int,
    };
  }

  /// إضافة معاملة دخل
  Future<int> addIncomeTransaction({
    required int treasuryId,
    required String description,
    required double amount,
    required String date,
    String notes = '',
    int? invoiceId,
    String? invoiceNumber,
  }) async {
    final transaction = {
      'treasury_id': treasuryId,
      'description': description,
      'income': amount,
      'expenses': 0.0,
      'notes': notes,
      'date': date,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'invoice_id': invoiceId,
      'invoice_number': invoiceNumber,
    };
    
    final transactionId = await insertTreasuryTransaction(transaction);
    await updateTreasuryBalance(treasuryId);
    return transactionId;
  }

  /// إضافة معاملة مصروف
  Future<int> addExpenseTransaction({
    required int treasuryId,
    required String description,
    required double amount,
    required String date,
    String notes = '',
    int? invoiceId,
    String? invoiceNumber,
  }) async {
    final transaction = {
      'treasury_id': treasuryId,
      'description': description,
      'income': 0.0,
      'expenses': amount,
      'notes': notes,
      'date': date,
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'invoice_id': invoiceId,
      'invoice_number': invoiceNumber,
    };
    
    final transactionId = await insertTreasuryTransaction(transaction);
    await updateTreasuryBalance(treasuryId);
    return transactionId;
  }

  /// الحصول على معاملات مرتبطة بفاتورة
  Future<List<Map<String, dynamic>>> getTransactionsByInvoice(int invoiceId) async {
    final db = await database;
    return await db.query(
      'treasury_transactions',
      where: 'invoice_id = ?',
      whereArgs: [invoiceId],
      orderBy: 'date DESC',
    );
  }

  /// إنشاء خزينة افتراضية
  Future<int> createDefaultTreasury() async {
    final treasury = {
      'name': 'الخزينة الرئيسية',
      'previous_balance': 0.0,
      'current_balance': 0.0,
      'date': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().millisecondsSinceEpoch,
    };
    
    return await insertTreasury(treasury);
  }

  /// التحقق من وجود خزائن
  Future<bool> hasTreasuries() async {
    final treasuries = await getAllTreasuries();
    return treasuries.isNotEmpty;
  }

  /// إنشاء بيانات تجريبية
  Future<void> createSampleData() async {
    // التحقق من وجود خزائن
    if (await hasTreasuries()) {
      return; // البيانات موجودة بالفعل
    }

    // إنشاء خزينة تجريبية
    await createDefaultTreasury();
  }

  // ==================== دوال إضافية مطلوبة ====================

  /// الحصول على معاملات اليوم
  Future<List<Map<String, dynamic>>> getTodayTransactions(int treasuryId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    return await getTransactionsByDateRange(
      treasuryId,
      startOfDay.toIso8601String(),
      endOfDay.toIso8601String()
    );
  }

  /// الحصول على ملخص اليوم
  Future<Map<String, double>> getTodaySummary(int treasuryId) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final transactions = await getTransactionsByDateRange(
      treasuryId,
      startOfDay.toIso8601String(),
      endOfDay.toIso8601String()
    );

    double income = 0.0;
    double expense = 0.0;

    for (final transaction in transactions) {
      final incomeAmount = transaction['income'] as double? ?? 0.0;
      final expenseAmount = transaction['expenses'] as double? ?? 0.0;

      income += incomeAmount;
      expense += expenseAmount;
    }

    return {
      'income': income,
      'expense': expense,
      'net': income - expense,
    };
  }

  /// تحديث معاملة خزينة
  Future<int> updateTreasuryTransaction(int id, Map<String, dynamic> transaction) async {
    final db = await _unifiedDb.database;
    return await db.update(
      'treasury_transactions',
      transaction,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// حذف معاملة خزينة (اسم بديل)
  Future<int> deleteTreasuryTransaction(int id) async {
    final db = await _unifiedDb.database;
    return await db.delete(
      'treasury_transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على خزينة بالمعرف (إرجاع Treasury object)
  Future<Treasury?> getTreasury(int id) async {
    final data = await getTreasuryById(id);
    if (data != null) {
      return Treasury.fromMap(data);
    }
    return null;
  }

  /// الحصول على إجماليات الخزينة
  Future<Map<String, double>> getTreasuryTotals(int treasuryId) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT
        SUM(CASE WHEN income > 0 THEN income ELSE 0 END) as totalIncome,
        SUM(CASE WHEN expenses > 0 THEN expenses ELSE 0 END) as totalExpenses
      FROM treasury_transactions
      WHERE treasury_id = ?
    ''', [treasuryId]);

    final row = result.first;
    return {
      'totalIncome': (row['totalIncome'] as double?) ?? 0.0,
      'totalExpenses': (row['totalExpenses'] as double?) ?? 0.0,
    };
  }
}
