import 'dart:io';

void main() async {
  print('بدء إصلاح الأخطاء في التطبيق...');
  
  // قائمة الملفات التي تحتاج إصلاح
  final filesToFix = [
    'lib/main.dart',
    'lib/screens/accounting/customers_screen.dart',
    'lib/screens/accounting/suppliers_screen.dart',
    'lib/screens/accounting/reports_screen.dart',
    'lib/screens/treasury/treasury_screen.dart',
    'lib/screens/treasury/treasury_summary_screen.dart',
    'lib/screens/treasury/treasury_transaction_form.dart',
    'lib/screens/cutting/panel_cutting_optimization_screen.dart',
    'lib/screens/cutting/panel_cutting_projects_screen.dart',
    'lib/screens/cutting/panel_cutting_screen.dart',
    'lib/screens/cutting/panel_optimization_screen.dart',
    'lib/screens/cutting/panel_project_view_screen.dart',
    'lib/screens/cutting/simple_panel_projects_screen.dart',
    'lib/screens/mysql_test_screen_simple.dart',
  ];

  for (String filePath in filesToFix) {
    await fixWithOpacityErrors(filePath);
  }

  // إصلاح أخطاء print
  await fixPrintStatements();
  
  // إصلاح الاستيرادات غير المستخدمة
  await fixUnusedImports();
  
  print('تم الانتهاء من إصلاح الأخطاء!');
}

Future<void> fixWithOpacityErrors(String filePath) async {
  try {
    final file = File(filePath);
    if (!await file.exists()) {
      print('الملف غير موجود: $filePath');
      return;
    }

    String content = await file.readAsString();
    
    // إصلاح withOpacity إلى withValues
    content = content.replaceAll(
      RegExp(r'\.withOpacity\(([^)]+)\)'),
      '.withValues(alpha: \$1)'
    );
    
    // إصلاح .value إلى .toARGB32()
    content = content.replaceAll(
      RegExp(r'Colors\.(\w+)\.value'),
      'Colors.\$1.toARGB32()'
    );
    
    await file.writeAsString(content);
    print('تم إصلاح: $filePath');
  } catch (e) {
    print('خطأ في إصلاح $filePath: $e');
  }
}

Future<void> fixPrintStatements() async {
  print('إصلاح أخطاء print...');
  
  final dartFiles = await Directory('lib')
      .list(recursive: true)
      .where((entity) => entity.path.endsWith('.dart'))
      .cast<File>()
      .toList();

  for (File file in dartFiles) {
    try {
      String content = await file.readAsString();
      
      // استبدال print بـ debugPrint
      if (content.contains('print(')) {
        // إضافة import للـ debugPrint إذا لم يكن موجود
        if (!content.contains("import 'package:flutter/foundation.dart'")) {
          content = "import 'package:flutter/foundation.dart';\n$content";
        }
        
        // استبدال print بـ debugPrint
        content = content.replaceAll('print(', 'debugPrint(');
        
        await file.writeAsString(content);
        print('تم إصلاح print في: ${file.path}');
      }
    } catch (e) {
      print('خطأ في إصلاح print في ${file.path}: $e');
    }
  }
}

Future<void> fixUnusedImports() async {
  print('إصلاح الاستيرادات غير المستخدمة...');
  
  // قائمة الملفات التي تحتوي على استيرادات غير مستخدمة
  final filesToFix = {
    'lib/screens/accounting/customer_account_details_screen.dart': [
      "import 'package:printing/printing.dart';"
    ],
    'lib/screens/accounting/supplier_account_details_screen.dart': [
      "import 'package:printing/printing.dart';"
    ],
  };

  for (String filePath in filesToFix.keys) {
    try {
      final file = File(filePath);
      if (!await file.exists()) continue;

      String content = await file.readAsString();
      
      for (String unusedImport in filesToFix[filePath]!) {
        content = content.replaceAll('$unusedImport\n', '');
        content = content.replaceAll(unusedImport, '');
      }
      
      await file.writeAsString(content);
      print('تم إصلاح الاستيرادات في: $filePath');
    } catch (e) {
      print('خطأ في إصلاح الاستيرادات في $filePath: $e');
    }
  }
}
