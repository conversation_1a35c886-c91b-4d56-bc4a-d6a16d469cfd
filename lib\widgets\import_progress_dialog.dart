import 'package:flutter/material.dart';

/// حوار عرض تقدم عملية الاستيراد
class ImportProgressDialog extends StatefulWidget {
  final String title;
  final Future<Map<String, dynamic>> Function(Function(String, int, int)) importFunction;

  const ImportProgressDialog({
    super.key,
    required this.title,
    required this.importFunction,
  });

  @override
  State<ImportProgressDialog> createState() => _ImportProgressDialogState();
}

class _ImportProgressDialogState extends State<ImportProgressDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  String _currentMessage = 'جاري التحضير...';
  int _currentProgress = 0;
  int _totalProgress = 100;
  bool _isCompleted = false;
  bool _hasError = false;
  String _errorMessage = '';
  Map<String, dynamic>? _result;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.repeat();
    _startImport();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startImport() async {
    try {
      final result = await widget.importFunction(_updateProgress);
      
      setState(() {
        _result = result;
        _isCompleted = true;
        _hasError = !result['success'];
        if (_hasError) {
          _errorMessage = result['error'] ?? 'حدث خطأ غير معروف';
          _currentMessage = 'فشل في الاستيراد';
        } else {
          _currentMessage = 'تم الاستيراد بنجاح';
          _currentProgress = 100;
        }
      });
      
      _animationController.stop();
      
    } catch (e) {
      setState(() {
        _isCompleted = true;
        _hasError = true;
        _errorMessage = e.toString();
        _currentMessage = 'حدث خطأ في الاستيراد';
      });
      
      _animationController.stop();
    }
  }

  void _updateProgress(String message, int current, int total) {
    if (mounted) {
      setState(() {
        _currentMessage = message;
        _currentProgress = current;
        _totalProgress = total;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _isCompleted,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // العنوان
              Row(
                children: [
                  Icon(
                    _hasError
                        ? Icons.error_outline
                        : _isCompleted
                            ? Icons.check_circle_outline
                            : Icons.download,
                    color: _hasError
                        ? Colors.red
                        : _isCompleted
                            ? Colors.green
                            : const Color(0xFF607D8B),
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // مؤشر التقدم
              if (!_isCompleted) ...[
                // دائرة التحميل المتحركة
                SizedBox(
                  height: 80,
                  width: 80,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // الدائرة الخلفية
                      CircularProgressIndicator(
                        value: _currentProgress / _totalProgress,
                        strokeWidth: 6,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF607D8B),
                        ),
                      ),
                      // النسبة المئوية
                      Text(
                        '${(_currentProgress / _totalProgress * 100).round()}%',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF607D8B),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // شريط التقدم الخطي
                LinearProgressIndicator(
                  value: _currentProgress / _totalProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    Color(0xFF607D8B),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // أيقونة النتيجة للحالة المكتملة
              if (_isCompleted) ...[
                Container(
                  height: 80,
                  width: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _hasError
                        ? Colors.red.withValues(alpha: 0.1)
                        : Colors.green.withValues(alpha: 0.1),
                  ),
                  child: Icon(
                    _hasError ? Icons.error : Icons.check_circle,
                    size: 40,
                    color: _hasError ? Colors.red : Colors.green,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // رسالة الحالة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _hasError
                      ? Colors.red.withValues(alpha: 0.1)
                      : _isCompleted
                          ? Colors.green.withValues(alpha: 0.1)
                          : const Color(0xFF607D8B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _currentMessage,
                  style: TextStyle(
                    fontSize: 14,
                    color: _hasError
                        ? Colors.red[700]
                        : _isCompleted
                            ? Colors.green[700]
                            : const Color(0xFF607D8B),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              // رسالة الخطأ
              if (_hasError && _errorMessage.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _errorMessage,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],

              // تفاصيل النتيجة
              if (_isCompleted && !_hasError && _result != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.green.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    children: [
                      if (_result!['importedSeries'] != null)
                        _buildResultRow(
                          'المجموعات المستوردة',
                          '${_result!['importedSeries']}',
                          Icons.folder,
                        ),
                      if (_result!['importedProfiles'] != null)
                        _buildResultRow(
                          'القطاعات المستوردة',
                          '${_result!['importedProfiles']}',
                          Icons.inventory_2,
                        ),
                      if (_result!['errors'] != null && 
                          (_result!['errors'] as List).isNotEmpty)
                        _buildResultRow(
                          'الأخطاء',
                          '${(_result!['errors'] as List).length}',
                          Icons.warning,
                          color: Colors.orange,
                        ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // أزرار التحكم
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (_isCompleted) ...[
                    if (_hasError)
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(false);
                        },
                        child: const Text('إغلاق'),
                      ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop(!_hasError);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _hasError ? Colors.red : Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(_hasError ? 'حسناً' : 'تم'),
                    ),
                  ] else ...[
                    TextButton(
                      onPressed: null, // لا يمكن الإلغاء أثناء التحميل
                      child: const Text('جاري التحميل...'),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value, IconData icon, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color ?? Colors.green[600],
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color ?? Colors.green[600],
            ),
          ),
        ],
      ),
    );
  }
}
