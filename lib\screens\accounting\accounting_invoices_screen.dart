import 'package:flutter/material.dart';
import '../../services/unified_invoice_service.dart';
import '../../services/currency_service.dart';
import 'invoices_screen.dart';
import 'customers_screen.dart';
import 'suppliers_screen.dart';
import 'reports_screen.dart';
import 'customer_accounts_screen.dart';
import 'supplier_accounts_screen.dart';

class AccountingInvoicesScreen extends StatefulWidget {
  const AccountingInvoicesScreen({super.key});

  @override
  State<AccountingInvoicesScreen> createState() => _AccountingInvoicesScreenState();
}

class _AccountingInvoicesScreenState extends State<AccountingInvoicesScreen> {
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  Map<String, double> _statistics = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }
  Future<void> _loadStatistics() async {
    try {
      final stats = await _database.getInvoiceStatistics();
      if (mounted) {
        setState(() {
          _statistics = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading statistics: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('حسابات وفواتير'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE8F5E8),
              Color(0xFFE3F2FD),
            ],
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistics Cards
              if (!_isLoading)
                _buildStatisticsSection(isTablet),

              SizedBox(height: isTablet ? 24 : 16),

              // Services Grid
              Expanded(
                child: _buildServicesGrid(isTablet),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatisticsSection(bool isTablet) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: isTablet ? 16 : 12),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 20 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Theme.of(context).colorScheme.primary,
                    size: isTablet ? 28 : 24,
                  ),
                  SizedBox(width: isTablet ? 12 : 8),
                  Text(
                    'ملخص الحسابات',
                    style: TextStyle(
                      fontSize: isTablet ? 20 : 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isTablet ? 16 : 12),
              LayoutBuilder(
                builder: (context, constraints) {
                  final netProfitLoss = _statistics['net_profit_loss'] ?? 0;
                  final profitLossColor = netProfitLoss >= 0 ? Colors.green : Colors.red;
                  final profitLossIcon = netProfitLoss >= 0 ? Icons.trending_up : Icons.trending_down;
                  final profitLossTitle = netProfitLoss >= 0 ? 'صافي الربح' : 'صافي الخسارة';

                  if (constraints.maxWidth > 900) {
                    // شاشة كبيرة - 5 كروت في صف واحد
                    return Row(
                      children: [
                        Expanded(child: _buildStatCard('المبيعات', _statistics['sales'] ?? 0, Colors.green, Icons.trending_up)),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard('المشتريات', _statistics['purchases'] ?? 0, Colors.blue, Icons.shopping_cart)),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard('مرتجع المبيعات', _statistics['sale_returns'] ?? 0, Colors.orange, Icons.keyboard_return)),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard('مرتجع المشتريات', _statistics['purchase_returns'] ?? 0, Colors.purple, Icons.undo)),
                        SizedBox(width: 12),
                        Expanded(child: _buildStatCard(profitLossTitle, netProfitLoss.abs(), profitLossColor, profitLossIcon)),
                      ],
                    );
                  } else if (constraints.maxWidth > 600) {
                    // شاشة متوسطة - صفين
                    return Column(
                      children: [
                        Row(
                          children: [
                            Expanded(child: _buildStatCard('المبيعات', _statistics['sales'] ?? 0, Colors.green, Icons.trending_up)),
                            SizedBox(width: 12),
                            Expanded(child: _buildStatCard('المشتريات', _statistics['purchases'] ?? 0, Colors.blue, Icons.shopping_cart)),
                            SizedBox(width: 12),
                            Expanded(child: _buildStatCard('مرتجع المبيعات', _statistics['sale_returns'] ?? 0, Colors.orange, Icons.keyboard_return)),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(child: _buildStatCard('مرتجع المشتريات', _statistics['purchase_returns'] ?? 0, Colors.purple, Icons.undo)),
                            SizedBox(width: 12),
                            Expanded(child: _buildStatCard(profitLossTitle, netProfitLoss.abs(), profitLossColor, profitLossIcon)),
                            Expanded(child: Container()), // مساحة فارغة للتوازن
                          ],
                        ),
                      ],
                    );
                  } else {
                    // شاشة صغيرة - عمودين
                    return Column(
                      children: [
                        Row(
                          children: [
                            Expanded(child: _buildStatCard('المبيعات', _statistics['sales'] ?? 0, Colors.green, Icons.trending_up)),
                            SizedBox(width: 8),
                            Expanded(child: _buildStatCard('المشتريات', _statistics['purchases'] ?? 0, Colors.blue, Icons.shopping_cart)),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(child: _buildStatCard('مرتجع المبيعات', _statistics['sale_returns'] ?? 0, Colors.orange, Icons.keyboard_return)),
                            SizedBox(width: 8),
                            Expanded(child: _buildStatCard('مرتجع المشتريات', _statistics['purchase_returns'] ?? 0, Colors.purple, Icons.undo)),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(child: _buildStatCard(profitLossTitle, netProfitLoss.abs(), profitLossColor, profitLossIcon)),
                            Expanded(child: Container()), // مساحة فارغة للتوازن
                          ],
                        ),
                      ],
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, double amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          FutureBuilder<String>(
            future: CurrencyService.instance.formatAmount(amount),
            builder: (context, snapshot) {
              return Text(
                snapshot.data ?? CurrencyService.instance.formatForPdf(amount),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              );
            },
          ),
        ],
      ),
    );
  }
  Widget _buildServicesGrid(bool isTablet) {
    final services = [
      {
        'title': 'فواتير المشتريات',
        'icon': Icons.shopping_cart,
        'color': const Color(0xFF2196F3),
        'description': 'إدارة فواتير المشتريات من الموردين',
        'onTap': () => _navigateToInvoices('purchase'),
      },
      {
        'title': 'فواتير المبيعات',
        'icon': Icons.point_of_sale,
        'color': const Color(0xFF4CAF50),
        'description': 'إدارة فواتير المبيعات للعملاء',
        'onTap': () => _navigateToInvoices('sale'),
      },
      {
        'title': 'فواتير مرتجع المشتريات',
        'icon': Icons.undo,
        'color': const Color(0xFF9C27B0),
        'description': 'إدارة فواتير إرجاع البضائع للموردين',
        'onTap': () => _navigateToInvoices('purchase_return'),
      },
      {
        'title': 'فواتير مرتجع المبيعات',
        'icon': Icons.keyboard_return,
        'color': const Color(0xFFFF9800),
        'description': 'إدارة فواتير إرجاع البضائع من العملاء',
        'onTap': () => _navigateToInvoices('sale_return'),
      },
      {
        'title': 'إدارة العملاء',
        'icon': Icons.person_add,
        'color': const Color(0xFF00BCD4),
        'description': 'إضافة وتعديل بيانات العملاء',
        'onTap': () => _navigateToCustomers(),
      },
      {
        'title': 'حسابات العملاء',
        'icon': Icons.people,
        'color': const Color(0xFF00ACC1),
        'description': 'عرض أرصدة وحسابات العملاء',
        'onTap': () => _navigateToCustomerAccounts(),
      },
      {
        'title': 'إدارة الموردين',
        'icon': Icons.business_center,
        'color': const Color(0xFF795548),
        'description': 'إضافة وتعديل بيانات الموردين',
        'onTap': () => _navigateToSuppliers(),
      },
      {
        'title': 'حسابات الموردين',
        'icon': Icons.business,
        'color': const Color(0xFF6D4C41),
        'description': 'عرض أرصدة وحسابات الموردين',
        'onTap': () => _navigateToSupplierAccounts(),
      },
      {
        'title': 'التقارير',
        'icon': Icons.bar_chart,
        'color': const Color(0xFF607D8B),
        'description': 'تقارير مفصلة عن المبيعات والمشتريات',
        'onTap': () => _navigateToReports(),
      },
    ];

    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount;
        double childAspectRatio;

        if (constraints.maxWidth < 600) {
          // موبايل - عمودين
          crossAxisCount = 2;
          childAspectRatio = 1.1;
        } else if (constraints.maxWidth < 900) {
          // تابلت - 3 أعمدة
          crossAxisCount = 3;
          childAspectRatio = 1.2;
        } else {
          // شاشة كبيرة - 4 أعمدة
          crossAxisCount = 4;
          childAspectRatio = 1.3;
        }

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: services.length,
          itemBuilder: (context, index) {
            final service = services[index];
            return _buildServiceCard(
              title: service['title'] as String,
              icon: service['icon'] as IconData,
              color: service['color'] as Color,
              description: service['description'] as String,
              onTap: service['onTap'] as VoidCallback,
              isTablet: isTablet,
            );
          },
        );
      },
    );
  }

  Widget _buildServiceCard({
    required String title,
    required IconData icon,
    required Color color,
    required String description,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isTablet ? 16 : 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: isTablet ? 32 : 28,
                  color: color,
                ),
              ),
              SizedBox(height: isTablet ? 12 : 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: isTablet ? 14 : 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isTablet ? 8 : 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: isTablet ? 11 : 10,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToInvoices(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoicesScreen(invoiceType: type),
      ),
    ).then((_) => _loadStatistics());
  }
  void _navigateToCustomers() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomersScreen(),
      ),
    );
  }

  void _navigateToCustomerAccounts() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomerAccountsScreen(),
      ),
    );
  }

  void _navigateToSuppliers() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SuppliersScreen(),
      ),
    );
  }

  void _navigateToSupplierAccounts() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SupplierAccountsScreen(),
      ),
    );
  }

  void _navigateToReports() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ReportsScreen(),
      ),
    );
  }
}
