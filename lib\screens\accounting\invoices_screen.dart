import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/invoice.dart';
import '../../services/unified_invoice_service.dart';
import 'add_edit_invoice_screen.dart';
import '../../services/currency_service.dart';

class InvoicesScreen extends StatefulWidget {
  final String invoiceType;

  const InvoicesScreen({super.key, required this.invoiceType});

  @override
  State<InvoicesScreen> createState() => _InvoicesScreenState();
}

class _InvoicesScreenState extends State<InvoicesScreen> {
  final UnifiedInvoiceService _database = UnifiedInvoiceService.instance;
  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadInvoices();
    _searchController.addListener(_filterInvoices);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInvoices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final invoices = await _database.getInvoices(type: widget.invoiceType);
      setState(() {
        _invoices = invoices;
        _filteredInvoices = invoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل الفواتير: $e');
      }
    }
  }

  void _filterInvoices() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredInvoices = _invoices;
      } else {
        _filteredInvoices = _invoices.where((invoice) {
          return invoice.invoiceNumber.toLowerCase().contains(query) ||
              invoice.supplierOrCustomerName.toLowerCase().contains(query) ||
              invoice.notes.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _getInvoiceTypeTitle() {
    switch (widget.invoiceType) {
      case 'purchase':
        return 'فواتير المشتريات';
      case 'sale':
        return 'فواتير المبيعات';
      case 'purchase_return':
        return 'فواتير مرتجع المشتريات';
      case 'sale_return':
        return 'فواتير مرتجع المبيعات';
      default:
        return 'الفواتير';
    }
  }

  String _getEntityLabel() {
    switch (widget.invoiceType) {
      case 'purchase':
      case 'purchase_return':
        return 'المورد';
      case 'sale':
      case 'sale_return':
        return 'العميل';
      default:
        return 'الطرف';
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(_getInvoiceTypeTitle()),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInvoices,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddInvoice(),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8F9FA),
              Color(0xFFE8F5E8),
            ],
          ),
        ),
        child: Column(
          children: [
            // Search bar
            Container(
              padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'البحث في الفواتير...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 16 : 12,
                    vertical: isTablet ? 16 : 12,
                  ),
                ),
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredInvoices.isEmpty
                      ? _buildEmptyState(isTablet)
                      : _buildInvoicesList(isTablet),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isTablet) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: isTablet ? 80 : 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: isTablet ? 16 : 12),
          Text(
            _searchController.text.isEmpty
                ? 'لا توجد فواتير حتى الآن'
                : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: isTablet ? 18 : 16,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: isTablet ? 8 : 6),
          Text(
            _searchController.text.isEmpty
                ? 'اضغط على + لإضافة فاتورة جديدة'
                : 'جرب كلمات بحث أخرى',
            style: TextStyle(
              fontSize: isTablet ? 14 : 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesList(bool isTablet) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 16.0 : 12.0,
        vertical: isTablet ? 8.0 : 6.0,
      ),
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];
        return _buildInvoiceCard(invoice, isTablet);
      },
    );
  }

  Widget _buildInvoiceCard(Invoice invoice, bool isTablet) {
    return Card(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToEditInvoice(invoice),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'رقم الفاتورة: ${invoice.invoiceNumber}',
                          style: TextStyle(
                            fontSize: isTablet ? 16 : 14,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        SizedBox(height: isTablet ? 4 : 2),
                        Text(
                          '${_getEntityLabel()}: ${invoice.supplierOrCustomerName}',
                          style: TextStyle(
                            fontSize: isTablet ? 14 : 12,
                            color: Colors.grey[700],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        _navigateToEditInvoice(invoice);
                      } else if (value == 'delete') {
                        _deleteInvoice(invoice);
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: isTablet ? 12 : 8),

              // Details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      'التاريخ',
                      DateFormat('yyyy/MM/dd').format(invoice.date),
                      Icons.calendar_today,
                      isTablet,
                    ),
                  ),
                  SizedBox(width: isTablet ? 16 : 12),
                  Expanded(
                    child: _buildDetailItem(
                      'عدد الأصناف',
                      '${invoice.items.length}',
                      Icons.inventory,
                      isTablet,
                    ),
                  ),
                ],
              ),

              SizedBox(height: isTablet ? 8 : 6),

              // Amount details
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'إجمالي الفاتورة:',
                          style: TextStyle(
                            fontSize: isTablet ? 13 : 11,
                            color: Colors.grey[700],
                          ),
                        ),
                        FutureBuilder<String>(
                          future: CurrencyService.instance.formatAmount(invoice.totalAmount),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? CurrencyService.instance.formatForPdf(invoice.totalAmount),
                              style: TextStyle(
                                fontSize: isTablet ? 13 : 11,
                                fontWeight: FontWeight.w500,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    if (invoice.discount > 0) ...[
                      SizedBox(height: isTablet ? 4 : 2),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'الخصم:',
                            style: TextStyle(
                              fontSize: isTablet ? 13 : 11,
                              color: Colors.grey[700],
                            ),
                          ),
                          FutureBuilder<String>(
                            future: CurrencyService.instance.formatAmount(invoice.discount),
                            builder: (context, snapshot) {
                              return Text(
                                snapshot.data ?? CurrencyService.instance.formatForPdf(invoice.discount),
                                style: TextStyle(
                                  fontSize: isTablet ? 13 : 11,
                                  color: Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                    if (invoice.expenses > 0) ...[
                      SizedBox(height: isTablet ? 4 : 2),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'المصاريف:',
                            style: TextStyle(
                              fontSize: isTablet ? 13 : 11,
                              color: Colors.grey[700],
                            ),
                          ),
                          FutureBuilder<String>(
                            future: CurrencyService.instance.formatAmount(invoice.expenses),
                            builder: (context, snapshot) {
                              return Text(
                                snapshot.data ?? CurrencyService.instance.formatForPdf(invoice.expenses),
                                style: TextStyle(
                                  fontSize: isTablet ? 13 : 11,
                                  color: Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                    Divider(height: isTablet ? 12 : 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'صافي الفاتورة:',
                          style: TextStyle(
                            fontSize: isTablet ? 14 : 12,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        FutureBuilder<String>(
                          future: CurrencyService.instance.formatAmount(invoice.netAmount),
                          builder: (context, snapshot) {
                            return Text(
                              snapshot.data ?? CurrencyService.instance.formatForPdf(invoice.netAmount),
                              style: TextStyle(
                                fontSize: isTablet ? 14 : 12,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              if (invoice.notes.isNotEmpty) ...[
                SizedBox(height: isTablet ? 8 : 6),
                Text(
                  'ملاحظات: ${invoice.notes}',
                  style: TextStyle(
                    fontSize: isTablet ? 12 : 10,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, bool isTablet) {
    return Row(
      children: [
        Icon(
          icon,
          size: isTablet ? 16 : 14,
          color: Colors.grey[600],
        ),
        SizedBox(width: isTablet ? 6 : 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: isTablet ? 11 : 9,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: isTablet ? 12 : 10,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToAddInvoice() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditInvoiceScreen(invoiceType: widget.invoiceType),
      ),
    ).then((_) => _loadInvoices());
  }

  void _navigateToEditInvoice(Invoice invoice) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditInvoiceScreen(
          invoiceType: widget.invoiceType,
          invoice: invoice,
        ),
      ),
    ).then((_) => _loadInvoices());
  }

  Future<void> _deleteInvoice(Invoice invoice) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فاتورة رقم ${invoice.invoiceNumber}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && invoice.id != null) {
      try {
        await _database.deleteInvoice(invoice.id!);
        _showSuccessSnackBar('تم حذف الفاتورة بنجاح');
        _loadInvoices();
      } catch (e) {
        _showErrorSnackBar('خطأ في حذف الفاتورة: $e');
      }
    }
  }
}
