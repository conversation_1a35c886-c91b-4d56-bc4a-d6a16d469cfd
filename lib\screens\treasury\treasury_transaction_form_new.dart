// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/treasury.dart';
import '../../models/treasury_transaction.dart';
import '../../services/unified_treasury_service.dart';
import '../../providers/database_provider.dart';

class TreasuryTransactionForm extends StatefulWidget {
  final int treasuryId;
  final TreasuryTransaction? transaction;

  const TreasuryTransactionForm({
    super.key,
    required this.treasuryId,
    this.transaction,
  });

  @override
  State<TreasuryTransactionForm> createState() => _TreasuryTransactionFormState();
}

class _TreasuryTransactionFormState extends State<TreasuryTransactionForm> {
  late UnifiedTreasuryService _databaseHelper;
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _descriptionController;
  late TextEditingController _incomeController;
  late TextEditingController _expensesController;
  late TextEditingController _notesController;
  late TextEditingController _dateController;
  
  Treasury? _treasury;
  List<TreasuryTransaction> _transactions = [];
  bool _isLoading = true;
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _balance = 0.0;

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController();
    _incomeController = TextEditingController(text: '0.00');
    _expensesController = TextEditingController(text: '0.00');
    _notesController = TextEditingController();
    
    final now = DateTime.now();
    _dateController = TextEditingController(
      text: DateFormat('yyyy-MM-dd').format(now),
    );
    
    _loadData();
    _loadTransactionsForDate(now);
    
    if (widget.transaction != null) {
      _descriptionController.text = widget.transaction!.description;
      _incomeController.text = widget.transaction!.income.toStringAsFixed(2);
      _expensesController.text = widget.transaction!.expenses.toStringAsFixed(2);
      _notesController.text = widget.transaction!.notes;
      _dateController.text = DateFormat('yyyy-MM-dd').format(widget.transaction!.date);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _databaseHelper = DatabaseProvider.of(context);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _incomeController.dispose();
    _expensesController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _loadTransactionsForDate(DateTime date) async {
    try {
      final startDate = DateTime(date.year, date.month, date.day);
      final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);
      
      final transactionsData = await _databaseHelper.getTransactionsByDateRange(
        widget.treasuryId,
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      );
      final transactions = transactionsData.map((data) => TreasuryTransaction.fromMap(data)).toList();

      setState(() {
        _transactions = transactions;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading transactions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final treasury = await _databaseHelper.getTreasury(widget.treasuryId);
      final totals = await _databaseHelper.getTreasuryTotals(widget.treasuryId);
      
      setState(() {
        _treasury = treasury;
        _totalIncome = totals['income']!;
        _totalExpenses = totals['expenses']!;
        _balance = totals['balance']!;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final description = _descriptionController.text.trim();
    final income = double.tryParse(_incomeController.text) ?? 0.0;
    final expenses = double.tryParse(_expensesController.text) ?? 0.0;
    final notes = _notesController.text.trim();
    final date = DateFormat('yyyy-MM-dd').parse(_dateController.text);

    if (income == 0.0 && expenses == 0.0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.incomeOrExpensesRequired),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      if (widget.transaction == null) {
        // Create new transaction
        final transaction = TreasuryTransaction(
          treasuryId: widget.treasuryId,
          description: description,
          income: income,
          expenses: expenses,
          notes: notes,
          date: date,
        );
        
        await _databaseHelper.insertTreasuryTransaction(transaction.toMap());
      } else {
        // Update existing transaction
        final updatedTransaction = widget.transaction!.copyWith(
          description: description,
          income: income,
          expenses: expenses,
          notes: notes,
          date: date,
        );
        
        await _databaseHelper.updateTreasuryTransaction(updatedTransaction.id!, updatedTransaction.toMap());
      }
      
      // Reset form and reload data
      if (mounted) {
        _resetForm();
        _loadData();
        _loadTransactionsForDate(date);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.transaction == null
                  ? AppLocalizations.of(context)!.transactionAdded
                  : AppLocalizations.of(context)!.transactionUpdated,
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resetForm() {
    _descriptionController.clear();
    _incomeController.text = '0.00';
    _expensesController.text = '0.00';
    _notesController.clear();
    final now = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(now);
    _formKey.currentState?.reset();
    _loadTransactionsForDate(now);
  }

  Widget _buildTreasuryInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildInfoColumn(
              AppLocalizations.of(context)!.income,
              '+${NumberFormat("#,##0.00").format(_totalIncome)}',
              Colors.green,
            ),
            _buildInfoColumn(
              AppLocalizations.of(context)!.expenses,
              '-${NumberFormat("#,##0.00").format(_totalExpenses)}',
              Colors.red,
            ),
            _buildInfoColumn(
              AppLocalizations.of(context)!.balance,
              NumberFormat("#,##0.00").format(_balance),
              _balance >= 0 ? Colors.blue : Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value, Color textColor) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildTransactionForm(AppLocalizations localizations) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.transaction == null
                    ? localizations.addTransaction
                    : localizations.editTransaction,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _dateController,
                decoration: InputDecoration(
                  labelText: localizations.date,
                  hintText: 'YYYY-MM-DD',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateFormat('yyyy-MM-dd').parse(_dateController.text),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    setState(() {
                      _dateController.text = DateFormat('yyyy-MM-dd').format(date);
                    });
                    await _loadTransactionsForDate(date);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.dateRequired;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: localizations.description,
                  hintText: localizations.enterDescription,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.description),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations.descriptionRequired;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _incomeController,
                      decoration: InputDecoration(
                        labelText: localizations.income,
                        hintText: '0.00',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.arrow_downward, color: Colors.green),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _expensesController,
                      decoration: InputDecoration(
                        labelText: localizations.expenses,
                        hintText: '0.00',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: const Icon(Icons.arrow_upward, color: Colors.red),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: localizations.notes,
                  hintText: localizations.enterNotes,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    icon: const Icon(Icons.refresh),
                    label: Text(localizations.reset),
                    onPressed: _resetForm,
                  ),
                  FilledButton.icon(
                    icon: const Icon(Icons.save),
                    label: Text(localizations.save),
                    onPressed: _saveTransaction,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionsForSelectedDate() {
    if (_transactions.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            AppLocalizations.of(context)!.noTransactions,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            AppLocalizations.of(context)!.transactions,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _transactions.length,
          itemBuilder: (context, index) {
            final transaction = _transactions[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
              child: ListTile(
                title: Text(
                  transaction.description,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                subtitle: transaction.notes.isNotEmpty 
                    ? Text(
                        transaction.notes,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      )
                    : null,
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (transaction.income > 0)
                      Text(
                        '+${NumberFormat("#,##0.00").format(transaction.income)}',
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    if (transaction.expenses > 0)
                      Text(
                        '-${NumberFormat("#,##0.00").format(transaction.expenses)}',
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  ],
                ),
                onTap: () => _showTransactionActions(transaction),
              ),
            );
          },
        ),
      ],
    );
  }

  void _showTransactionActions(TreasuryTransaction transaction) {
    final localizations = AppLocalizations.of(context)!;
    
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: Text(localizations.edit),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _descriptionController.text = transaction.description;
                  _incomeController.text = transaction.income.toStringAsFixed(2);
                  _expensesController.text = transaction.expenses.toStringAsFixed(2);
                  _notesController.text = transaction.notes;
                  _dateController.text = DateFormat('yyyy-MM-dd').format(transaction.date);
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: Text(localizations.delete),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmationDialog(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmationDialog(TreasuryTransaction transaction) {
    final localizations = AppLocalizations.of(context)!;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteTransaction),
        content: Text(localizations.deleteTransactionConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          FilledButton(
            onPressed: () async {
              try {
                await _databaseHelper.deleteTreasuryTransaction(transaction.id!);
                if (mounted) {
                  Navigator.pop(context);
                  _loadData();
                  _loadTransactionsForDate(transaction.date);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(localizations.transactionDeleted),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_treasury?.name ?? localizations.treasury),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildTreasuryInfoCard(),
                  ),
                  const SizedBox(height: 16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: _buildTransactionForm(localizations),
                  ),
                  const SizedBox(height: 16),
                  const Divider(height: 1),
                  _buildTransactionsForSelectedDate(),
                  const SizedBox(height: 16),
                ],
              ),
            ),
    );
  }
}
