import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../models/profile_series.dart';
import '../../models/aluminum_profile.dart';
import 'profile_management_screen.dart';

class ProfileCategoriesScreen extends StatefulWidget {
  final ProfileType profileType;
  final ProfileSeries series;

  const ProfileCategoriesScreen({
    super.key,
    required this.profileType,
    required this.series,
  });

  @override
  State<ProfileCategoriesScreen> createState() => _ProfileCategoriesScreenState();
}

class _ProfileCategoriesScreenState extends State<ProfileCategoriesScreen> {
  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    final localizations = AppLocalizations.of(context)!;
    final isTablet = MediaQuery.of(context).size.width > 600;
    
    final categories = widget.profileType == ProfileType.hinge 
        ? ProfileCategory.getHingeCategories() 
        : ProfileCategory.getSlidingCategories();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${widget.series.name} - فئات القطاعات',
        ),
        backgroundColor: const Color(0xFF607D8B),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF607D8B),
              Color(0xFFECEFF1),
            ],
            stops: [0.0, 0.3],
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: isTablet ? 4 : 3,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: isTablet ? 1.1 : 0.9,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryCard(category);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryCard(ProfileCategory category) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProfileManagementScreen(
                profileType: widget.profileType,
                profileCategory: category,
                series: widget.series,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                _getCategoryColor(category).withValues(alpha: 0.1),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getCategoryColor(category).withValues(alpha: 0.15),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getCategoryIcon(category),
                  size: 28,
                  color: _getCategoryColor(category),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                category.arabicName,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(category),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                category.englishName,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return Icons.crop_square;
      case ProfileCategory.bar:
        return Icons.horizontal_rule;
      case ProfileCategory.dalfa:
        return Icons.door_front_door;
      case ProfileCategory.marad:
        return Icons.vertical_split;
      case ProfileCategory.baketa:
        return Icons.linear_scale;
      case ProfileCategory.soas:
        return Icons.horizontal_distribute;
      case ProfileCategory.dalfaSilk:
        return Icons.grid_4x4;
      case ProfileCategory.olba:
        return Icons.inventory_2;
      case ProfileCategory.filta:
        return Icons.circle;
      case ProfileCategory.skineh:
        return Icons.linear_scale;
      case ProfileCategory.anf:
        return Icons.arrow_forward;
    }
  }

  Color _getCategoryColor(ProfileCategory category) {
    switch (category) {
      case ProfileCategory.halaf:
        return const Color(0xFF2196F3); // Blue
      case ProfileCategory.bar:
        return const Color(0xFF4CAF50); // Green
      case ProfileCategory.dalfa:
        return const Color(0xFF9C27B0); // Purple
      case ProfileCategory.marad:
        return const Color(0xFFFF9800); // Orange
      case ProfileCategory.baketa:
        return const Color(0xFFF44336); // Red
      case ProfileCategory.soas:
        return const Color(0xFF00BCD4); // Cyan
      case ProfileCategory.dalfaSilk:
        return const Color(0xFF795548); // Brown
      case ProfileCategory.olba:
        return const Color(0xFF607D8B); // Blue Grey
      case ProfileCategory.filta:
        return const Color(0xFF9E9E9E); // Grey
      case ProfileCategory.skineh:
        return const Color(0xFF3F51B5); // Indigo
      case ProfileCategory.anf:
        return const Color(0xFFE91E63); // Pink
    }
  }
}
