import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'lib/services/unified_invoice_service.dart';
import 'lib/services/unified_treasury_service.dart';
import 'lib/services/unified_cutting_service.dart';
import 'lib/services/unified_aluminum_service.dart';
import 'lib/services/unified_upvc_service.dart';

/// اختبار الخدمات الموحدة الجديدة
Future<void> main() async {
  // تهيئة sqflite_ffi
  sqfliteFfiInit();
  databaseFactory = databaseFactoryFfi;
  
  print('🧪 اختبار الخدمات الموحدة الجديدة...\n');

  try {
    // اختبار خدمة الفواتير الموحدة
    await testUnifiedInvoiceService();
    
    // اختبار خدمة الخزينة الموحدة
    await testUnifiedTreasuryService();
    
    // اختبار خدمة التقطيع والمهام الموحدة
    await testUnifiedCuttingService();
    
    // اختبار خدمة الألومنيوم الموحدة
    await testUnifiedAluminumService();
    
    // اختبار خدمة uPVC الموحدة
    await testUnifiedUpvcService();
    
    print('\n🎉 تم اكتمال جميع اختبارات الخدمات الموحدة بنجاح!');
    print('✅ جميع الخدمات الموحدة تعمل بشكل صحيح!');
    
  } catch (e) {
    print('❌ خطأ في اختبار الخدمات الموحدة: $e');
  }
}

/// اختبار خدمة الفواتير الموحدة
Future<void> testUnifiedInvoiceService() async {
  print('📋 اختبار خدمة الفواتير الموحدة...');
  
  try {
    final invoiceService = UnifiedInvoiceService();
    
    // اختبار استرجاع البيانات الموجودة
    final customers = await invoiceService.getAllCustomers();
    print('   👥 العملاء: ${customers.length} عميل');
    
    final suppliers = await invoiceService.getAllSuppliers();
    print('   🏢 الموردين: ${suppliers.length} مورد');
    
    final invoices = await invoiceService.getAllInvoices();
    print('   📄 الفواتير: ${invoices.length} فاتورة');
    
    // اختبار إحصائيات المبيعات
    final totalSales = await invoiceService.getTotalSales();
    print('   💰 إجمالي المبيعات: ${totalSales.toStringAsFixed(2)} ريال');
    
    final totalPurchases = await invoiceService.getTotalPurchases();
    print('   💸 إجمالي المشتريات: ${totalPurchases.toStringAsFixed(2)} ريال');
    
    // اختبار البحث
    final searchResults = await invoiceService.searchInvoices('فاتورة');
    print('   🔍 نتائج البحث: ${searchResults.length} فاتورة');
    
    print('   ✅ خدمة الفواتير الموحدة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في خدمة الفواتير الموحدة: $e');
  }
}

/// اختبار خدمة الخزينة الموحدة
Future<void> testUnifiedTreasuryService() async {
  print('\n💰 اختبار خدمة الخزينة الموحدة...');
  
  try {
    final treasuryService = UnifiedTreasuryService();
    
    // اختبار استرجاع البيانات الموجودة
    final treasuries = await treasuryService.getAllTreasuries();
    print('   🏦 الخزائن: ${treasuries.length} خزينة');
    
    final transactions = await treasuryService.getAllTransactions();
    print('   💳 المعاملات: ${transactions.length} معاملة');
    
    // اختبار إحصائيات الخزينة إذا كانت موجودة
    if (treasuries.isNotEmpty) {
      final treasuryId = treasuries.first['id'] as int;
      final stats = await treasuryService.getTreasuryStatistics(treasuryId);
      print('   📊 إحصائيات الخزينة الأولى:');
      print('      💰 الرصيد الحالي: ${stats['current_balance']} ريال');
      print('      📈 إجمالي الإيرادات: ${stats['total_income']} ريال');
      print('      📉 إجمالي المصروفات: ${stats['total_expenses']} ريال');
    }
    
    print('   ✅ خدمة الخزينة الموحدة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في خدمة الخزينة الموحدة: $e');
  }
}

/// اختبار خدمة التقطيع والمهام الموحدة
Future<void> testUnifiedCuttingService() async {
  print('\n✂️ اختبار خدمة التقطيع والمهام الموحدة...');
  
  try {
    final cuttingService = UnifiedCuttingService();
    
    // اختبار استرجاع البيانات الموجودة
    final projects = await cuttingService.getAllCuttingProjects();
    print('   📋 مشاريع التقطيع: ${projects.length} مشروع');
    
    final tasks = await cuttingService.getAllTasks();
    print('   📝 المهام: ${tasks.length} مهمة');
    
    final categories = await cuttingService.getAllTaskCategories();
    print('   📂 فئات المهام: ${categories.length} فئة');
    
    // اختبار الإحصائيات
    final projectStats = await cuttingService.getProjectsStatistics();
    print('   📊 إحصائيات المشاريع:');
    print('      📋 المشاريع: ${projectStats['projects_count']}');
    print('      📦 العناصر: ${projectStats['items_count']}');
    print('      📏 القياسات: ${projectStats['measurements_count']}');
    
    final taskStats = await cuttingService.getTasksStatistics();
    print('   📊 إحصائيات المهام:');
    print('      📝 إجمالي المهام: ${taskStats['total_tasks']}');
    
    print('   ✅ خدمة التقطيع والمهام الموحدة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في خدمة التقطيع والمهام الموحدة: $e');
  }
}

/// اختبار خدمة الألومنيوم الموحدة
Future<void> testUnifiedAluminumService() async {
  print('\n🔧 اختبار خدمة الألومنيوم الموحدة...');
  
  try {
    final aluminumService = UnifiedAluminumService();
    
    // اختبار استرجاع البيانات الموجودة
    final profiles = await aluminumService.getAllProfiles();
    print('   🔧 قطاعات الألومنيوم: ${profiles.length} قطاع');
    
    final quotations = await aluminumService.getAllQuotations();
    print('   💰 عروض أسعار الألومنيوم: ${quotations.length} عرض سعر');
    
    final hingeDesigns = await aluminumService.getHingeDesigns();
    print('   🚪 تصاميم المفصلي: ${hingeDesigns.length} تصميم');
    
    final slidingDesigns = await aluminumService.getSlidingDesigns();
    print('   🪟 تصاميم السحاب: ${slidingDesigns.length} تصميم');
    
    // اختبار الإحصائيات
    final stats = await aluminumService.getAluminumStatistics();
    print('   📊 إحصائيات الألومنيوم:');
    print('      🔧 القطاعات: ${stats['profiles_count']}');
    print('      💰 عروض الأسعار: ${stats['quotations_count']}');
    print('      🎨 إجمالي التصاميم: ${stats['total_designs']}');
    
    // اختبار البحث
    if (profiles.isNotEmpty) {
      final searchResults = await aluminumService.searchProfiles('ألومنيوم');
      print('   🔍 نتائج البحث: ${searchResults.length} قطاع');
    }
    
    print('   ✅ خدمة الألومنيوم الموحدة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في خدمة الألومنيوم الموحدة: $e');
  }
}

/// اختبار خدمة uPVC الموحدة
Future<void> testUnifiedUpvcService() async {
  print('\n🔩 اختبار خدمة uPVC الموحدة...');
  
  try {
    final upvcService = UnifiedUpvcService();
    
    // اختبار استرجاع البيانات الموجودة
    final profiles = await upvcService.getAllProfiles();
    print('   🔩 قطاعات uPVC: ${profiles.length} قطاع');
    
    final quotations = await upvcService.getAllQuotations();
    print('   💰 عروض أسعار uPVC: ${quotations.length} عرض سعر');
    
    final hingeDesigns = await upvcService.getUpvcHingeDesigns();
    print('   🚪 تصاميم المفصلي uPVC: ${hingeDesigns.length} تصميم');
    
    final slidingDesigns = await upvcService.getUpvcSlidingDesigns();
    print('   🪟 تصاميم السحاب uPVC: ${slidingDesigns.length} تصميم');
    
    // اختبار الإحصائيات
    final stats = await upvcService.getUpvcStatistics();
    print('   📊 إحصائيات uPVC:');
    print('      🔩 القطاعات: ${stats['profiles_count']}');
    print('      💰 عروض الأسعار: ${stats['quotations_count']}');
    print('      🎨 إجمالي التصاميم: ${stats['total_designs']}');
    
    // اختبار البحث
    if (profiles.isNotEmpty) {
      final searchResults = await upvcService.searchProfiles('uPVC');
      print('   🔍 نتائج البحث: ${searchResults.length} قطاع');
    }
    
    print('   ✅ خدمة uPVC الموحدة تعمل بشكل صحيح');
    
  } catch (e) {
    print('   ❌ خطأ في خدمة uPVC الموحدة: $e');
  }
}
