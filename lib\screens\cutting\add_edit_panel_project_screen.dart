import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/panel_cutting_project.dart';
import '../../models/panel_order_item.dart';

class AddEditPanelProjectScreen extends StatefulWidget {
  final PanelCuttingProject? project;

  const AddEditPanelProjectScreen({super.key, this.project});

  @override
  State<AddEditPanelProjectScreen> createState() => _AddEditPanelProjectScreenState();
}

class _AddEditPanelProjectScreenState extends State<AddEditPanelProjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final _projectNameController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _panelWidthController = TextEditingController();
  final _panelHeightController = TextEditingController();
  final _notesController = TextEditingController();
  List<PanelOrderItem> _orderItems = [];
  double _panelWidth = 280.0;
  double _panelHeight = 140.0;

  @override
  void initState() {
    super.initState();
    if (widget.project != null) {
      _loadProjectData();
    } else {
      _addNewItem();
    }
  }  void _loadProjectData() {
    final project = widget.project!;
    _projectNameController.text = project.projectName;
    _customerNameController.text = project.customerName;
    _panelWidthController.text = project.panelWidth.toString();
    _panelHeightController.text = project.panelHeight.toString();
    _notesController.text = project.notes ?? '';
    _panelWidth = project.panelWidth;
    _panelHeight = project.panelHeight;
    _orderItems = List.from(project.orderItems);
  }
  void _addNewItem() {
    setState(() {
      _orderItems.add(PanelOrderItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        label: 'قطعة ${_orderItems.length + 1}',
        width: 50.0,
        height: 30.0,
        quantity: 1,
        material: PanelMaterial.wood,
      ));
    });
  }

  void _removeItem(int index) {
    setState(() {
      _orderItems.removeAt(index);
    });
  }

  void _updatePanelDimensions() {
    setState(() {
      _panelWidth = double.tryParse(_panelWidthController.text) ?? 280.0;
      _panelHeight = double.tryParse(_panelHeightController.text) ?? 140.0;
    });
  }

  Widget _buildPanelDimensionsCard() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.dashboard, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'مقاسات اللوح الأساسي',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _panelWidthController,
                    decoration: const InputDecoration(
                      labelText: 'عرض اللوح (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.width_full),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال عرض اللوح';
                      }
                      final width = double.tryParse(value);
                      if (width == null || width <= 0) {
                        return 'يرجى إدخال عرض صحيح';
                      }
                      return null;
                    },
                    onChanged: (value) => _updatePanelDimensions(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _panelHeightController,
                    decoration: const InputDecoration(
                      labelText: 'ارتفاع اللوح (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.height),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال ارتفاع اللوح';
                      }
                      final height = double.tryParse(value);
                      if (height == null || height <= 0) {
                        return 'يرجى إدخال ارتفاع صحيح';
                      }
                      return null;
                    },
                    onChanged: (value) => _updatePanelDimensions(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'مساحة اللوح: ${(_panelWidth * _panelHeight / 10000).toStringAsFixed(2)} م²',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemsSection() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.list_alt, color: Colors.orange),
                    SizedBox(width: 8),
                    Text(
                      'القطع المطلوبة',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                ElevatedButton.icon(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة قطعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_orderItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(32),
                alignment: Alignment.center,
                child: const Column(
                  children: [
                    Icon(Icons.inbox, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد قطع مضافة',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _orderItems.length,
                itemBuilder: (context, index) => _buildOrderItemCard(index),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemCard(int index) {
    final item = _orderItems[index];
    final widthController = TextEditingController(text: item.width.toString());
    final heightController = TextEditingController(text: item.height.toString());
    final quantityController = TextEditingController(text: item.quantity.toString());
    final labelController = TextEditingController(text: item.label);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'قطعة ${index + 1}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: () => _removeItem(index),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف القطعة',
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: labelController,
              decoration: const InputDecoration(
                labelText: 'اسم القطعة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              onChanged: (value) {
                setState(() {
                  _orderItems[index] = _orderItems[index].copyWith(label: value);
                });
              },
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: widthController,
                    decoration: const InputDecoration(
                      labelText: 'العرض (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.width_full),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final width = double.tryParse(value);
                      if (width == null || width <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      final width = double.tryParse(value) ?? 0;
                      setState(() {
                        _orderItems[index] = _orderItems[index].copyWith(width: width);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: heightController,
                    decoration: const InputDecoration(
                      labelText: 'الارتفاع (سم)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.height),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*'))],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final height = double.tryParse(value);
                      if (height == null || height <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      final height = double.tryParse(value) ?? 0;
                      setState(() {
                        _orderItems[index] = _orderItems[index].copyWith(height: height);
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                SizedBox(
                  width: 100,
                  child: TextFormField(
                    controller: quantityController,
                    decoration: const InputDecoration(
                      labelText: 'الكمية',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'مطلوب';
                      }
                      final quantity = int.tryParse(value);
                      if (quantity == null || quantity <= 0) {
                        return 'قيمة غير صحيحة';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      final quantity = int.tryParse(value) ?? 1;
                      setState(() {
                        _orderItems[index] = _orderItems[index].copyWith(quantity: quantity);
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<PanelMaterial>(
              value: item.material,
              decoration: const InputDecoration(
                labelText: 'نوع المادة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
              items: PanelMaterial.values.map((material) {
                return DropdownMenuItem(
                  value: material,
                  child: Text(material.arabicName),
                );
              }).toList(),
              onChanged: (material) {
                if (material != null) {
                  setState(() {
                    _orderItems[index] = _orderItems[index].copyWith(material: material);
                  });
                }
              },
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calculate, color: Colors.blue, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'المساحة الإجمالية: ${(item.totalArea / 10000).toStringAsFixed(4)} م²',
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  Future<void> _saveProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_orderItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة قطعة واحدة على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Update panel dimensions
    _updatePanelDimensions();

    final now = DateTime.now();
    final project = PanelCuttingProject(
      id: widget.project?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      projectName: _projectNameController.text.trim(),
      customerName: _customerNameController.text.trim(),
      panelWidth: _panelWidth,
      panelHeight: _panelHeight,
      orderItems: _orderItems,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      createdAt: widget.project?.createdAt ?? now,
      updatedAt: now,
    );

    // TODO: حفظ في قاعدة البيانات
    debugPrint('Project saved: ${project.toMap()}');

    if (mounted) {
      Navigator.of(context).pop(true);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ المشروع بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.project == null ? 'مشروع تقطيع ألواح جديد' : 'تعديل مشروع الألواح',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            onPressed: _saveProject,
            icon: const Icon(Icons.save),
            tooltip: 'حفظ المشروع',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          children: [
            // معلومات المشروع الأساسية
            Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.info, color: Colors.orange),
                        SizedBox(width: 8),
                        Text(
                          'معلومات المشروع',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _projectNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المشروع',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.work),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال اسم المشروع';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _customerNameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم العميل',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال اسم العميل';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.note),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            // مقاسات اللوح
            _buildPanelDimensionsCard(),

            // القطع المطلوبة
            _buildOrderItemsSection(),

            const SizedBox(height: 20),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _saveProject,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.save),
        label: const Text('حفظ المشروع'),
      ),
    );
  }

  @override
  void dispose() {
    _projectNameController.dispose();
    _customerNameController.dispose();
    _panelWidthController.dispose();
    _panelHeightController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
