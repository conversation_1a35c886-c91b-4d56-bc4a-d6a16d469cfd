import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

abstract class AppLocalizations {
  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    Locale('en'),
  ];

  // App Info
  String get appTitle;
  String get appSubtitle;
  String get home;
  String get settings;
  String get about;
  String get version;
  String get aboutDescription;
  String get aboutCopyright;

  // Common Actions
  String get cancel;
  String get save;
  String get delete;
  String get edit;
  String get add;
  String get create;
  String get update;
  String get confirm;
  String get back;
  String get ok;
  String get comingSoon;
  String get comingSoonMessage;

  // Services
  String get availableServices;
  String get searchHint;
  String get noServicesFound;

  // Treasury
  String get treasury;
  String get treasuryDesc;
  String get treasuryName;
  String get enterTreasuryName;
  String get previousBalance;
  String get currentBalance;
  String get finalBalance;
  String get income;
  String get expenses;
  String get balance;
  String get totalIncome;
  String get totalExpenses;
  String get noTreasuriesFound;
  String get treasurySummary;
  String get treasurySummaryDesc;
  String get overallSummary;
  String get totalBalance;
  String get incomeVsExpenses;
  String get treasuryBalances;
  String get treasuries;
  String get noTreasuriesMatchSearch;
  String get searchTreasury;
  String get addTreasury;
  String get editTreasury;
  String get deleteTreasury;
  String get treasuryNameRequired;
  String get initialBalance;
  String get noItems;
  String get charts;
  String deleteTreasuryConfirmation(String name);
  String treasuryDeleted(String name);

  // Treasury Transactions
  String get transactions;
  String get noTransactions;
  String get addTransaction;
  String get editTransaction;
  String get deleteTransaction;
  String get deleteTransactionConfirmation;
  String get transactionAdded;
  String get transactionUpdated;
  String get transactionDeleted;
  String get description;
  String get enterDescription;
  String get descriptionRequired;
  String get notes;
  String get enterNotes;
  String get dateRequired;
  String get date;
  String get incomeOrExpensesRequired;
  String get reset;

  // Daily Summary
  String get todaySummary;
  String get recentTransactions;
  String get net;
  String errorLoadingData(String error);

  // Aluminum Quotations
  String get aluminumQuotations;
  String get aluminumQuotationsDesc;
  String get addQuotation;
  String get editQuotation;
  String get quotationNumber;
  String get quotationDate;
  String get clientName;
  String get clientPhone;
  String get clientAddress;
  String get quotationNotes;
  String get quotationItems;
  String get addQuotationItem;
  String get windowType;
  String get doorType;
  String get itemWidth;
  String get itemHeight;
  String get itemQuantity;
  String get itemNotes;
  String get hingeDoor;
  String get slidingDoor;
  String get hingeWindow;
  String get slidingWindow;
  String get fixedWindow;
}

class AppLocalizationsAr extends AppLocalizations {
  @override
  String get appTitle => 'المساعد الذكي';

  @override
  String get appSubtitle => 'لخدمات المطابخ والألومنيوم';

  @override
  String get home => 'الرئيسية';

  @override
  String get settings => 'الإعدادات';

  @override
  String get about => 'حول التطبيق';

  @override
  String get version => 'الإصدار 1.0.0';

  @override
  String get aboutDescription => 'تطبيق المساعد الذكي لخدمات المطابخ والألومنيوم';

  @override
  String get aboutCopyright => 'تم التطوير بواسطة Uptime Smart Solutions © 2025';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get create => 'إنشاء';

  @override
  String get update => 'تحديث';

  @override
  String get confirm => 'تأكيد';

  @override
  String get back => 'رجوع';

  @override
  String get ok => 'حسناً';

  @override
  String get comingSoon => 'قريباً';

  @override
  String get comingSoonMessage => 'هذه الخدمة قيد التطوير وستكون متاحة قريباً';

  @override
  String get availableServices => 'الخدمات المتاحة';

  @override
  String get searchHint => 'ابحث عن خدمة...';

  @override
  String get noServicesFound => 'لا توجد خدمات تطابق بحثك';

  @override
  String get treasury => 'الخزينة';

  @override
  String get treasuryDesc => 'إدارة الخزينة والمعاملات المالية';

  @override
  String get treasuryName => 'اسم الخزينة';

  @override
  String get enterTreasuryName => 'أدخل اسم الخزينة';

  @override
  String get previousBalance => 'الرصيد السابق';

  @override
  String get currentBalance => 'الرصيد الحالي';

  @override
  String get finalBalance => 'الرصيد الختامي';

  @override
  String get income => 'وارد';

  @override
  String get expenses => 'صادر';

  @override
  String get balance => 'الرصيد';

  @override
  String get totalIncome => 'إجمالي الوارد';

  @override
  String get totalExpenses => 'إجمالي الصادر';

  @override
  String get noTreasuriesFound => 'لا توجد خزائن';

  @override
  String get treasurySummary => 'ملخص الخزينة';

  @override
  String get treasurySummaryDesc => 'إحصائيات ورسوم بيانية للخزينة';

  @override
  String get overallSummary => 'الملخص العام';

  @override
  String get totalBalance => 'إجمالي الرصيد';

  @override
  String get incomeVsExpenses => 'الوارد مقابل الصادر';

  @override
  String get treasuryBalances => 'أرصدة الخزائن';

  @override
  String get treasuries => 'الخزائن';

  @override
  String get noTreasuriesMatchSearch => 'لا توجد خزائن تطابق بحثك';

  @override
  String get searchTreasury => 'بحث في الخزائن';

  @override
  String get addTreasury => 'إضافة خزينة';

  @override
  String get editTreasury => 'تعديل الخزينة';

  @override
  String get deleteTreasury => 'حذف الخزينة';

  @override
  String get treasuryNameRequired => 'اسم الخزينة مطلوب';

  @override
  String get initialBalance => 'الرصيد الأولي';

  @override
  String get noItems => 'لا توجد عناصر';

  @override
  String get charts => 'الرسوم البيانية';

  @override
  String deleteTreasuryConfirmation(String name) => 'هل أنت متأكد من حذف الخزينة "$name"؟';

  @override
  String treasuryDeleted(String name) => 'تم حذف الخزينة "$name" بنجاح';

  @override
  String get transactions => 'المعاملات';

  @override
  String get noTransactions => 'لا توجد معاملات';

  @override
  String get addTransaction => 'إضافة معاملة';

  @override
  String get editTransaction => 'تعديل المعاملة';

  @override
  String get deleteTransaction => 'حذف المعاملة';

  @override
  String get deleteTransactionConfirmation => 'هل أنت متأكد من حذف هذه المعاملة؟';

  @override
  String get transactionAdded => 'تم إضافة المعاملة بنجاح';

  @override
  String get transactionUpdated => 'تم تحديث المعاملة بنجاح';

  @override
  String get transactionDeleted => 'تم حذف المعاملة بنجاح';

  @override
  String get description => 'البيان';

  @override
  String get enterDescription => 'أدخل البيان';

  @override
  String get descriptionRequired => 'البيان مطلوب';

  @override
  String get notes => 'ملاحظات';

  @override
  String get enterNotes => 'أدخل الملاحظات';

  @override
  String get dateRequired => 'التاريخ مطلوب';

  @override
  String get date => 'التاريخ';

  @override
  String get incomeOrExpensesRequired => 'يجب إدخال وارد أو صادر';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get todaySummary => 'ملخص اليوم';

  @override
  String get recentTransactions => 'المعاملات الأخيرة';

  @override
  String get net => 'الصافي';

  @override
  String errorLoadingData(String error) => 'خطأ في تحميل البيانات: $error';

  @override
  String get aluminumQuotations => 'مقايسات الألومنيوم';

  @override
  String get aluminumQuotationsDesc => 'إنشاء وإدارة مقايسات الألومنيوم';

  @override
  String get addQuotation => 'إضافة مقايسة';

  @override
  String get editQuotation => 'تعديل مقايسة';

  @override
  String get quotationNumber => 'رقم المقايسة';

  @override
  String get quotationDate => 'تاريخ المقايسة';

  @override
  String get clientName => 'اسم العميل';

  @override
  String get clientPhone => 'هاتف العميل';

  @override
  String get clientAddress => 'عنوان العميل';

  @override
  String get quotationNotes => 'ملاحظات المقايسة';

  @override
  String get quotationItems => 'بنود المقايسة';

  @override
  String get addQuotationItem => 'إضافة بند';

  @override
  String get windowType => 'نوع الشباك';

  @override
  String get doorType => 'نوع الباب';

  @override
  String get itemWidth => 'العرض';

  @override
  String get itemHeight => 'الارتفاع';

  @override
  String get itemQuantity => 'الكمية';

  @override
  String get itemNotes => 'ملاحظات البند';

  @override
  String get hingeDoor => 'باب مفصلي';

  @override
  String get slidingDoor => 'باب سحاب';

  @override
  String get hingeWindow => 'شباك مفصلي';

  @override
  String get slidingWindow => 'شباك سحاب';

  @override
  String get fixedWindow => 'شباك ثابت';
}

class AppLocalizationsEn extends AppLocalizations {
  @override
  String get appTitle => 'Smart Assistant';

  @override
  String get appSubtitle => 'Kitchen & Aluminum Services';

  @override
  String get home => 'Home';

  @override
  String get settings => 'Settings';

  @override
  String get about => 'About';

  @override
  String get version => 'Version 1.0.0';

  @override
  String get aboutDescription => 'Smart Assistant App for Kitchen and Aluminum Services';

  @override
  String get aboutCopyright => 'Developed by Uptime Smart Solutions © 2025';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get create => 'Create';

  @override
  String get update => 'Update';

  @override
  String get confirm => 'Confirm';

  @override
  String get back => 'Back';

  @override
  String get ok => 'OK';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get comingSoonMessage => 'This service is under development and will be available soon';

  @override
  String get availableServices => 'Available Services';

  @override
  String get searchHint => 'Search for a service...';

  @override
  String get noServicesFound => 'No services found';

  @override
  String get treasury => 'Treasury';

  @override
  String get treasuryDesc => 'Manage treasury and financial transactions';

  @override
  String get treasuryName => 'Treasury Name';

  @override
  String get enterTreasuryName => 'Enter treasury name';

  @override
  String get previousBalance => 'Previous Balance';

  @override
  String get currentBalance => 'Current Balance';

  @override
  String get finalBalance => 'Final Balance';

  @override
  String get income => 'Income';

  @override
  String get expenses => 'Expenses';

  @override
  String get balance => 'Balance';

  @override
  String get totalIncome => 'Total Income';

  @override
  String get totalExpenses => 'Total Expenses';

  @override
  String get noTreasuriesFound => 'No treasuries found';

  @override
  String get treasurySummary => 'Treasury Summary';

  @override
  String get treasurySummaryDesc => 'Statistics and charts for treasury';

  @override
  String get overallSummary => 'Overall Summary';

  @override
  String get totalBalance => 'Total Balance';

  @override
  String get incomeVsExpenses => 'Income vs Expenses';

  @override
  String get treasuryBalances => 'Treasury Balances';

  @override
  String get treasuries => 'Treasuries';

  @override
  String get noTreasuriesMatchSearch => 'No treasuries match your search';

  @override
  String get searchTreasury => 'Search treasuries';

  @override
  String get addTreasury => 'Add Treasury';

  @override
  String get editTreasury => 'Edit Treasury';

  @override
  String get deleteTreasury => 'Delete Treasury';

  @override
  String get treasuryNameRequired => 'Treasury name is required';

  @override
  String get initialBalance => 'Initial Balance';

  @override
  String get noItems => 'No items';

  @override
  String get charts => 'Charts';

  @override
  String deleteTreasuryConfirmation(String name) => 'Are you sure you want to delete treasury "$name"?';

  @override
  String treasuryDeleted(String name) => 'Treasury "$name" has been deleted successfully';

  @override
  String get transactions => 'Transactions';

  @override
  String get noTransactions => 'No transactions';

  @override
  String get addTransaction => 'Add Transaction';

  @override
  String get editTransaction => 'Edit Transaction';

  @override
  String get deleteTransaction => 'Delete Transaction';

  @override
  String get deleteTransactionConfirmation => 'Are you sure you want to delete this transaction?';

  @override
  String get transactionAdded => 'Transaction added successfully';

  @override
  String get transactionUpdated => 'Transaction updated successfully';

  @override
  String get transactionDeleted => 'Transaction deleted successfully';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Enter description';

  @override
  String get descriptionRequired => 'Description is required';

  @override
  String get notes => 'Notes';

  @override
  String get enterNotes => 'Enter notes';

  @override
  String get dateRequired => 'Date is required';

  @override
  String get date => 'Date';

  @override
  String get incomeOrExpensesRequired => 'Income or expenses must be entered';

  @override
  String get reset => 'Reset';

  @override
  String get todaySummary => "Today's Summary";

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get net => 'Net';

  @override
  String errorLoadingData(String error) => 'Error loading data: $error';

  @override
  String get aluminumQuotations => 'Aluminum Quotations';

  @override
  String get aluminumQuotationsDesc => 'Create and manage aluminum quotations';

  @override
  String get addQuotation => 'Add Quotation';

  @override
  String get editQuotation => 'Edit Quotation';

  @override
  String get quotationNumber => 'Quotation Number';

  @override
  String get quotationDate => 'Quotation Date';

  @override
  String get clientName => 'Client Name';

  @override
  String get clientPhone => 'Client Phone';

  @override
  String get clientAddress => 'Client Address';

  @override
  String get quotationNotes => 'Quotation Notes';

  @override
  String get quotationItems => 'Quotation Items';

  @override
  String get addQuotationItem => 'Add Item';

  @override
  String get windowType => 'Window Type';

  @override
  String get doorType => 'Door Type';

  @override
  String get itemWidth => 'Width';

  @override
  String get itemHeight => 'Height';

  @override
  String get itemQuantity => 'Quantity';

  @override
  String get itemNotes => 'Item Notes';

  @override
  String get hingeDoor => 'Hinge Door';

  @override
  String get slidingDoor => 'Sliding Door';

  @override
  String get hingeWindow => 'Hinge Window';

  @override
  String get slidingWindow => 'Sliding Window';

  @override
  String get fixedWindow => 'Fixed Window';
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'ar'),
      Locale.fromSubtags(languageCode: 'en'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<AppLocalizations> load(Locale locale) => _load(locale);
  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

bool _isSupported(Locale locale) {
  for (var supportedLocale in _AppLocalizationsDelegate().supportedLocales) {
    if (supportedLocale.languageCode == locale.languageCode) {
      return true;
    }
  }
  return false;
}

Future<AppLocalizations> _load(Locale locale) {
  switch (locale.languageCode) {
    case 'ar':
      return Future.value(AppLocalizationsAr());
    case 'en':
      return Future.value(AppLocalizationsEn());
    default:
      return Future.value(AppLocalizationsAr());
  }
}
