// نماذج التخصيمات لـ uPVC

class UpvcHingeDesign {
  final int? id;
  final int? seriesId; // ربط بمجموعة القطاعات
  final double? dalfaOnHalaf; // ركوب الضلفة على الحلق
  final double? maradDalfaComplete; // تخصيم المرد الضلفة بالكامل
  final double? maradDalfaWithKaab; // تخصيم المرد للضلفة بكعب
  final double? maradBetweenDalfa; // تخصيم بين الضلفتين (المرد)
  final double? dalfaFromGround; // تخصيم الضلفة من الارض فى حالة ابواب بكعب
  final double? dalfaGlass; // تخصيم زجاج الضلف
  final double? fixedGlass; // تخصيم زجاج الثابت
  final double? movingSilk; // تخصيم السلك المتحرك
  final double? fixedSilk; // تخصيم السلك الثابت
  final DateTime createdAt;
  final DateTime updatedAt;

  UpvcHingeDesign({
    this.id,
    this.seriesId,
    this.dalfaOnHalaf,
    this.maradDalfaComplete,
    this.maradDalfaWithKaab,
    this.maradBetweenDalfa,
    this.dalfaFromGround,
    this.dalfaGlass,
    this.fixedGlass,
    this.movingSilk,
    this.fixedSilk,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'series_id': seriesId,
      'dalfa_on_halaf': dalfaOnHalaf,
      'marad_dalfa_complete': maradDalfaComplete,
      'marad_dalfa_with_kaab': maradDalfaWithKaab,
      'marad_between_dalfa': maradBetweenDalfa,
      'dalfa_from_ground': dalfaFromGround,
      'dalfa_glass': dalfaGlass,
      'fixed_glass': fixedGlass,
      'moving_silk': movingSilk,
      'fixed_silk': fixedSilk,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcHingeDesign.fromMap(Map<String, dynamic> map) {
    return UpvcHingeDesign(
      id: map['id']?.toInt(),
      seriesId: map['series_id']?.toInt(),
      dalfaOnHalaf: map['dalfa_on_halaf']?.toDouble(),
      maradDalfaComplete: map['marad_dalfa_complete']?.toDouble(),
      maradDalfaWithKaab: map['marad_dalfa_with_kaab']?.toDouble(),
      maradBetweenDalfa: map['marad_between_dalfa']?.toDouble(),
      dalfaFromGround: map['dalfa_from_ground']?.toDouble(),
      dalfaGlass: map['dalfa_glass']?.toDouble(),
      fixedGlass: map['fixed_glass']?.toDouble(),
      movingSilk: map['moving_silk']?.toDouble(),
      fixedSilk: map['fixed_silk']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }
}

// عدد الضلف للسحاب
enum UpvcDalfaCount {
  two(2),
  three(3),
  four(4),
  six(6);

  const UpvcDalfaCount(this.count);
  final int count;

  String get arabicName {
    switch (this) {
      case UpvcDalfaCount.two:
        return 'ضلفتين';
      case UpvcDalfaCount.three:
        return 'ثلاث ضلف';
      case UpvcDalfaCount.four:
        return 'أربع ضلف';
      case UpvcDalfaCount.six:
        return 'ست ضلف';
    }
  }
}

// طريقة الضلف للسحاب
enum UpvcDalfaMethod {
  degrees45(45),
  degrees90(90);

  const UpvcDalfaMethod(this.angle);
  final int angle;

  String get arabicName {
    switch (this) {
      case UpvcDalfaMethod.degrees45:
        return '45 درجة';
      case UpvcDalfaMethod.degrees90:
        return '90 درجة';
    }
  }
}

class UpvcSlidingDesign {
  final int? id;
  final int? seriesId; // ربط بمجموعة القطاعات
  final UpvcDalfaCount dalfaCount; // عدد الضلف
  final UpvcDalfaMethod dalfaMethod; // طريقة الضلف
  final double? dalfaWidthPlus; // تخصيم الضلف عرض (+)
  final double? dalfaHeightMinus; // تخصيم الضلف ارتفاع (-)
  final double? skinehHeightMinus; // تخصيم السكينة ارتفاع (-)
  final double? silkWidthPlus; // تخصيم السلك عرض (+)
  final double? silkHeightMinus; // تخصيم السلك ارتفاع (-)
  final double? glassWidthMinus; // تخصيم الزجاج عرض (-)
  final double? glassHeightMinus; // تخصيم الزجاج ارتفاع (-)
  final DateTime createdAt;
  final DateTime updatedAt;

  UpvcSlidingDesign({
    this.id,
    this.seriesId,
    required this.dalfaCount,
    required this.dalfaMethod,
    this.dalfaWidthPlus,
    this.dalfaHeightMinus,
    this.skinehHeightMinus,
    this.silkWidthPlus,
    this.silkHeightMinus,
    this.glassWidthMinus,
    this.glassHeightMinus,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'series_id': seriesId,
      'dalfa_count': dalfaCount.count,
      'dalfa_method': dalfaMethod.angle,
      'dalfa_width_plus': dalfaWidthPlus,
      'dalfa_height_minus': dalfaHeightMinus,
      'skineh_height_minus': skinehHeightMinus,
      'silk_width_plus': silkWidthPlus,
      'silk_height_minus': silkHeightMinus,
      'glass_width_minus': glassWidthMinus,
      'glass_height_minus': glassHeightMinus,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcSlidingDesign.fromMap(Map<String, dynamic> map) {
    return UpvcSlidingDesign(
      id: map['id']?.toInt(),
      seriesId: map['series_id']?.toInt(),
      dalfaCount: UpvcDalfaCount.values.firstWhere(
        (e) => e.count == map['dalfa_count'],
        orElse: () => UpvcDalfaCount.two,
      ),
      dalfaMethod: UpvcDalfaMethod.values.firstWhere(
        (e) => e.angle == map['dalfa_method'],
        orElse: () => UpvcDalfaMethod.degrees45,
      ),
      dalfaWidthPlus: map['dalfa_width_plus']?.toDouble(),
      dalfaHeightMinus: map['dalfa_height_minus']?.toDouble(),
      skinehHeightMinus: map['skineh_height_minus']?.toDouble(),
      silkWidthPlus: map['silk_width_plus']?.toDouble(),
      silkHeightMinus: map['silk_height_minus']?.toDouble(),
      glassWidthMinus: map['glass_width_minus']?.toDouble(),
      glassHeightMinus: map['glass_height_minus']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }
}

// نموذج مجموعة قطاعات uPVC
class UpvcProfileSeries {
  final int? id;
  final String name;
  final String description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  UpvcProfileSeries({
    this.id,
    required this.name,
    this.description = '',
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcProfileSeries.fromMap(Map<String, dynamic> map) {
    return UpvcProfileSeries(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  UpvcProfileSeries copyWith({
    int? id,
    String? name,
    String? description,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UpvcProfileSeries(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
