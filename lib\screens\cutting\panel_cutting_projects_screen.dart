import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/panel_cutting_project.dart';
import 'add_edit_panel_project_screen.dart';
import 'panel_project_view_screen.dart';

class PanelCuttingProjectsScreen extends StatefulWidget {
  const PanelCuttingProjectsScreen({super.key});

  @override
  State<PanelCuttingProjectsScreen> createState() => _PanelCuttingProjectsScreenState();
}

class _PanelCuttingProjectsScreenState extends State<PanelCuttingProjectsScreen> {
  List<PanelCuttingProject> _projects = [];
  List<PanelCuttingProject> _filteredProjects = [];
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProjects();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
        _filterProjects();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Load from database when implemented
      await _loadSampleData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المشاريع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  Future<void> _loadSampleData() async {
    // Sample data for testing
    _projects = [
      PanelCuttingProject(
        id: '1',
        projectName: 'PNL-001',
        customerName: 'أحمد محمد العلي',
        panelWidth: 2440,
        panelHeight: 1220,
        orderItems: [],
        notes: 'مشروع تقطيع ألواح MDF للمطبخ الجديد',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      PanelCuttingProject(
        id: '2',
        projectName: 'PNL-002',
        customerName: 'سارة أحمد الخالد',
        panelWidth: 2440,
        panelHeight: 1220,
        orderItems: [],
        notes: 'تقطيع ألواح زجاج للمكتب',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PanelCuttingProject(
        id: '3',
        projectName: 'PNL-003',
        customerName: 'محمد علي السعد',
        panelWidth: 2440,
        panelHeight: 1220,
        orderItems: [],
        notes: 'ألواح فيبر للديكور',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
    _filterProjects();
  }
  void _filterProjects() {
    if (_searchQuery.isEmpty) {
      _filteredProjects = List.from(_projects);
    } else {
      _filteredProjects = _projects.where((project) {
        return project.projectName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               project.customerName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (project.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مشاريع تقطيع الألواح'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewProject,
            tooltip: 'إضافة مشروع جديد',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadProjects,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar and stats
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Search field
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن مشروع...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                  ),
                ),
                const SizedBox(height: 16),

                // Quick stats
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'إجمالي المشاريع',
                        _projects.length.toString(),
                        Icons.assignment,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 8),                    Expanded(
                      child: _buildStatCard(
                        'مشاريع اليوم',
                        _projects.where((p) =>
                          p.createdAt.day == DateTime.now().day &&
                          p.createdAt.month == DateTime.now().month &&
                          p.createdAt.year == DateTime.now().year
                        ).length.toString(),
                        Icons.today,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'مشاريع حديثة',
                        _projects.where((p) =>
                          p.createdAt.difference(DateTime.now()).inDays >= -7
                        ).length.toString(),
                        Icons.schedule,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Projects list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredProjects.isEmpty
                    ? _buildEmptyState()
                    : _buildProjectsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 11),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.content_cut,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'لا توجد مشاريع تطابق بحثك'
                : 'لا توجد مشاريع تقطيع ألواح',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          if (_searchQuery.isEmpty)
            ElevatedButton.icon(
              onPressed: _addNewProject,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مشروع جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProjectsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredProjects.length,
      itemBuilder: (context, index) {
        final project = _filteredProjects[index];
        return _buildProjectCard(project);
      },
    );
  }
  Widget _buildProjectCard(PanelCuttingProject project) {
    final isRecent = project.createdAt.difference(DateTime.now()).inDays >= -3;
    final isToday = project.createdAt.day == DateTime.now().day &&
                   project.createdAt.month == DateTime.now().month &&
                   project.createdAt.year == DateTime.now().year;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isRecent ? const BorderSide(color: Colors.orange, width: 2) : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => _openProject(project),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.view_module,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [                        Text(
                          project.projectName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          project.customerName,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isToday)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                      ),
                      child: const Text(
                        'اليوم',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (isRecent && !isToday) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                      ),
                      child: const Text(
                        'عاجل',
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),
                Text(
                project.notes ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'تاريخ الإنشاء: ${DateFormat('dd/MM/yyyy').format(project.createdAt)}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              Row(
                children: [
                  Icon(Icons.straighten, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'أبعاد اللوح: ${project.panelWidth.toInt()} × ${project.panelHeight.toInt()} مم',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  const Spacer(),
                  Text(
                    '${project.orderItems.length} قطعة',
                    style: TextStyle(
                      fontSize: 12,
                      color: isRecent ? Colors.orange : Colors.grey[600],
                      fontWeight: isRecent ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              Row(
                children: [
                  Icon(Icons.update, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'آخر تحديث: ${DateFormat('dd/MM/yyyy').format(project.updatedAt)}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addNewProject() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditPanelProjectScreen(),
      ),
    ).then((result) {
      if (result == true) {
        _loadProjects();
      }
    });
  }

  void _openProject(PanelCuttingProject project) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PanelProjectViewScreen(project: project),
      ),
    ).then((result) {
      if (result == true) {
        _loadProjects();
      }
    });
  }
}
