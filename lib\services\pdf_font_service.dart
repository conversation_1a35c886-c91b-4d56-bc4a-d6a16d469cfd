import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class PdfFontService {
  static pw.Font? _arabicFont;
  static pw.Font? _arabicFontBold;
  static bool _fontsLoaded = false;

  /// Load Arabic fonts with better number and symbol support
  static Future<void> loadFonts() async {
    if (_fontsLoaded) return;

    try {
      // Try Cairo font first (best for Arabic + English)
      await _loadCairoFont();
    } catch (e) {
      try {
        await _loadNotoSansArabic();
      } catch (e2) {
        try {
          await _loadAmiriFont();
        } catch (e3) {
          // Fallback to default fonts
          await _loadDefaultFonts();
        }
      }
    }

    _fontsLoaded = true;
  }

  /// Load Noto Sans Arabic (best for numbers and symbols)
  static Future<void> _loadNotoSansArabic() async {
    _arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    _arabicFontBold = await PdfGoogleFonts.notoSansArabicBold();
  }

  /// Load Amiri font (good Arabic support)
  static Future<void> _loadAmiriFont() async {
    _arabicFont = await PdfGoogleFonts.amiriRegular();
    _arabicFontBold = await PdfGoogleFonts.amiriBold();
  }

  /// Load Cairo font (fallback)
  static Future<void> _loadCairoFont() async {
    _arabicFont = await PdfGoogleFonts.cairoRegular();
    _arabicFontBold = await PdfGoogleFonts.cairoBold();
  }

  /// Load default fonts (last resort)
  static Future<void> _loadDefaultFonts() async {
    _arabicFont = await PdfGoogleFonts.robotoRegular();
    _arabicFontBold = await PdfGoogleFonts.robotoBold();
  }

  /// Get regular Arabic font
  static pw.Font get arabicFont {
    if (!_fontsLoaded || _arabicFont == null) {
      throw Exception('Fonts not loaded. Call loadFonts() first.');
    }
    return _arabicFont!;
  }

  /// Get bold Arabic font
  static pw.Font get arabicFontBold {
    if (!_fontsLoaded || _arabicFontBold == null) {
      throw Exception('Fonts not loaded. Call loadFonts() first.');
    }
    return _arabicFontBold!;
  }

  /// Create PDF theme with loaded fonts
  static pw.ThemeData createTheme() {
    return pw.ThemeData.withFont(
      base: arabicFont,
      bold: arabicFontBold,
    );
  }

  /// Format number for PDF display (ensures proper number rendering)
  static String formatNumber(double number) {
    // Use English numerals to avoid font issues
    final formatted = number.toStringAsFixed(2);
    // Remove trailing zeros and decimal point if not needed
    final cleaned = formatted.replaceAll(RegExp(r'\.?0+$'), '');
    final result = cleaned.isEmpty ? '0' : cleaned;

    // Ensure English numerals
    return result
        .replaceAll('٠', '0')
        .replaceAll('١', '1')
        .replaceAll('٢', '2')
        .replaceAll('٣', '3')
        .replaceAll('٤', '4')
        .replaceAll('٥', '5')
        .replaceAll('٦', '6')
        .replaceAll('٧', '7')
        .replaceAll('٨', '8')
        .replaceAll('٩', '9');
  }

  /// Format percentage for PDF display (handles % symbol properly)
  static String formatPercentage(double percentage) {
    final formatted = formatNumber(percentage);
    return '$formatted%'; // Use standard % symbol
  }

  /// Format Arabic text for PDF display (ensures proper text rendering)
  static String formatArabicText(String text) {
    // Ensure proper Arabic text rendering
    return text
        .replaceAll('ي', 'ي')  // Normalize Ya
        .replaceAll('ك', 'ك')  // Normalize Kaf
        .replaceAll('ة', 'ة'); // Normalize Ta Marbuta
  }

  /// Format currency for PDF display
  static String formatCurrency(double amount, {String currency = 'ريال'}) {
    final formattedAmount = formatNumber(amount);
    return '$formattedAmount $currency';
  }

  /// Format date for PDF display
  static String formatDate(DateTime date) {
    return '${date.year}/${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}';
  }

  /// Create text style for headers
  static pw.TextStyle headerStyle({
    double fontSize = 24,
    PdfColor color = PdfColors.blue800,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: pw.FontWeight.bold,
      color: color,
      font: arabicFontBold,
    );
  }

  /// Create text style for body text
  static pw.TextStyle bodyStyle({
    double fontSize = 14,
    PdfColor color = PdfColors.black,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      color: color,
      font: arabicFont,
    );
  }

  /// Create text style for table headers
  static pw.TextStyle tableHeaderStyle({
    double fontSize = 12,
    PdfColor color = PdfColors.black,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: pw.FontWeight.bold,
      color: color,
      font: arabicFontBold,
    );
  }

  /// Create text style for table data
  static pw.TextStyle tableDataStyle({
    double fontSize = 11,
    PdfColor color = PdfColors.black,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      color: color,
      font: arabicFont,
    );
  }

  /// Create text style for amounts/numbers (optimized for symbols)
  static pw.TextStyle amountStyle({
    double fontSize = 14,
    PdfColor color = PdfColors.black,
    bool bold = false,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
      color: color,
      font: bold ? arabicFontBold : arabicFont,
    );
  }

  /// Create text style specifically for numbers and symbols
  static pw.TextStyle numberStyle({
    double fontSize = 12,
    PdfColor color = PdfColors.black,
    bool bold = false,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
      color: color,
      font: bold ? arabicFontBold : arabicFont,
      letterSpacing: 0.5, // Better spacing for numbers
    );
  }

  /// Test font rendering with sample text
  static Future<bool> testFontRendering() async {
    try {
      await loadFonts();

      // Create a test PDF to verify font rendering
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              children: [
                pw.Text(
                  'اختبار الخط العربي',
                  style: headerStyle(),
                ),
                pw.Text(
                  'الأرقام: 1234567890',
                  style: bodyStyle(),
                ),
                pw.Text(
                  'العملة: ${formatCurrency(1234.56)}',
                  style: amountStyle(),
                ),
                pw.Text(
                  'التاريخ: ${formatDate(DateTime.now())}',
                  style: bodyStyle(),
                ),
              ],
            );
          },
        ),
      );

      // Try to generate PDF bytes
      await pdf.save();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get font information for debugging
  static Map<String, dynamic> getFontInfo() {
    return {
      'fontsLoaded': _fontsLoaded,
      'arabicFontLoaded': _arabicFont != null,
      'arabicFontBoldLoaded': _arabicFontBold != null,
    };
  }

  /// Reset fonts (for testing different fonts)
  static void resetFonts() {
    _arabicFont = null;
    _arabicFontBold = null;
    _fontsLoaded = false;
  }

  /// Get available font options
  static List<String> getAvailableFonts() {
    return [
      'Noto Sans Arabic (Recommended)',
      'Amiri',
      'Cairo',
      'Roboto (Fallback)',
    ];
  }

  /// Load specific font by name
  static Future<void> loadSpecificFont(String fontName) async {
    resetFonts();

    switch (fontName.toLowerCase()) {
      case 'cairo':
        await _loadCairoFont();
        break;
      case 'noto':
      case 'noto sans arabic':
        await _loadNotoSansArabic();
        break;
      case 'amiri':
        await _loadAmiriFont();
        break;
      default:
        await _loadDefaultFonts();
    }

    _fontsLoaded = true;
  }

  /// Force reload fonts (useful when having display issues)
  static Future<void> forceReloadFonts() async {
    resetFonts();
    await loadFonts();
  }

  /// Create a comprehensive text style for PDF
  static pw.TextStyle createTextStyle({
    double fontSize = 12,
    bool bold = false,
    PdfColor color = PdfColors.black,
    bool isNumber = false,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: bold ? pw.FontWeight.bold : pw.FontWeight.normal,
      color: color,
      font: bold ? arabicFontBold : arabicFont,
    );
  }
}
