import 'package:flutter/foundation.dart';
import '../services/mysql_service.dart';
import '../models/aluminum_quotation.dart';
import '../models/aluminum_quotation_item.dart';
import '../services/unified_aluminum_service.dart';

/// خدمة مزامنة المقايسات للألومنيوم - نسخة محدثة للعمل مع قاعدة البيانات الموحدة
/// تشمل الجداول: aluminum_quotations, aluminum_quotation_items
class AluminumQuotationSyncService {
  static final MySQLService _mysql = MySQLService.instance;
  static final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  /// مزامنة جميع المقايسات
  static Future<Map<String, dynamic>> syncAllQuotations() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      // الحصول على المقايسات من قاعدة البيانات الموحدة
      final quotationsData = await _aluminumService.getAllQuotations();
      final quotations = quotationsData.map((data) => AluminumQuotation.fromMap(data)).toList();

      int uploadedCount = 0;
      int errorCount = 0;
      List<String> errors = [];

      for (final quotation in quotations) {
        try {
          // رفع المقايسة
          await _uploadQuotationToMySQL(quotation);
          
          // رفع عناصر المقايسة
          final itemsData = await _aluminumService.getQuotationItems(quotation.id!);
          final items = itemsData.map((data) => AluminumQuotationItem.fromMap(data)).toList();
          
          for (final item in items) {
            await _uploadQuotationItemToMySQL(item);
          }
          
          uploadedCount++;
        } catch (e) {
          errorCount++;
          errors.add('خطأ في مقايسة ${quotation.quotationNumber}: $e');
        }
      }

      return {
        'success': true,
        'uploaded_quotations': uploadedCount,
        'errors': errorCount,
        'error_details': errors,
        'timestamp': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ عام في المزامنة: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// رفع مقايسة واحدة إلى MySQL
  static Future<void> _uploadQuotationToMySQL(AluminumQuotation quotation) async {
    final quotationData = {
      'quotation_number': quotation.quotationNumber,
      'quotation_date': quotation.quotationDate.toIso8601String(),
      'client_name': quotation.clientName,
      'client_phone': quotation.clientPhone,
      'client_address': quotation.clientAddress,
      'notes': quotation.notes,
      'created_at': quotation.createdAt.toIso8601String(),
      'updated_at': quotation.updatedAt.toIso8601String(),
    };

    await _mysql.insertOrUpdate('aluminum_quotations', quotationData, 'quotation_number');
  }

  /// رفع عنصر مقايسة إلى MySQL
  static Future<void> _uploadQuotationItemToMySQL(AluminumQuotationItem item) async {
    final itemData = {
      'quotation_id': item.quotationId,
      'type': item.type,
      'profile_type': item.profileType,
      'series_id': item.seriesId,
      'series_name': item.seriesName,
      'sash_count': item.sashCount,
      'track_count': item.trackCount,
      'width': item.width,
      'height': item.height,
      'quantity': item.quantity,
      'notes': item.notes,
      'drawing_options': item.drawingOptions.toString(),
      'created_at': item.createdAt.toIso8601String(),
    };

    await _mysql.insertOrUpdate('aluminum_quotation_items', itemData, 'id');
  }

  /// تحميل المقايسات من MySQL
  static Future<Map<String, dynamic>> downloadAllQuotations() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      // تحميل المقايسات
      final quotationsResult = await _mysql.query('SELECT * FROM aluminum_quotations ORDER BY quotation_date DESC');
      int downloadedCount = 0;

      for (final quotationData in quotationsResult) {
        try {
          // تحويل البيانات إلى نموذج
          final quotation = AluminumQuotation(
            quotationNumber: quotationData['quotation_number'] as String,
            quotationDate: DateTime.parse(quotationData['quotation_date'] as String),
            clientName: quotationData['client_name'] as String,
            clientPhone: quotationData['client_phone'] as String? ?? '',
            clientAddress: quotationData['client_address'] as String? ?? '',
            notes: quotationData['notes'] as String? ?? '',
            createdAt: quotationData['created_at'] != null ? DateTime.parse(quotationData['created_at'] as String) : DateTime.now(),
            updatedAt: quotationData['updated_at'] != null ? DateTime.parse(quotationData['updated_at'] as String) : DateTime.now(),
          );

          // حفظ في قاعدة البيانات الموحدة
          await _aluminumService.insertQuotation(quotation.toMap());
          downloadedCount++;

        } catch (e) {
          debugPrint('خطأ في تحميل مقايسة: $e');
        }
      }

      return {
        'success': true,
        'downloaded_quotations': downloadedCount,
        'timestamp': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل المقايسات: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// الحصول على إحصائيات المزامنة
  static Future<Map<String, dynamic>> getSyncStatistics() async {
    try {
      // إحصائيات قاعدة البيانات الموحدة
      final localQuotations = await _aluminumService.getAllQuotations();
      
      // إحصائيات MySQL (إذا كان متاحاً)
      int remoteQuotations = 0;
      if (await _mysql.connect()) {
        final result = await _mysql.query('SELECT COUNT(*) as count FROM aluminum_quotations');
        remoteQuotations = result.isNotEmpty ? result.first['count'] as int : 0;
      }

      return {
        'success': true,
        'local_quotations': localQuotations.length,
        'remote_quotations': remoteQuotations,
        'sync_needed': localQuotations.length != remoteQuotations,
        'timestamp': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في الحصول على الإحصائيات: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
