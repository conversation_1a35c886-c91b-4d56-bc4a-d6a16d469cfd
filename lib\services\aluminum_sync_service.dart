import 'package:flutter/foundation.dart';
import '../services/mysql_service.dart';
import '../services/unified_aluminum_service.dart';

/// خدمة مزامنة بيانات الألومنيوم
class AluminumSyncService {
  static final MySQLService _mysql = MySQLService.instance;
  static final UnifiedAluminumService _aluminumService = UnifiedAluminumService();

  /// مزامنة جميع بيانات الألومنيوم
  static Future<Map<String, dynamic>> syncAll() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      final results = <String, dynamic>{
        'success': true,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // مزامنة السلاسل
      final seriesResult = await syncSeries();
      results['series'] = seriesResult;

      // مزامنة القطاعات
      final profilesResult = await syncProfiles();
      results['profiles'] = profilesResult;

      // مزامنة المقايسات
      final quotationsResult = await syncQuotations();
      results['quotations'] = quotationsResult;

      return results;

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ عام في المزامنة: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// مزامنة السلاسل
  static Future<Map<String, dynamic>> syncSeries() async {
    try {
      final seriesData = await _aluminumService.getAllSeries();
      int uploadedCount = 0;
      List<String> errors = [];

      for (final series in seriesData) {
        try {
          await _mysql.insertOrUpdate('profile_series', series, 'code');
          uploadedCount++;
        } catch (e) {
          errors.add('خطأ في سلسلة ${series['name']}: $e');
        }
      }

      return {
        'success': true,
        'uploaded': uploadedCount,
        'errors': errors,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في مزامنة السلاسل: $e',
      };
    }
  }

  /// مزامنة القطاعات
  static Future<Map<String, dynamic>> syncProfiles() async {
    try {
      final profilesData = await _aluminumService.getAllProfiles();
      int uploadedCount = 0;
      List<String> errors = [];

      for (final profile in profilesData) {
        try {
          await _mysql.insertOrUpdate('aluminum_profiles', profile, 'code');
          uploadedCount++;
        } catch (e) {
          errors.add('خطأ في قطاع ${profile['name']}: $e');
        }
      }

      return {
        'success': true,
        'uploaded': uploadedCount,
        'errors': errors,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في مزامنة القطاعات: $e',
      };
    }
  }

  /// مزامنة المقايسات
  static Future<Map<String, dynamic>> syncQuotations() async {
    try {
      final quotationsData = await _aluminumService.getAllQuotations();
      int uploadedCount = 0;
      List<String> errors = [];

      for (final quotation in quotationsData) {
        try {
          await _mysql.insertOrUpdate('aluminum_quotations', quotation, 'quotation_number');
          uploadedCount++;
        } catch (e) {
          errors.add('خطأ في مقايسة ${quotation['quotation_number']}: $e');
        }
      }

      return {
        'success': true,
        'uploaded': uploadedCount,
        'errors': errors,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في مزامنة المقايسات: $e',
      };
    }
  }

  /// تحميل البيانات من MySQL
  static Future<Map<String, dynamic>> downloadAll() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      final results = <String, dynamic>{
        'success': true,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تحميل السلاسل
      final seriesResult = await downloadSeries();
      results['series'] = seriesResult;

      // تحميل القطاعات
      final profilesResult = await downloadProfiles();
      results['profiles'] = profilesResult;

      // تحميل المقايسات
      final quotationsResult = await downloadQuotations();
      results['quotations'] = quotationsResult;

      return results;

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ عام في التحميل: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// تحميل السلاسل من MySQL
  static Future<Map<String, dynamic>> downloadSeries() async {
    try {
      final seriesData = await _mysql.query('SELECT * FROM profile_series ORDER BY name');
      int downloadedCount = 0;

      for (final series in seriesData) {
        try {
          await _aluminumService.insertSeries(series);
          downloadedCount++;
        } catch (e) {
          debugPrint('خطأ في تحميل سلسلة: $e');
        }
      }

      return {
        'success': true,
        'downloaded': downloadedCount,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل السلاسل: $e',
      };
    }
  }

  /// تحميل القطاعات من MySQL
  static Future<Map<String, dynamic>> downloadProfiles() async {
    try {
      final profilesData = await _mysql.query('SELECT * FROM aluminum_profiles ORDER BY name');
      int downloadedCount = 0;

      for (final profile in profilesData) {
        try {
          await _aluminumService.insertProfile(profile);
          downloadedCount++;
        } catch (e) {
          debugPrint('خطأ في تحميل قطاع: $e');
        }
      }

      return {
        'success': true,
        'downloaded': downloadedCount,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل القطاعات: $e',
      };
    }
  }

  /// تحميل المقايسات من MySQL
  static Future<Map<String, dynamic>> downloadQuotations() async {
    try {
      final quotationsData = await _mysql.query('SELECT * FROM aluminum_quotations ORDER BY quotation_date DESC');
      int downloadedCount = 0;

      for (final quotation in quotationsData) {
        try {
          await _aluminumService.insertQuotation(quotation);
          downloadedCount++;
        } catch (e) {
          debugPrint('خطأ في تحميل مقايسة: $e');
        }
      }

      return {
        'success': true,
        'downloaded': downloadedCount,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل المقايسات: $e',
      };
    }
  }

  /// مزامنة جميع بيانات الألومنيوم (اسم بديل)
  static Future<Map<String, dynamic>> syncAllAluminumData() async {
    return await syncAll();
  }

  /// مزامنة إعدادات القطاعات
  static Future<Map<String, dynamic>> syncSectorSettings() async {
    return await syncSeries();
  }

  /// رفع تصاميم المفصلي
  static Future<Map<String, dynamic>> uploadHingeDesigns() async {
    try {
      final hingeDesigns = await _aluminumService.getHingeDesigns();
      int uploadedCount = 0;
      List<String> errors = [];

      for (final design in hingeDesigns) {
        try {
          await _mysql.insertOrUpdate('hinge_designs', design, 'id');
          uploadedCount++;
        } catch (e) {
          errors.add('خطأ في تصميم مفصلي: $e');
        }
      }

      return {
        'success': true,
        'uploaded': uploadedCount,
        'errors': errors,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في رفع تصاميم المفصلي: $e',
      };
    }
  }

  /// رفع تصاميم السحاب
  static Future<Map<String, dynamic>> uploadSlidingDesigns() async {
    try {
      final slidingDesigns = await _aluminumService.getSlidingDesigns();
      int uploadedCount = 0;
      List<String> errors = [];

      for (final design in slidingDesigns) {
        try {
          await _mysql.insertOrUpdate('sliding_designs', design, 'id');
          uploadedCount++;
        } catch (e) {
          errors.add('خطأ في تصميم سحاب: $e');
        }
      }

      return {
        'success': true,
        'uploaded': uploadedCount,
        'errors': errors,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في رفع تصاميم السحاب: $e',
      };
    }
  }

  /// تحميل سلاسل القطاعات
  static Future<Map<String, dynamic>> downloadProfileSeries() async {
    return await downloadSeries();
  }

  /// تحميل قطاعات الألومنيوم
  static Future<Map<String, dynamic>> downloadAluminumProfiles() async {
    return await downloadProfiles();
  }

  /// تحميل تصاميم المفصلي
  static Future<Map<String, dynamic>> downloadHingeDesigns() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      final hingeDesignsResult = await _mysql.query('SELECT * FROM hinge_designs ORDER BY created_at DESC');
      int downloadedCount = 0;

      for (final design in hingeDesignsResult) {
        try {
          await _aluminumService.insertHingeDesign(design);
          downloadedCount++;
        } catch (e) {
          debugPrint('خطأ في تحميل تصميم مفصلي: $e');
        }
      }

      return {
        'success': true,
        'downloaded': downloadedCount,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل تصاميم المفصلي: $e',
      };
    }
  }

  /// تحميل تصاميم السحاب
  static Future<Map<String, dynamic>> downloadSlidingDesigns() async {
    try {
      if (!await _mysql.connect()) {
        return {'success': false, 'error': 'فشل الاتصال بقاعدة البيانات'};
      }

      final slidingDesignsResult = await _mysql.query('SELECT * FROM sliding_designs ORDER BY created_at DESC');
      int downloadedCount = 0;

      for (final design in slidingDesignsResult) {
        try {
          await _aluminumService.insertSlidingDesign(design);
          downloadedCount++;
        } catch (e) {
          debugPrint('خطأ في تحميل تصميم سحاب: $e');
        }
      }

      return {
        'success': true,
        'downloaded': downloadedCount,
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في تحميل تصاميم السحاب: $e',
      };
    }
  }

  /// الحصول على إحصائيات المزامنة
  static Future<Map<String, dynamic>> getSyncStatistics() async {
    try {
      // إحصائيات محلية
      final localSeries = await _aluminumService.getAllSeries();
      final localProfiles = await _aluminumService.getAllProfiles();
      final localQuotations = await _aluminumService.getAllQuotations();

      // إحصائيات MySQL (إذا كان متاحاً)
      int remoteSeries = 0;
      int remoteProfiles = 0;
      int remoteQuotations = 0;

      if (await _mysql.connect()) {
        final seriesResult = await _mysql.query('SELECT COUNT(*) as count FROM profile_series');
        remoteSeries = seriesResult.isNotEmpty ? seriesResult.first['count'] as int : 0;

        final profilesResult = await _mysql.query('SELECT COUNT(*) as count FROM aluminum_profiles');
        remoteProfiles = profilesResult.isNotEmpty ? profilesResult.first['count'] as int : 0;

        final quotationsResult = await _mysql.query('SELECT COUNT(*) as count FROM aluminum_quotations');
        remoteQuotations = quotationsResult.isNotEmpty ? quotationsResult.first['count'] as int : 0;
      }

      return {
        'success': true,
        'local': {
          'series': localSeries.length,
          'profiles': localProfiles.length,
          'quotations': localQuotations.length,
        },
        'remote': {
          'series': remoteSeries,
          'profiles': remoteProfiles,
          'quotations': remoteQuotations,
        },
        'sync_needed': {
          'series': localSeries.length != remoteSeries,
          'profiles': localProfiles.length != remoteProfiles,
          'quotations': localQuotations.length != remoteQuotations,
        },
        'timestamp': DateTime.now().toIso8601String(),
      };

    } catch (e) {
      return {
        'success': false,
        'error': 'خطأ في الحصول على الإحصائيات: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
