enum WindowDoorType {
  hinge('مفصلي', 'Hinge'),
  sliding('سحاب', 'Sliding');

  const WindowDoorType(this.arabicName, this.englishName);
  final String arabicName;
  final String englishName;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

enum SashCount {
  one('ضلفة واحدة', 'One Sash', 1),
  two('ضلفتين', 'Two Sashes', 2),
  three('ثلاث ضلف', 'Three Sashes', 3),
  four('أربع ضلف', 'Four Sashes', 4),
  six('6 ضلف', 'Six Sashes', 6);

  const SashCount(this.arabicName, this.englishName, this.count);
  final String arabicName;
  final String englishName;
  final int count;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }

  static List<SashCount> getHingeOptions() {
    return [one, two];
  }

  static List<SashCount> getSlidingOptions() {
    return [two, three, four, six];
  }
}

enum TrackCount {
  one('سكة واحدة', 'One Track', 1),
  two('سكتين', 'Two Tracks', 2);

  const TrackCount(this.arabicName, this.englishName, this.count);
  final String arabicName;
  final String englishName;
  final int count;

  String getLocalizedName(String languageCode) {
    return languageCode == 'ar' ? arabicName : englishName;
  }
}

class UpvcQuotationItem {
  final int? id;
  final int quotationId;
  final WindowDoorType type;
  final SashCount sashCount; // عدد الضلف
  final TrackCount? trackCount; // عدد السكك (للسحاب فقط)
  final double width;
  final double height;
  final int quantity;
  final String notes;
  final DateTime createdAt;

  UpvcQuotationItem({
    this.id,
    required this.quotationId,
    required this.type,
    this.sashCount = SashCount.one, // افتراضي ضلفة واحدة
    this.trackCount, // للسحاب فقط
    required this.width,
    required this.height,
    required this.quantity,
    this.notes = '',
    required this.createdAt,
  });

  double get area => width * height;
  double get totalArea => area * quantity;
  double get perimeter => 2 * (width + height);
  double get totalPerimeter => perimeter * quantity;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quotation_id': quotationId,
      'type': type.name,
      'sash_count': sashCount.name,
      'track_count': trackCount?.name,
      'width': width,
      'height': height,
      'quantity': quantity,
      'notes': notes,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory UpvcQuotationItem.fromMap(Map<String, dynamic> map) {
    return UpvcQuotationItem(
      id: map['id']?.toInt(),
      quotationId: map['quotation_id']?.toInt() ?? 0,
      type: WindowDoorType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => WindowDoorType.hinge,
      ),
      sashCount: SashCount.values.firstWhere(
        (s) => s.name == map['sash_count'],
        orElse: () => SashCount.one,
      ),
      trackCount: map['track_count'] != null
          ? TrackCount.values.firstWhere(
              (t) => t.name == map['track_count'],
              orElse: () => TrackCount.one,
            )
          : null,
      width: (map['width'] ?? 0).toDouble(),
      height: (map['height'] ?? 0).toDouble(),
      quantity: map['quantity']?.toInt() ?? 1,
      notes: map['notes'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  UpvcQuotationItem copyWith({
    int? id,
    int? quotationId,
    WindowDoorType? type,
    SashCount? sashCount,
    TrackCount? trackCount,
    double? width,
    double? height,
    int? quantity,
    String? notes,
    DateTime? createdAt,
  }) {
    return UpvcQuotationItem(
      id: id ?? this.id,
      quotationId: quotationId ?? this.quotationId,
      type: type ?? this.type,
      sashCount: sashCount ?? this.sashCount,
      trackCount: trackCount ?? this.trackCount,
      width: width ?? this.width,
      height: height ?? this.height,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'UpvcQuotationItem{id: $id, type: $type, width: $width, height: $height, quantity: $quantity}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UpvcQuotationItem &&
        other.id == id &&
        other.quotationId == quotationId &&
        other.type == type &&
        other.width == width &&
        other.height == height &&
        other.quantity == quantity;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        quotationId.hashCode ^
        type.hashCode ^
        width.hashCode ^
        height.hashCode ^
        quantity.hashCode;
  }
}
