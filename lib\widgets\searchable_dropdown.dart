import 'package:flutter/material.dart';

class SearchableDropdown extends StatefulWidget {
  final String? value;
  final List<String> items;
  final String hintText;
  final String labelText;
  final IconData? prefixIcon;
  final Function(String?) onChanged;
  final String? Function(String?)? validator;
  final bool allowCustomInput;

  const SearchableDropdown({
    super.key,
    this.value,
    required this.items,
    required this.hintText,
    required this.labelText,
    this.prefixIcon,
    required this.onChanged,
    this.validator,
    this.allowCustomInput = true,
  });

  @override
  State<SearchableDropdown> createState() => _SearchableDropdownState();
}

class _SearchableDropdownState extends State<SearchableDropdown> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<String> _filteredItems = [];
  bool _isDropdownOpen = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.value ?? '';
    _filteredItems = widget.items;
    _focusNode.addListener(_onFocusChange);
    debugPrint('SearchableDropdown initialized with ${widget.items.length} items: ${widget.items}'); // Debug
  }

  @override
  void didUpdateWidget(SearchableDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.items != widget.items) {
      setState(() {
        _filteredItems = widget.items;
      });
      debugPrint('SearchableDropdown updated with ${widget.items.length} items: ${widget.items}'); // Debug
    }
    if (oldWidget.value != widget.value) {
      setState(() {
        _controller.text = widget.value ?? '';
      });
      debugPrint('SearchableDropdown value updated to: ${widget.value}'); // Debug
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      _showDropdown();
    } else {
      _hideDropdown();
    }
  }

  void _filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items
            .where((item) => item.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
    _updateOverlay();
  }

  void _showDropdown() {
    if (_isDropdownOpen) return;

    _isDropdownOpen = true;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    if (!_isDropdownOpen) return;

    _removeOverlay();
    _isDropdownOpen = false;
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_isDropdownOpen) {
      _removeOverlay();
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 5.0),
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _filteredItems.isEmpty
                  ? const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'لا توجد نتائج',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return _DropdownItem(
                          item: item,
                          prefixIcon: widget.prefixIcon,
                          isLast: index == _filteredItems.length - 1,
                          onTap: () {
                            debugPrint('Item tapped: $item'); // Debug
                            _selectItem(item);
                          },
                        );
                      },
                    ),
            ),
          ),
        ),
      ),
    );
  }

  void _selectItem(String item) {
    debugPrint('Selecting item: $item'); // Debug
    setState(() {
      _controller.text = item;
    });
    widget.onChanged(item);

    // Add small delay before hiding dropdown to ensure selection is processed
    Future.delayed(const Duration(milliseconds: 100), () {
      _hideDropdown();
      _focusNode.unfocus();
    });

    debugPrint('Item selected and controller updated: ${_controller.text}'); // Debug
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
          suffixIcon: Icon(
            _isDropdownOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: Colors.grey[600],
          ),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        validator: widget.validator,
        onChanged: (value) {
          _filterItems(value);
          if (widget.allowCustomInput) {
            widget.onChanged(value);
          }
        },
        onTap: () {
          if (!_isDropdownOpen) {
            _showDropdown();
          }
        },
      ),
    );
  }
}

class _DropdownItem extends StatefulWidget {
  final String item;
  final IconData? prefixIcon;
  final bool isLast;
  final VoidCallback onTap;

  const _DropdownItem({
    required this.item,
    this.prefixIcon,
    required this.isLast,
    required this.onTap,
  });

  @override
  State<_DropdownItem> createState() => _DropdownItemState();
}

class _DropdownItemState extends State<_DropdownItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          decoration: BoxDecoration(
            color: _isHovered ? Colors.grey[100] : Colors.transparent,
            border: !widget.isLast
                ? Border(
                    bottom: BorderSide(
                      color: Colors.grey[200]!,
                      width: 1,
                    ),
                  )
                : null,
          ),
          child: Row(
            children: [
              Icon(
                widget.prefixIcon ?? Icons.person,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.item,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
