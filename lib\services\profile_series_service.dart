import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/profile_series.dart';
import '../models/aluminum_profile.dart';

class ProfileSeriesService {
  static Database? _database;
  static const String _seriesTable = 'profile_series';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'profile_series.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $_seriesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        description TEXT,
        image_path TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Create indexes
    await db.execute('CREATE INDEX idx_series_type ON $_seriesTable (type)');
    await db.execute('CREATE INDEX idx_series_code ON $_seriesTable (code)');
    await db.execute('CREATE INDEX idx_series_active ON $_seriesTable (is_active)');
  }

  // Series CRUD operations
  Future<int> insertSeries(ProfileSeries series) async {
    final db = await database;
    return await db.insert(_seriesTable, series.toMap());
  }

  Future<List<ProfileSeries>> getAllSeries() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _seriesTable,
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'name',
    );
    return List.generate(maps.length, (i) => ProfileSeries.fromMap(maps[i]));
  }

  Future<List<ProfileSeries>> getSeriesByType(ProfileType type) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _seriesTable,
      where: 'type = ? AND is_active = ?',
      whereArgs: [type.key, 1],
      orderBy: 'name',
    );
    return List.generate(maps.length, (i) => ProfileSeries.fromMap(maps[i]));
  }

  Future<ProfileSeries?> getSeriesById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _seriesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return ProfileSeries.fromMap(maps.first);
    }
    return null;
  }

  Future<ProfileSeries?> getSeriesByCode(String code) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _seriesTable,
      where: 'code = ?',
      whereArgs: [code],
    );
    if (maps.isNotEmpty) {
      return ProfileSeries.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateSeries(ProfileSeries series) async {
    final db = await database;
    return await db.update(
      _seriesTable,
      series.toMap(),
      where: 'id = ?',
      whereArgs: [series.id],
    );
  }

  Future<int> deleteSeries(int id) async {
    final db = await database;
    // Soft delete - mark as inactive
    return await db.update(
      _seriesTable,
      {'is_active': 0, 'updated_at': DateTime.now().millisecondsSinceEpoch},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<ProfileSeries>> searchSeries(String query, {ProfileType? type}) async {
    final db = await database;
    String whereClause = 'is_active = 1 AND (name LIKE ? OR code LIKE ? OR description LIKE ?)';
    List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

    if (type != null) {
      whereClause += ' AND type = ?';
      whereArgs.add(type.key);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      _seriesTable,
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'name',
    );
    return List.generate(maps.length, (i) => ProfileSeries.fromMap(maps[i]));
  }

  // Utility methods
  Future<String> generateSeriesCode(ProfileType type) async {
    final typePrefix = type.key.substring(0, 1).toUpperCase();

    final db = await database;
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count FROM $_seriesTable
      WHERE code LIKE ?
    ''', ['$typePrefix%']);

    final count = result.first['count'] as int;
    final sequence = (count + 1).toString().padLeft(3, '0');

    return '$typePrefix$sequence';
  }

  Future<void> insertSampleData() async {
    final db = await database;

    // Check if sample data already exists
    final count = await db.rawQuery('SELECT COUNT(*) as count FROM $_seriesTable');
    if ((count.first['count'] as int) > 0) return;

    final now = DateTime.now();

    // Sample hinge series
    final hingeSeries = [
      ProfileSeries(
        name: 'سوناتا 45',
        code: 'SON45',
        type: ProfileType.hinge,
        description: 'مجموعة قطاعات مفصلي سوناتا 45 مم',
        createdAt: now,
        updatedAt: now,
      ),
      ProfileSeries(
        name: 'كلاسيك 60',
        code: 'CLS60',
        type: ProfileType.hinge,
        description: 'مجموعة قطاعات مفصلي كلاسيك 60 مم',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    // Sample sliding series
    final slidingSeries = [
      ProfileSeries(
        name: 'سلايدر 80',
        code: 'SLD80',
        type: ProfileType.sliding,
        description: 'مجموعة قطاعات سحاب سلايدر 80 مم',
        createdAt: now,
        updatedAt: now,
      ),
      ProfileSeries(
        name: 'ماكس 100',
        code: 'MAX100',
        type: ProfileType.sliding,
        description: 'مجموعة قطاعات سحاب ماكس 100 مم',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    // Insert sample data
    for (var series in [...hingeSeries, ...slidingSeries]) {
      await db.insert(_seriesTable, series.toMap());
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // دالة لحذف قاعدة البيانات وإعادة إنشائها
  Future<void> resetDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }

    String path = join(await getDatabasesPath(), 'profile_series.db');
    await deleteDatabase(path);

    // إعادة تهيئة قاعدة البيانات
    await database;
  }
}
