import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/device_info_service.dart';
import '../services/license_service.dart';

class LicenseActivationScreen extends StatefulWidget {
  final String currentDeviceSerial;
  final String? databaseSerial;
  final String message;

  const LicenseActivationScreen({
    super.key,
    required this.currentDeviceSerial,
    this.databaseSerial,
    required this.message,
  });

  @override
  State<LicenseActivationScreen> createState() => _LicenseActivationScreenState();
}

class _LicenseActivationScreenState extends State<LicenseActivationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _copySerialToClipboard() async {
    await Clipboard.setData(ClipboardData(text: widget.currentDeviceSerial));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('تم نسخ السيريال إلى الحافظة'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Future<void> _retryLicenseCheck() async {
    setState(() {
      _isRetrying = true;
    });

    try {
      final result = await LicenseService.checkLicense();
      
      if (mounted) {
        if (result['success'] == true && result['is_current_device'] == true) {
          // السيريال متطابق الآن، الانتقال إلى الشاشة الرئيسية
          Navigator.pushReplacementNamed(context, '/home');
        } else {
          // لا يزال غير متطابق
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.white),
                  SizedBox(width: 8),
                  Text('لا يزال السيريال غير مسجل في النظام'),
                ],
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('خطأ في فحص الترخيص: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFE3F2FD),
              Color(0xFFF5F5F5),
            ],
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value,
                child: Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        
                        // أيقونة التحذير
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.orange.shade100,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.orange.shade300,
                              width: 3,
                            ),
                          ),
                          child: Icon(
                            Icons.security,
                            size: 50,
                            color: Colors.orange.shade700,
                          ),
                        ),
                        
                        const SizedBox(height: 30),
                        
                        // عنوان الشاشة
                        Text(
                          'تفعيل ترخيص التطبيق',
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // رسالة التوضيح
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.orange.shade200,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            widget.message,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.orange.shade800,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        
                        const SizedBox(height: 30),
                        
                        // معلومات الجهاز الحالي
                        _buildInfoCard(
                          title: 'معلومات الجهاز الحالي',
                          icon: Icons.computer,
                          color: Colors.blue,
                          children: [
                            _buildInfoRow('المنصة', DeviceInfoService.devicePlatform),
                            _buildInfoRow('اسم الجهاز', DeviceInfoService.deviceName ?? 'غير متوفر'),
                            _buildInfoRow('سيريال الجهاز', widget.currentDeviceSerial),
                          ],
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // معلومات قاعدة البيانات (فقط إذا كان الجهاز مسجل)
                        if (widget.databaseSerial != null && widget.databaseSerial!.isNotEmpty)
                          _buildInfoCard(
                            title: 'السيريال المسجل في النظام',
                            icon: Icons.storage,
                            color: Colors.green,
                            children: [
                              _buildInfoRow('السيريال المسجل', widget.databaseSerial!),
                            ],
                          ),
                        
                        const SizedBox(height: 30),
                        
                        // أزرار العمليات
                        Column(
                          children: [
                            // زر نسخ السيريال
                            SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton.icon(
                                onPressed: _copySerialToClipboard,
                                icon: const Icon(Icons.copy, size: 24),
                                label: const Text(
                                  'نسخ سيريال الجهاز',
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 3,
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // زر تشخيص السيريال (للمطورين فقط)
                            if (LicenseService.isDevValue == 1)
                              SizedBox(
                                width: double.infinity,
                                height: 48,
                                child: OutlinedButton.icon(
                                  onPressed: _showSerialDiagnostics,
                                  icon: const Icon(Icons.bug_report, size: 20),
                                  label: const Text(
                                    'تشخيص السيريال (مطور)',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: Colors.orange,
                                    side: const BorderSide(color: Colors.orange),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ),

                            if (LicenseService.isDevValue == 1)
                              const SizedBox(height: 16),
                            
                            // زر إعادة فحص الترخيص
                            SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton.icon(
                                onPressed: _isRetrying ? null : _retryLicenseCheck,
                                icon: _isRetrying 
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : const Icon(Icons.refresh, size: 24),
                                label: Text(
                                  _isRetrying ? 'جاري فحص الترخيص...' : 'إعادة فحص الترخيص',
                                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 3,
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 30),
                        
                        // تعليمات للمستخدم
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.blue.shade200,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // عنوان خطوات التفعيل
                              Row(
                                children: [
                                  Icon(Icons.list_alt, color: Colors.blue.shade700, size: 24),
                                  const SizedBox(width: 8),
                                  Text(
                                    'خطوات التفعيل:',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // الخطوات
                              _buildStepItem(
                                icon: Icons.copy,
                                stepNumber: '1',
                                text: 'انسخ سيريال الجهاز بواسطة الزر أعلاه.',
                                color: Colors.blue.shade700,
                              ),
                              const SizedBox(height: 12),
                              _buildStepItem(
                                icon: Icons.support_agent,
                                stepNumber: '2',
                                text: 'تواصل مع الدعم الفني وإرسال السيريال.',
                                color: Colors.blue.shade700,
                              ),
                              const SizedBox(height: 12),
                              _buildStepItem(
                                icon: Icons.refresh,
                                stepNumber: '3',
                                text: 'بعد التأكد من التفعيل اضغط على إعادة فحص الترخيص أو قم بإغلاق التطبيق وإعد تشغيله مرة أخرى.',
                                color: Colors.blue.shade700,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // تفاصيل الدعم الفني
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.green.shade200,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // عنوان تفاصيل الدعم الفني
                              Row(
                                children: [
                                  Icon(Icons.contact_support, color: Colors.green.shade700, size: 24),
                                  const SizedBox(width: 8),
                                  Text(
                                    'تفاصيل الدعم الفني:',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              // تفاصيل الدعم
                              _buildContactItem(
                                icon: Icons.person,
                                label: 'مطور التطبيق',
                                value: 'إسلام عبد الفتاح',
                                color: Colors.green.shade700,
                              ),
                              const SizedBox(height: 12),
                              _buildContactItem(
                                icon: Icons.phone,
                                label: 'رقم الموبايل',
                                value: '01280698569',
                                color: Colors.green.shade700,
                                isClickable: true,
                              ),
                              const SizedBox(height: 12),
                              _buildContactItem(
                                icon: Icons.phone_android,
                                label: 'من خارج مصر',
                                value: '00201280698569',
                                color: Colors.green.shade700,
                                isClickable: true,
                              ),
                              const SizedBox(height: 12),
                              _buildContactItem(
                                icon: Icons.business,
                                label: 'اسم الشركة',
                                value: 'UptimeCode',
                                color: Colors.green.shade700,
                              ),
                              const SizedBox(height: 12),
                              _buildContactItem(
                                icon: Icons.language,
                                label: 'الموقع الإلكتروني',
                                value: 'www.uptimecode.com',
                                color: Colors.green.shade700,
                                isClickable: true,
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepItem({
    required IconData icon,
    required String stepNumber,
    required String text,
    required Color color,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // رقم الخطوة مع الأيقونة
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color, width: 2),
          ),
          child: Center(
            child: Text(
              stepNumber,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // الأيقونة
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 18),
        ),
        const SizedBox(width: 12),
        // النص
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: color,
                height: 1.4,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isClickable = false,
  }) {
    return GestureDetector(
      onTap: isClickable ? () => _handleContactTap(label, value) : null,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // الأيقونة
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            // التسمية والقيمة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: color,
                      decoration: isClickable ? TextDecoration.underline : null,
                    ),
                  ),
                ],
              ),
            ),
            // أيقونة النسخ أو الاتصال
            if (isClickable)
              Icon(
                label.contains('رقم') ? Icons.phone : Icons.copy,
                color: color.withValues(alpha: 0.7),
                size: 18,
              ),
          ],
        ),
      ),
    );
  }

  void _handleContactTap(String label, String value) {
    if (label.contains('رقم')) {
      // محاولة الاتصال
      _showContactDialog(value);
    } else {
      // نسخ القيمة
      Clipboard.setData(ClipboardData(text: value));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('تم نسخ $label إلى الحافظة'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  void _showContactDialog(String phoneNumber) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.phone, color: Colors.green),
              SizedBox(width: 8),
              Text('الاتصال بالدعم الفني'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('هل تريد الاتصال بالرقم:'),
              const SizedBox(height: 8),
              Text(
                phoneNumber,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                Clipboard.setData(ClipboardData(text: phoneNumber));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.white),
                        SizedBox(width: 8),
                        Text('تم نسخ رقم الهاتف إلى الحافظة'),
                      ],
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              icon: const Icon(Icons.copy),
              label: const Text('نسخ الرقم'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  /// عرض نافذة تشخيص السيريال
  void _showSerialDiagnostics() {
    // طباعة التشخيص في الكونسول
    DeviceInfoService.printSerialDiagnostics();

    // الحصول على معلومات السيريال
    final serialInfo = DeviceInfoService.getSerialInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.bug_report,
              color: Colors.orange,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'تشخيص سيريال الجهاز',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'المعلومات الأساسية',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildDiagnosticRow('المنصة', serialInfo['platform'] ?? 'غير معروف'),
                    _buildDiagnosticRow('السيريال المستخدم', serialInfo['serial'] ?? 'غير متوفر'),
                    _buildDiagnosticRow('مصدر السيريال', serialInfo['source'] ?? 'غير معروف'),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // المعرفات المتاحة
              if (serialInfo['available_ids'] != null && (serialInfo['available_ids'] as List).isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.list, color: Colors.green[700], size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'المعرفات المتاحة',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      ...((serialInfo['available_ids'] as List).map((id) =>
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Text(
                            '• $id',
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.green[700],
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      )),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              // رسائل تحذيرية
              if (serialInfo['error'] != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'خطأ',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        serialInfo['error'],
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              // نصائح
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb_outline, color: Colors.orange[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'نصائح للمطورين',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• تحقق من الكونسول لمزيد من التفاصيل\n'
                      '• في الأندرويد 10+، قد لا يكون Serial Number متاحاً\n'
                      '• Android ID هو البديل الأفضل للأندرويد\n'
                      '• تأكد من صلاحيات التطبيق إذا لزم الأمر',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () async {
              await Clipboard.setData(ClipboardData(text: serialInfo.toString()));
              if (context.mounted) {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم نسخ معلومات التشخيص'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('نسخ التشخيص'),
          ),
        ],
      ),
    );
  }

  Widget _buildDiagnosticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
