import 'package:flutter/material.dart';
import '../../models/panel_cutting_project.dart';
import '../../models/panel_cutting_models.dart';

class PanelCuttingOptimizationScreen extends StatefulWidget {
  final PanelCuttingProject project;

  const PanelCuttingOptimizationScreen({super.key, required this.project});

  @override
  State<PanelCuttingOptimizationScreen> createState() => _PanelCuttingOptimizationScreenState();
}

class _PanelCuttingOptimizationScreenState extends State<PanelCuttingOptimizationScreen> {
  List<OptimizedPanel> _optimizedPanels = [];
  double _wastePercentage = 0.0;
  bool _isOptimizing = false;
  bool _allowRotation = true;
  double _cuttingKerf = 3.0; // سماكة المنشار

  @override
  void initState() {
    super.initState();
    _optimizePanels();
  }

  Future<void> _optimizePanels() async {
    setState(() {
      _isOptimizing = true;
    });

    // محاكاة وقت المعالجة
    await Future.delayed(const Duration(milliseconds: 500));

    final optimizer = PanelOptimizer(
      panelWidth: widget.project.panelWidth,
      panelHeight: widget.project.panelHeight,
      allowRotation: _allowRotation,
      cuttingKerf: _cuttingKerf,
    );

    final optimizedResult = optimizer.optimize(widget.project.orderItems);

    setState(() {
      _optimizedPanels = optimizedResult.panels;
      _wastePercentage = optimizedResult.wastePercentage;
      _isOptimizing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تحسين تقطيع الألواح',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            onPressed: _exportOptimization,
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير PDF',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildOptionsCard(),
          _buildStatisticsCard(),
          Expanded(
            child: _isOptimizing
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Colors.orange),
                      SizedBox(height: 16),
                      Text('جاري تحسين التقطيع...'),
                    ],
                  ),
                )
              : _buildOptimizedPanelsList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _optimizePanels,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        tooltip: 'إعادة تحسين',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildOptionsCard() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'خيارات التحسين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: SwitchListTile(
                    title: const Text('السماح بالدوران'),
                    subtitle: const Text('دوران القطع لتحسين الاستفادة'),
                    value: _allowRotation,
                    onChanged: (value) {
                      setState(() {
                        _allowRotation = value;
                      });
                      _optimizePanels();
                    },
                    activeColor: Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'سماكة المنشار (مم)',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Slider(
                        value: _cuttingKerf,
                        min: 0.0,
                        max: 10.0,
                        divisions: 20,
                        label: '${_cuttingKerf.toStringAsFixed(1)} مم',
                        onChanged: (value) {
                          setState(() {
                            _cuttingKerf = value;
                          });
                        },
                        onChangeEnd: (value) {
                          _optimizePanels();
                        },
                        activeColor: Colors.orange,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final totalPanelsUsed = _optimizedPanels.length;
    final totalOriginalArea = widget.project.orderItems.fold<double>(
      0, (sum, item) => sum + item.totalArea,
    ) / 10000;
    final totalPanelArea = totalPanelsUsed * (widget.project.panelWidth * widget.project.panelHeight / 10000);
    final efficiency = totalOriginalArea / totalPanelArea * 100;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'نتائج التحسين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatTile(
                    'عدد الألواح',
                    totalPanelsUsed.toString(),
                    Icons.dashboard,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatTile(
                    'كفاءة الاستخدام',
                    '${efficiency.toStringAsFixed(1)}%',
                    Icons.speed,
                    efficiency > 80 ? Colors.green : efficiency > 60 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatTile(
                    'الهدر',
                    '${_wastePercentage.toStringAsFixed(1)}%',
                    Icons.delete_outline,
                    _wastePercentage < 20 ? Colors.green : _wastePercentage < 40 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatTile(
                    'المساحة الكلية',
                    '${totalPanelArea.toStringAsFixed(2)} م²',
                    Icons.square_foot,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  Widget _buildStatTile(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 11, color: color.withValues(alpha: 0.3)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizedPanelsList() {
    if (_optimizedPanels.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا يمكن ترتيب القطع في الألواح المتاحة',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _optimizedPanels.length,
      itemBuilder: (context, index) => _buildPanelCard(_optimizedPanels[index], index + 1),
    );
  }

  Widget _buildPanelCard(OptimizedPanel panel, int panelNumber) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'لوح رقم $panelNumber',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.orange.shade300),
                  ),
                  child: Text(
                    '${panel.rectangles.length} قطعة',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض تخطيط اللوح
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomPaint(
                painter: PanelLayoutPainter(
                  panel: panel,
                  panelWidth: widget.project.panelWidth,
                  panelHeight: widget.project.panelHeight,
                ),
                size: const Size(double.infinity, 200),
              ),
            ),

            const SizedBox(height: 16),

            // قائمة القطع في هذا اللوح
            const Text(
              'القطع في هذا اللوح:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: panel.rectangles.map((rect) {
                final item = widget.project.orderItems.firstWhere(
                  (item) => item.id == rect.itemId,
                );
                return Container(
                  padding: const EdgeInsets.all(8),                  decoration: BoxDecoration(
                    color: _getItemColor(rect.itemId).withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: _getItemColor(rect.itemId).withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        item.label,
                        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        '${rect.width.toInt()}×${rect.height.toInt()}',
                        style: const TextStyle(fontSize: 10),
                      ),
                      if (rect.isRotated)
                        const Icon(
                          Icons.rotate_right,
                          size: 12,
                          color: Colors.red,
                        ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Color _getItemColor(String itemId) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
    ];
    return colors[itemId.hashCode % colors.length];
  }

  void _exportOptimization() {
    // TODO: تنفيذ تصدير PDF للنتائج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ تصدير PDF قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

// خوارزمية تحسين تقطيع الألواح
class PanelOptimizer {
  final double panelWidth;
  final double panelHeight;
  final bool allowRotation;
  final double cuttingKerf;

  PanelOptimizer({
    required this.panelWidth,
    required this.panelHeight,
    this.allowRotation = true,
    this.cuttingKerf = 3.0,
  });

  OptimizationResult optimize(List<dynamic> orderItems) {
    // تحويل القطع إلى مستطيلات مع إضافة سماكة المنشار
    List<PanelRectangle> rectangles = [];

    for (var item in orderItems) {
      for (int i = 0; i < item.quantity; i++) {
        rectangles.add(PanelRectangle(
          id: '${item.id}_$i',
          itemId: item.id,
          width: item.width + cuttingKerf,
          height: item.height + cuttingKerf,
        ));
      }
    }

    // ترتيب القطع حسب المساحة (من الأكبر للأصغر)
    rectangles.sort((a, b) => (b.width * b.height).compareTo(a.width * a.height));

    List<OptimizedPanel> panels = [];
    List<PanelRectangle> remainingRectangles = List.from(rectangles);

    while (remainingRectangles.isNotEmpty) {
      final panel = _packPanel(remainingRectangles);
      panels.add(panel);

      // إزالة القطع المستخدمة
      for (final rect in panel.rectangles) {
        remainingRectangles.removeWhere((r) => r.id == rect.id);
      }
    }

    final wastePercentage = _calculateWastePercentage(panels, rectangles);

    return OptimizationResult(
      panels: panels,
      wastePercentage: wastePercentage,
    );
  }

  OptimizedPanel _packPanel(List<PanelRectangle> availableRectangles) {
    List<PanelRectangle> packedRectangles = [];
    List<_FreeSpace> freeSpaces = [
      _FreeSpace(0, 0, panelWidth, panelHeight),
    ];

    for (final rect in availableRectangles) {
      bool placed = false;

      // محاولة وضع القطعة في المساحات الفارغة
      for (int i = 0; i < freeSpaces.length && !placed; i++) {
        final space = freeSpaces[i];

        // محاولة الوضع العادي
        if (rect.width <= space.width && rect.height <= space.height) {
          _placeRectangle(rect, space.x, space.y, false, packedRectangles, freeSpaces, i);
          placed = true;
        }
        // محاولة الوضع مع الدوران (إذا كان مسموحاً)
        else if (allowRotation && rect.height <= space.width && rect.width <= space.height) {
          _placeRectangle(rect, space.x, space.y, true, packedRectangles, freeSpaces, i);
          placed = true;
        }
      }

      if (!placed) {
        break; // لا يمكن وضع المزيد من القطع في هذا اللوح
      }
    }

    // حساب المعايير المطلوبة للوح المحسن
    final totalPanelArea = panelWidth * panelHeight;
    final usedArea = packedRectangles.fold(0.0, (sum, rect) => sum + (rect.width * rect.height));
    final wasteArea = totalPanelArea - usedArea;
    final efficiency = totalPanelArea > 0 ? (usedArea / totalPanelArea) * 100 : 0.0;

    return OptimizedPanel(
      panelWidth: panelWidth,
      panelHeight: panelHeight,
      placedItems: packedRectangles,
      wasteAreas: [], // يمكن تطوير هذا لاحقاً لحساب المناطق المهدرة الفعلية
      efficiency: efficiency,
      usedArea: usedArea,
      wasteArea: wasteArea,
    );
  }

  void _placeRectangle(
    PanelRectangle rect,
    double x,
    double y,
    bool rotate,
    List<PanelRectangle> packedRectangles,
    List<_FreeSpace> freeSpaces,
    int spaceIndex,
  ) {
    final width = rotate ? rect.height : rect.width;
    final height = rotate ? rect.width : rect.height;

    // إضافة القطعة المُوضَعة
    packedRectangles.add(PanelRectangle(
      id: rect.id,
      itemId: rect.itemId,
      width: width,
      height: height,
      x: x,
      y: y,
      isRotated: rotate,
    ));

    // تحديث المساحات الفارغة
    final usedSpace = freeSpaces.removeAt(spaceIndex);

    // إضافة مساحات فارغة جديدة حول القطعة الموضوعة
    if (x + width < usedSpace.x + usedSpace.width) {
      freeSpaces.add(_FreeSpace(
        x + width,
        y,
        usedSpace.x + usedSpace.width - (x + width),
        height,
      ));
    }

    if (y + height < usedSpace.y + usedSpace.height) {
      freeSpaces.add(_FreeSpace(
        usedSpace.x,
        y + height,
        usedSpace.width,
        usedSpace.y + usedSpace.height - (y + height),
      ));
    }

    // ترتيب المساحات الفارغة حسب الأولوية
    freeSpaces.sort((a, b) {
      final areaA = a.width * a.height;
      final areaB = b.width * b.height;
      return areaB.compareTo(areaA);
    });
  }

  double _calculateWastePercentage(List<OptimizedPanel> panels, List<PanelRectangle> allRectangles) {
    final totalPanelArea = panels.length * panelWidth * panelHeight;
    final totalUsedArea = allRectangles.fold<double>(
      0, (sum, rect) => sum + (rect.width * rect.height),
    );

    return ((totalPanelArea - totalUsedArea) / totalPanelArea) * 100;
  }
}

class _FreeSpace {
  final double x, y, width, height;

  _FreeSpace(this.x, this.y, this.width, this.height);
}

class OptimizationResult {
  final List<OptimizedPanel> panels;
  final double wastePercentage;

  OptimizationResult({
    required this.panels,
    required this.wastePercentage,
  });
}

// رسام تخطيط اللوح
class PanelLayoutPainter extends CustomPainter {
  final OptimizedPanel panel;
  final double panelWidth;
  final double panelHeight;

  PanelLayoutPainter({
    required this.panel,
    required this.panelWidth,
    required this.panelHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم حدود اللوح
    paint.color = Colors.black;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );

    // رسم القطع
    final scaleX = size.width / panelWidth;
    final scaleY = size.height / panelHeight;

    final colors = [
      Colors.blue,
      Colors.green,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
      Colors.amber,
    ];

    for (int i = 0; i < panel.rectangles.length; i++) {
      final rect = panel.rectangles[i];
      final color = colors[i % colors.length];

      // رسم القطعة
      paint
        ..color = color
        ..style = PaintingStyle.fill;

      canvas.drawRect(
        Rect.fromLTWH(
          rect.x * scaleX,
          rect.y * scaleY,
          rect.width * scaleX,
          rect.height * scaleY,
        ),
        paint,
      );

      // رسم حدود القطعة
      paint
        ..color = Colors.black
        ..style = PaintingStyle.stroke;

      canvas.drawRect(
        Rect.fromLTWH(
          rect.x * scaleX,
          rect.y * scaleY,
          rect.width * scaleX,
          rect.height * scaleY,
        ),
        paint,
      );

      // رسم أيقونة الدوران إذا كانت القطعة مُدوَّرة
      if (rect.isRotated) {
        final textPainter = TextPainter(
          text: const TextSpan(
            text: '↻',
            style: TextStyle(
              color: Colors.red,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            rect.x * scaleX + 2,
            rect.y * scaleY + 2,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
