import 'package:flutter/material.dart';
import '../screens/cutting/cutting_projects_screen.dart';
import '../screens/cutting/panel_cutting_projects_screen.dart';

class CuttingService extends StatefulWidget {
  const CuttingService({super.key});

  @override
  State<CuttingService> createState() => _CuttingServiceState();
}

class _CuttingServiceState extends State<CuttingService> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'خدمة التقطيع',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.straighten),
              text: 'تقطيع الأعواد',
            ),
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'تقطيع الألواح',
            ),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          CuttingProjectsScreen(),
          PanelCuttingProjectsScreen(),
        ],
      ),
    );
  }
}