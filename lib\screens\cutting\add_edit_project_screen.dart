import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../l10n/app_localizations.dart';
import '../../models/cutting_project.dart';
import '../../services/unified_cutting_service.dart';

class AddEditProjectScreen extends StatefulWidget {
  final CuttingProject? project;

  const AddEditProjectScreen({super.key, this.project});

  @override
  State<AddEditProjectScreen> createState() => _AddEditProjectScreenState();
}

class _AddEditProjectScreenState extends State<AddEditProjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final UnifiedCuttingService _databaseHelper = UnifiedCuttingService();

  late TextEditingController _projectNumberController;
  late TextEditingController _customerNameController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;
  late TextEditingController _notesController;

  DateTime _selectedDate = DateTime.now();
  DateTime _selectedDeliveryDate = DateTime.now().add(const Duration(days: 7));

  bool _isLoading = false;
  String _generatedProjectNumber = '';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    if (widget.project == null) {
      _generateProjectNumber();
    }
  }

  void _initializeControllers() {
    if (widget.project != null) {
      _projectNumberController = TextEditingController(text: widget.project!.projectNumber);
      _customerNameController = TextEditingController(text: widget.project!.customerName);
      _phoneController = TextEditingController(text: widget.project!.phone);
      _addressController = TextEditingController(text: widget.project!.address);
      _notesController = TextEditingController(text: widget.project!.notes);
      _selectedDate = widget.project!.date;
      _selectedDeliveryDate = widget.project!.deliveryDate;
    } else {
      _projectNumberController = TextEditingController();
      _customerNameController = TextEditingController();
      _phoneController = TextEditingController();
      _addressController = TextEditingController();
      _notesController = TextEditingController();
    }
  }

  Future<void> _generateProjectNumber() async {
    try {
      final projectNumber = await _databaseHelper.generateNextProjectNumber();
      setState(() {
        _generatedProjectNumber = projectNumber;
        _projectNumberController.text = projectNumber;
      });
    } catch (e) {
      // Handle error silently or show a message
      setState(() {
        _generatedProjectNumber = 'P0001';
        _projectNumberController.text = 'P0001';
      });
    }
  }

  @override
  void dispose() {
    _projectNumberController.dispose();
    _customerNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isDeliveryDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isDeliveryDate ? _selectedDeliveryDate : _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: Localizations.localeOf(context),
    );

    if (picked != null) {
      setState(() {
        if (isDeliveryDate) {
          _selectedDeliveryDate = picked;
        } else {
          _selectedDate = picked;
        }
      });
    }
  }

  Future<void> _saveProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final localizations = AppLocalizations.of(context)!;

    try {
      // Use generated project number for new projects, or existing number for updates
      String projectNumber = _projectNumberController.text.trim();
      if (widget.project == null && projectNumber.isEmpty) {
        projectNumber = _generatedProjectNumber;
      }

      final project = CuttingProject(
        id: widget.project?.id,
        projectNumber: projectNumber,
        customerName: _customerNameController.text.trim(),
        date: _selectedDate,
        deliveryDate: _selectedDeliveryDate,
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        notes: _notesController.text.trim(),
      );

      if (widget.project == null) {
        // Add new project
        await _databaseHelper.insertCuttingProject(project.toMap());
        _showSuccessSnackBar(localizations.projectSaved);
      } else {
        // Update existing project
        await _databaseHelper.updateCuttingProject(project);
        _showSuccessSnackBar(localizations.projectUpdated);
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      _showErrorSnackBar('${localizations.error}: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final dateFormat = DateFormat('dd/MM/yyyy');
    final isEditing = widget.project != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? localizations.editProject : localizations.addNewProject,
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Project Number
              TextFormField(
                controller: _projectNumberController,
                decoration: InputDecoration(
                  labelText: localizations.projectNumber,
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.numbers),
                  filled: true,
                  fillColor: Colors.grey[100],
                  suffixIcon: widget.project == null
                    ? const Icon(Icons.auto_awesome, color: Colors.green)
                    : null,
                  helperText: widget.project == null
                    ? localizations.automaticNumber
                    : null,
                ),
                readOnly: widget.project == null, // Read-only for new projects
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${localizations.pleaseEnter} ${localizations.projectNumber}';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Customer Name
              TextFormField(
                controller: _customerNameController,
                decoration: InputDecoration(
                  labelText: localizations.customerName,
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${localizations.pleaseEnter} ${localizations.customerName}';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Date
              InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: localizations.date,
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.calendar_today),
                  ),
                  child: Text(dateFormat.format(_selectedDate)),
                ),
              ),

              const SizedBox(height: 16),

              // Delivery Date
              InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: localizations.deliveryDate,
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.local_shipping),
                  ),
                  child: Text(dateFormat.format(_selectedDeliveryDate)),
                ),
              ),

              const SizedBox(height: 16),

              // Phone
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: localizations.phone,
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${localizations.pleaseEnter} ${localizations.phone}';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Address
              TextFormField(
                controller: _addressController,
                decoration: InputDecoration(
                  labelText: localizations.address,
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.location_on),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${localizations.pleaseEnter} ${localizations.address}';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: localizations.notes,
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.note),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 32),

              // Save Button
              ElevatedButton(
                onPressed: _isLoading ? null : _saveProject,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  localizations.save,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
