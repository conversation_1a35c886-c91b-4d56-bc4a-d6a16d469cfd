import 'package:flutter/material.dart';
import '../../models/panel_cutting_project.dart';
import '../../models/panel_order_item.dart';
import 'add_edit_panel_project_screen.dart';
import 'panel_cutting_optimization_screen.dart';

class PanelProjectViewScreen extends StatelessWidget {
  final PanelCuttingProject project;

  const PanelProjectViewScreen({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          project.projectName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'رجوع',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
              },
              tooltip: 'الصفحة الرئيسية',
            ),
          ],
        ),
        leadingWidth: 100,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => AddEditPanelProjectScreen(project: project),
                ),
              );
            },
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل المشروع',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'duplicate':
                  _duplicateProject(context);
                  break;
                case 'export':
                  _exportProject(context);
                  break;
                case 'delete':
                  _deleteProject(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('تكرار المشروع'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('تصدير PDF'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف المشروع', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildProjectInfoCard(),
          const SizedBox(height: 16),
          _buildPanelDimensionsCard(),
          const SizedBox(height: 16),
          _buildOrderItemsCard(),
          const SizedBox(height: 16),
          _buildStatisticsCard(),
          const SizedBox(height: 20),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PanelCuttingOptimizationScreen(project: project),
            ),
          );
        },
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.auto_fix_high),
        label: const Text('تحسين التقطيع'),
      ),
    );
  }

  Widget _buildProjectInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'معلومات المشروع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('اسم المشروع', project.projectName, Icons.work),
            _buildInfoRow('اسم العميل', project.customerName, Icons.person),
            _buildInfoRow(
              'تاريخ الإنشاء',
              '${project.createdAt.day}/${project.createdAt.month}/${project.createdAt.year}',
              Icons.calendar_today,
            ),            if (project.updatedAt != project.createdAt)
              _buildInfoRow(
                'آخر تعديل',
                '${project.updatedAt.day}/${project.updatedAt.month}/${project.updatedAt.year}',
                Icons.update,
              ),
            if (project.notes != null && project.notes!.isNotEmpty)
              _buildInfoRow('ملاحظات', project.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  Widget _buildPanelDimensionsCard() {
    final panelArea = (project.panelWidth * project.panelHeight / 10000);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.dashboard, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'مقاسات اللوح الأساسي',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      children: [
                        const Icon(Icons.width_full, color: Colors.blue),
                        const SizedBox(height: 4),
                        const Text('العرض', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('${project.panelWidth} سم', style: const TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Column(
                      children: [
                        const Icon(Icons.height, color: Colors.green),
                        const SizedBox(height: 4),
                        const Text('الارتفاع', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('${project.panelHeight} سم', style: const TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Column(
                      children: [
                        const Icon(Icons.straighten, color: Colors.orange),
                        const SizedBox(height: 4),
                        const Text('المساحة', style: TextStyle(fontWeight: FontWeight.bold)),
                        Text('${panelArea.toStringAsFixed(2)} م²', style: const TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(Icons.list_alt, color: Colors.orange),
                    SizedBox(width: 8),
                    Text(
                      'القطع المطلوبة',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                Text(
                  '${project.orderItems.length} قطعة',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: project.orderItems.length,
              itemBuilder: (context, index) => _buildOrderItemTile(project.orderItems[index], index + 1),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItemTile(PanelOrderItem item, int number) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                item.label,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getMaterialColor(item.material),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  item.material.arabicName,
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildDetailChip(
                  Icons.width_full,
                  'العرض',
                  '${item.width} سم',
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDetailChip(
                  Icons.height,
                  'الارتفاع',
                  '${item.height} سم',
                  Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDetailChip(
                  Icons.numbers,
                  'الكمية',
                  '${item.quantity}',
                  Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                const Icon(Icons.calculate, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Text(
                  'المساحة الإجمالية: ${(item.totalArea / 10000).toStringAsFixed(4)} م²',
                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  Widget _buildDetailChip(IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 2),
          Text(label, style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.3))),
          Text(value, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final totalItems = project.orderItems.fold<int>(0, (sum, item) => sum + item.quantity);
    final totalArea = project.orderItems.fold<double>(0, (sum, item) => sum + item.totalArea) / 10000;
    final panelArea = (project.panelWidth * project.panelHeight / 10000);
    final efficiency = (totalArea / panelArea * 100).clamp(0, 100);

    // إحصائيات المواد
    final materialStats = <PanelMaterial, int>{};
    for (final item in project.orderItems) {
      materialStats[item.material] = (materialStats[item.material] ?? 0) + item.quantity;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'إحصائيات المشروع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي القطع',
                    totalItems.toString(),
                    Icons.widgets,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المساحة المطلوبة',
                    '${totalArea.toStringAsFixed(2)} م²',
                    Icons.square_foot,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'مساحة اللوح',
                    '${panelArea.toStringAsFixed(2)} م²',
                    Icons.dashboard,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'كفاءة الاستخدام',
                    '${efficiency.toStringAsFixed(1)}%',
                    Icons.speed,
                    efficiency > 80 ? Colors.green : efficiency > 60 ? Colors.orange : Colors.red,
                  ),
                ),
              ],
            ),
            if (materialStats.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'توزيع المواد',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: materialStats.entries.map((entry) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getMaterialColor(entry.key),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${entry.key.arabicName}: ${entry.value}',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.3)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey, size: 20),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Color _getMaterialColor(PanelMaterial material) {
    switch (material) {
      case PanelMaterial.wood:
        return Colors.brown;
      case PanelMaterial.fiber:
        return Colors.grey;
      case PanelMaterial.glass:
        return Colors.blue;
      case PanelMaterial.mdf:
        return Colors.orange;
      case PanelMaterial.plywood:
        return Colors.green;
    }
  }

  void _duplicateProject(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddEditPanelProjectScreen(
          project: project.copyWith(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            projectName: '${project.projectName} - نسخة',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ),
      ),
    );
  }

  void _exportProject(BuildContext context) {
    // TODO: تنفيذ تصدير PDF
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ تصدير PDF قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _deleteProject(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              // TODO: حذف المشروع من قاعدة البيانات
              Navigator.of(context).pop();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المشروع بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
